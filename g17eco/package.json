{"name": "g17eco", "version": "0.0.0", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"gcp-login": "npx google-artifactregistry-auth --repo-config=$HOME/.npmrc", "clean": "turbo run clean", "clean-all": "turbo run clean && rm -r node_modules", "build": "turbo run build", "test": "turbo run test", "test:cov": "turbo run test:cov", "typecheck": "turbo run typecheck", "dev": "turbo run dev --parallel", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "publish-packages": "turbo run build lint test --filter=@g17eco/convert-units && changeset version && changeset publish", "publish-core": "turbo run build lint test --filter=@g17eco/core && changeset version && changeset publish", "utr:dev": "turbo run dev --filter=api.internal.g17.eco --filter=internal.g17.eco", "utr:typecheck": "turbo run typecheck --filter=internal.g17.eco", "staff:dev": "turbo run dev --filter=staff.g17.eco", "staff:typecheck": "turbo run typecheck --filter=staff.g17.eco", "utr:api:dev": "turbo run dev --filter=api.internal.g17.eco", "utr:www:dev": "turbo run dev --filter=internal.g17.eco", "ledger:dev": "turbo run dev --filter=ledger.microservice", "data-integration:dev": "turbo run dev --filter=data-integration.microservice", "apps:dev": "turbo run dev --filter=!api-docs", "docs:dev": "turbo run dev --filter=internal-docs", "ws:dev": "turbo run dev --filter=ws.g17.eco", "generate:pipelines": "tsx deploy/bitbucket/generate-pipelines.ts"}, "devDependencies": {"@types/flexsearch": "^0.7.6", "eslint-config-custom": "*", "prettier": "latest", "turbo": "^2.5.0"}, "overrides": {"nextra-theme-docs": {"flexsearch": "0.7.21"}}, "engines": {"node": ">=22.14.0", "npm": ">=10.9.2"}, "packageManager": "npm@10.5.0", "optionalDependencies": {"@esbuild/darwin-arm64": "^0.25.9", "@esbuild/linux-x64": "^0.25.9", "@napi-rs/simple-git-darwin-arm64-gnu": "^0.1.9", "@napi-rs/simple-git-linux-x64-gnu": "^0.1.9", "@swc/core-darwin-arm64": "^1.3.84", "@swc/core-darwin-x64": "^1.3.84", "@swc/core-linux-arm-gnueabihf": "^1.3.84", "@swc/core-linux-arm64-gnu": "^1.3.84", "@swc/core-linux-arm64-musl": "^1.3.84", "@swc/core-linux-x64-gnu": "^1.3.84", "@swc/core-linux-x64-musl": "^1.3.84"}, "dependencies": {"@changesets/cli": "^2.27.10", "yaml": "^2.6.1"}, "publishConfig": {"registry": "https://europe-west2-npm.pkg.dev/g17-eco/packages/"}}