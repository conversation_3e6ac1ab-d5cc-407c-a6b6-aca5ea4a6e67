{"name": "ws.g17.eco", "version": "1.0.0", "description": "Live collaboration websocket server", "main": "src/main.ts", "type": "module", "private": true, "scripts": {"dev": "tsx watch --env-file=.env src/main.ts", "build": "tsup src/main.ts --format cjs --out-dir dist", "test": "echo \"Error: no test specified\" && exit 1", "clean": "rm -rf .turbo node_modules dist"}, "author": "", "license": "ISC", "dependencies": {"@g17eco/logger": "*", "@hocuspocus/extension-database": "^3.2.3", "@hocuspocus/extension-logger": "^3.2.3", "@hocuspocus/extension-redis": "^3.2.3", "@hocuspocus/extension-sqlite": "^3.2.3", "@hocuspocus/server": "^3.2.3", "@hocuspocus/transformer": "^3.2.3", "@sentry/node": "^8.37.1", "axios": "^1.11.0", "express": "^5.0.1", "express-ws": "^5.0.2", "jsonwebtoken": "^9.0.2", "postgres": "^3.4.5"}, "devDependencies": {"@sentry/cli": "^2.38.2", "@types/express-ws": "^3.0.5", "eslint-config-custom": "*", "tsup": "^8.3.5", "tsx": "^4.19.2", "typescript": "*"}}