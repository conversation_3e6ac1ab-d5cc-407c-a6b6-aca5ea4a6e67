import { expect } from 'chai';
import {
  AggregationMode,
  ColumnType,
  ColumnValueAggregation,
  type TableColumn,
  UtrValueType,
  ValueAggregation,
} from '../../../../server/models/public/universalTrackerType';
import { getAggregatorByUniversalTrackerFn, isCompatibleAggregationOverride, isCompatibleColumnsAggregationOverride } from '../../../../server/service/utr/aggregation/utrTypeAggregator';
import type { UniversalTrackerPlain } from '../../../../server/models/universalTracker';

describe('utrTypeAggregator', () => {

  describe('getAggregatorByUniversalTrackerFn', () => {
    it('should return ValueAggregation.EmptyAggregator if it is set on the UTR', () => {
      const utr = {
        valueType: UtrValueType.Number,
        valueAggregation: ValueAggregation.EmptyAggregator,
      };

      const result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Children);
      const result2 = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Combined);

      expect(result).eq(ValueAggregation.EmptyAggregator);
      expect(result2).eq(ValueAggregation.EmptyAggregator);
    });

    it('should use the right compatibility list based on the aggregation mode and return the default aggregator if the one set on the UTR is not compatible', () => {
      const utr = {
        valueType: UtrValueType.Number,
        valueAggregation: ValueAggregation.ValueAverageAggregator,
      };

      const result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Children);

      expect(result).eq(ValueAggregation.ValueSumAggregator);
    });

    it('should use the right compatibility list based on the aggregation mode and return the utr aggregator if it is compatible', () => {
      const utr = {
        valueType: UtrValueType.Number,
        valueAggregation: ValueAggregation.ValueAverageAggregator,
      };

      const result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Combined);
      expect(result).eq(ValueAggregation.ValueAverageAggregator);
    });

    describe('with aggregationConfig mode-specific overrides', () => {
      it('should use children mode override when in Children aggregation mode', () => {
        const utr: Pick<UniversalTrackerPlain, 'valueType' | 'valueAggregation' | 'aggregationConfig'> = {
          valueType: UtrValueType.Number,
          valueAggregation: ValueAggregation.ValueAverageAggregator,
          aggregationConfig: {
            modes: {
              [AggregationMode.Children]: { valueAggregation: ValueAggregation.ValueSumAggregator },
              [AggregationMode.Combined]: { valueAggregation: ValueAggregation.LatestAggregator },
            },
          },
        };

        const result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Children);
        expect(result).to.equal(ValueAggregation.ValueSumAggregator);
      });

      it('should use combined mode override when in Combined aggregation mode', () => {
        const utr: Pick<UniversalTrackerPlain, 'valueType' | 'valueAggregation' | 'aggregationConfig'> = {
          valueType: UtrValueType.Number,
          valueAggregation: ValueAggregation.ValueSumAggregator,
          aggregationConfig: {
            modes: {
              [AggregationMode.Children]: { valueAggregation: ValueAggregation.ValueSumAggregator },
              [AggregationMode.Combined]: { valueAggregation: ValueAggregation.LatestAggregator },
            },
          },
        };

        const result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Combined);
        expect(result).to.equal(ValueAggregation.LatestAggregator);
      });

      it('should fall back to valueAggregation when mode-specific config is not compatible', () => {
        const utr: Pick<UniversalTrackerPlain, 'valueType' | 'valueAggregation' | 'aggregationConfig'> = {
          valueType: UtrValueType.Number,
          valueAggregation: ValueAggregation.ValueSumAggregator,
          aggregationConfig: {
            modes: {
              // TableColumnAggregator is not compatible with Number type
              [AggregationMode.Children]: { valueAggregation: ValueAggregation.TableColumnAggregator as ValueAggregation },
            },
          },
        };

        const result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Children);
        // Should fall back to valueAggregation since mode-specific is incompatible
        expect(result).to.equal(ValueAggregation.ValueSumAggregator);
      });

      it('should use mode-specific config even when valueAggregation is set', () => {
        const utr: Pick<UniversalTrackerPlain, 'valueType' | 'valueAggregation' | 'aggregationConfig'> = {
          valueType: UtrValueType.Number,
          valueAggregation: ValueAggregation.ValueAverageAggregator,
          aggregationConfig: {
            modes: {
              [AggregationMode.Combined]: { valueAggregation: ValueAggregation.LatestAggregator },
            },
          },
        };

        const result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Combined);
        // Mode-specific config takes precedence
        expect(result).to.equal(ValueAggregation.LatestAggregator);
      });
    });

    describe('backward compatibility', () => {
      it('should use valueAggregation when no aggregationConfig is present', () => {
        const utr: Pick<UniversalTrackerPlain, 'valueType' | 'valueAggregation' | 'aggregationConfig'> = {
          valueType: UtrValueType.Number,
          valueAggregation: ValueAggregation.ValueAverageAggregator,
        };

        const result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Combined);
        expect(result).to.equal(ValueAggregation.ValueAverageAggregator);
      });

      it('should use valueAggregation when aggregationConfig has empty modes', () => {
        const utr: Pick<UniversalTrackerPlain, 'valueType' | 'valueAggregation' | 'aggregationConfig'> = {
          valueType: UtrValueType.Number,
          valueAggregation: ValueAggregation.ValueSumAggregator,
          aggregationConfig: {
            modes: {},
          },
        };

        const result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Children);
        expect(result).to.equal(ValueAggregation.ValueSumAggregator);
      });

      it('should use default aggregation when neither aggregationConfig nor valueAggregation is compatible', () => {
        const utr: Pick<UniversalTrackerPlain, 'valueType' | 'valueAggregation' | 'aggregationConfig'> = {
          valueType: UtrValueType.Number,
          // TableColumnAggregator is not compatible with Number type
          valueAggregation: ValueAggregation.TableColumnAggregator as ValueAggregation,
        };

        const result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Children);
        // Should fall back to default for Number type in Children mode
        expect(result).to.equal(ValueAggregation.ValueSumAggregator);
      });

      it('should use default aggregation when no aggregation config is provided', () => {
        const utr: Pick<UniversalTrackerPlain, 'valueType' | 'valueAggregation' | 'aggregationConfig'> = {
          valueType: UtrValueType.Percentage,
        };

        const result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Children);
        // Should use default for Percentage type in Children mode
        expect(result).to.equal(ValueAggregation.ValueAverageAggregator);
      });
    });

    describe('different value types', () => {
      it('should handle Text type with mode-specific config', () => {
        const utr: Pick<UniversalTrackerPlain, 'valueType' | 'valueAggregation' | 'aggregationConfig'> = {
          valueType: UtrValueType.Text,
          valueAggregation: ValueAggregation.LatestAggregator,
          aggregationConfig: {
            modes: {
              [AggregationMode.Combined]: { valueAggregation: ValueAggregation.ValueConcatenateAggregator },
            },
          },
        };

        const result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Combined);
        expect(result).to.equal(ValueAggregation.ValueConcatenateAggregator);
      });

      it('should handle Table type with mode-specific config', () => {
        const utr: Pick<UniversalTrackerPlain, 'valueType' | 'valueAggregation' | 'aggregationConfig'> = {
          valueType: UtrValueType.Table,
          valueAggregation: ValueAggregation.TableColumnAggregator,
          aggregationConfig: {
            modes: {
              [AggregationMode.Combined]: { valueAggregation: ValueAggregation.TableRowGroupAggregator },
              [AggregationMode.Children]: { valueAggregation: ValueAggregation.TableColumnAggregator },
            },
          },
        };

        let result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Combined);
        expect(result).to.equal(ValueAggregation.TableRowGroupAggregator);

        result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Children);
        expect(result).to.equal(ValueAggregation.TableColumnAggregator);
      });

      it('should handle NumericValueList type with mode-specific config', () => {
        const utr: Pick<UniversalTrackerPlain, 'valueType' | 'valueAggregation' | 'aggregationConfig'> = {
          valueType: UtrValueType.NumericValueList,
          valueAggregation: ValueAggregation.NumericValueListSumAggregator,
          aggregationConfig: {
            modes: {
              [AggregationMode.Combined]: { valueAggregation: ValueAggregation.NumericValueListAverageAggregator },
            },
          },
        };

        const result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Combined);
        expect(result).to.equal(ValueAggregation.NumericValueListAverageAggregator);
      });
    });

    describe('edge cases', () => {
      it('should handle undefined aggregationConfig gracefully', () => {
        const utr: Pick<UniversalTrackerPlain, 'valueType' | 'valueAggregation' | 'aggregationConfig'> = {
          valueType: UtrValueType.Number,
          valueAggregation: ValueAggregation.ValueSumAggregator,
          aggregationConfig: undefined,
        };

        const result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Combined);
        expect(result).to.equal(ValueAggregation.ValueSumAggregator);
      });

      it('should handle undefined modes in aggregationConfig gracefully', () => {
        const utr: Pick<UniversalTrackerPlain, 'valueType' | 'valueAggregation' | 'aggregationConfig'> = {
          valueType: UtrValueType.Number,
          valueAggregation: ValueAggregation.ValueSumAggregator,
          aggregationConfig: {
            modes: undefined,
          },
        };

        const result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Combined);
        expect(result).to.equal(ValueAggregation.ValueSumAggregator);
      });

      it('should handle partial mode configuration', () => {
        const utr: Pick<UniversalTrackerPlain, 'valueType' | 'valueAggregation' | 'aggregationConfig'> = {
          valueType: UtrValueType.Number,
          valueAggregation: ValueAggregation.ValueSumAggregator,
          aggregationConfig: {
            modes: {
              // Only combined mode is configured with a compatible aggregation
              [AggregationMode.Combined]: { valueAggregation: ValueAggregation.ValueAverageAggregator },
            },
          },
        };

        // Should fall back to valueAggregation for children (no mode-specific config)
        let result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Children);
        expect(result).to.equal(ValueAggregation.ValueSumAggregator);

        // Should use mode-specific for combined
        result = getAggregatorByUniversalTrackerFn(utr, AggregationMode.Combined);
        expect(result).to.equal(ValueAggregation.ValueAverageAggregator);
      });
    });
  });

  describe('isCompatibleAggregationOverride', () => {
    it('should return true for compatible Number aggregations in Children mode', () => {
      const result = isCompatibleAggregationOverride({
        valueType: UtrValueType.Number,
        mode: AggregationMode.Children,
        aggregation: ValueAggregation.ValueSumAggregator,
      });
      expect(result).to.be.true;
    });

    it('should return false for incompatible Number aggregations in Children mode', () => {
      const result = isCompatibleAggregationOverride({
        valueType: UtrValueType.Number,
        mode: AggregationMode.Children,
        aggregation: ValueAggregation.TableColumnAggregator,
      });
      expect(result).to.be.false;
    });

    it('should return true for compatible Text aggregations in Combined mode', () => {
      const result = isCompatibleAggregationOverride({
        valueType: UtrValueType.Text,
        mode: AggregationMode.Combined,
        aggregation: ValueAggregation.ValueConcatenateAggregator,
      });
      expect(result).to.be.true;
    });

    it('should return false for incompatible Text aggregations in Combined mode', () => {
      const result = isCompatibleAggregationOverride({
        valueType: UtrValueType.Text,
        mode: AggregationMode.Combined,
        aggregation: ValueAggregation.ValueSumAggregator,
      });
      expect(result).to.be.false;
    });

    it('should return true for compatible Table aggregations in Children mode', () => {
      const result = isCompatibleAggregationOverride({
        valueType: UtrValueType.Table,
        mode: AggregationMode.Children,
        aggregation: ValueAggregation.TableColumnAggregator,
      });
      expect(result).to.be.true;
    });

    it('should return true for compatible Table aggregations in Combined mode', () => {
      const result = isCompatibleAggregationOverride({
        valueType: UtrValueType.Table,
        mode: AggregationMode.Combined,
        aggregation: ValueAggregation.TableRowGroupAggregator,
      });
      expect(result).to.be.true;
    });

    it('should return false for incompatible Table aggregations', () => {
      const result = isCompatibleAggregationOverride({
        valueType: UtrValueType.Table,
        mode: AggregationMode.Children,
        aggregation: ValueAggregation.ValueSumAggregator,
      });
      expect(result).to.be.false;
    });

    it('should return true for compatible NumericValueList aggregations', () => {
      const result = isCompatibleAggregationOverride({
        valueType: UtrValueType.NumericValueList,
        mode: AggregationMode.Combined,
        aggregation: ValueAggregation.NumericValueListAverageAggregator,
      });
      expect(result).to.be.true;
    });

    it('should return false for unknown value types', () => {
      const result = isCompatibleAggregationOverride({
        valueType: 'UnknownType' as UtrValueType,
        mode: AggregationMode.Children,
        aggregation: ValueAggregation.ValueSumAggregator,
      });
      expect(result).to.be.false;
    });
  });

  describe('isCompatibleColumnsAggregationOverride', () => {
    it('should return true for empty columns array', () => {
      const columns: TableColumn[] = [];
      const result = isCompatibleColumnsAggregationOverride(columns);
      expect(result).to.be.true;
    });

    it('should return true for columns with no aggregation config', () => {
      const columns = [
        { type: ColumnType.Number, name: 'col1', code: 'col1' },
        { type: ColumnType.Text, name: 'col2', code: 'col2' },
      ];
      const result = isCompatibleColumnsAggregationOverride(columns);
      expect(result).to.be.true;
    });

    it('should return true for columns with empty aggregation config modes', () => {
      const columns = [
        {
          type: ColumnType.Number,
          name: 'col1',
          code: 'col1',
          aggregationConfig: { modes: {} }
        },
      ];
      const result = isCompatibleColumnsAggregationOverride(columns);
      expect(result).to.be.true;
    });

    it('should return true for columns with compatible number aggregations', () => {
      const columns = [
        {
          type: ColumnType.Number,
          name: 'col1',
          code: 'col1',
          aggregationConfig: {
            modes: {
              [AggregationMode.Combined]: { valueAggregation: ColumnValueAggregation.ColumnSumAggregator },
              [AggregationMode.Children]: { valueAggregation: ColumnValueAggregation.ColumnSumAggregator },
            }
          }
        },
      ];
      const result = isCompatibleColumnsAggregationOverride(columns);
      expect(result).to.be.true;
    });

    it('should return true for columns with compatible text aggregations', () => {
      const columns = [
        {
          type: ColumnType.Text,
          name: 'col1',
          code: 'col1',
          aggregationConfig: {
            modes: {
              [AggregationMode.Combined]: { valueAggregation: ColumnValueAggregation.ColumnConcatenateAggregator },
            }
          }
        },
      ];
      const result = isCompatibleColumnsAggregationOverride(columns);
      expect(result).to.be.true;
    });

    it('should return false for columns with incompatible number aggregations', () => {
      const columns = [
        {
          type: ColumnType.Number,
          name: 'col1',
          code: 'col1',
          aggregationConfig: {
            modes: {
              [AggregationMode.Combined]: { valueAggregation: ColumnValueAggregation.ColumnConcatenateAggregator }, // Text aggregation on number column
            }
          }
        },
      ];
      const result = isCompatibleColumnsAggregationOverride(columns);
      expect(result).to.be.false;
    });

    it('should return false for columns with incompatible text aggregations', () => {
      const columns = [
        {
          type: ColumnType.Text,
          name: 'col1',
          code: 'col1',
          aggregationConfig: {
            modes: {
              [AggregationMode.Combined]: { valueAggregation: ColumnValueAggregation.ColumnSumAggregator }, // Number aggregation on text column
            }
          }
        },
      ];
      const result = isCompatibleColumnsAggregationOverride(columns);
      expect(result).to.be.false;
    });

    it('should return false if any column has incompatible aggregation', () => {
      const columns = [
        {
          type: ColumnType.Number,
          name: 'col1',
          code: 'col1',
          aggregationConfig: {
            modes: {
              [AggregationMode.Combined]: { valueAggregation: ColumnValueAggregation.ColumnSumAggregator }, // Compatible
            }
          }
        },
        {
          type: ColumnType.Text,
          name: 'col2',
          code: 'col2',
          aggregationConfig: {
            modes: {
              [AggregationMode.Combined]: { valueAggregation: ColumnValueAggregation.ColumnSumAggregator }, // Incompatible
            }
          }
        },
      ];
      const result = isCompatibleColumnsAggregationOverride(columns);
      expect(result).to.be.false;
    });

    it('should return true for mixed columns with all compatible aggregations', () => {
      const columns = [
        {
          type: ColumnType.Number,
          name: 'col1',
          code: 'col1',
          aggregationConfig: {
            modes: {
              [AggregationMode.Combined]: { valueAggregation: ColumnValueAggregation.ColumnSumAggregator },
            }
          }
        },
        {
          type: ColumnType.Text,
          name: 'col2',
          code: 'col2',
          aggregationConfig: {
            modes: {
              [AggregationMode.Combined]: { valueAggregation: ColumnValueAggregation.ColumnConcatenateAggregator },
            }
          }
        },
        {
          type: ColumnType.Percentage,
          name: 'col3',
          code: 'col3',
          aggregationConfig: {
            modes: {
              [AggregationMode.Children]: { valueAggregation: ColumnValueAggregation.ColumnWeightedAverageAggregator },
            }
          }
        },
      ];
      const result = isCompatibleColumnsAggregationOverride(columns);
      expect(result).to.be.true;
    });

    it('should handle columns with only Children mode aggregation', () => {
      const columns = [
        {
          type: ColumnType.Number,
          name: 'col1',
          code: 'col1',
          aggregationConfig: {
            modes: {
              [AggregationMode.Children]: { valueAggregation: ColumnValueAggregation.ColumnSumAggregator },
            }
          }
        },
      ];
      const result = isCompatibleColumnsAggregationOverride(columns);
      expect(result).to.be.true;
    });

    it('should handle columns with only Combined mode aggregation', () => {
      const columns = [
        {
          type: ColumnType.Percentage,
          name: 'col1',
          code: 'col1',
          aggregationConfig: {
            modes: {
              [AggregationMode.Combined]: { valueAggregation: ColumnValueAggregation.ColumnWeightedAverageAggregator },
            }
          }
        },
      ];
      const result = isCompatibleColumnsAggregationOverride(columns);
      expect(result).to.be.true;
    });
  });
});
