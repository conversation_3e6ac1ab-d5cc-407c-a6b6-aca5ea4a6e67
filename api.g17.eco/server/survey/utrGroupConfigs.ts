
/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */
import type { FragmentUtrConfiguration } from './compositeUtrConfigs';
import { ObjectId } from 'bson';

export interface UTrGroupConfigMap {
  [key: string]: UTrGroupConfigInterface;
}

// Used as enum for mongo model
export enum GroupType {
  Custom = 'custom',
  Group = 'group',
  Static = 'static',
}

interface GroupData {
  colour?: string;
  link?: string;
  icon?: string;
  alternativeCode?: string;
}

export interface UTrGroupConfigCreate {
  type: GroupType;
  groupName: string;
  utrCodes: string[];
  groupId?: string | ObjectId;
  groupDate?: Date | string;
  groupData?: GroupData;
}

export interface UTrGroupConfigInterface<T = ObjectId> {
  type?: GroupType;
  groupId?: T;
  groupDate?: Date | string;
  groupName: string;
  groupData?: GroupData;
  utrCodes: string[];
  fragmentUtrConfiguration?: FragmentUtrConfiguration;
}

export interface UTrCustomGroup<T = ObjectId> extends UTrGroupConfigInterface<T> {
  groupId: T;
  type: GroupType;
}
