/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { Blueprint } from '../../repository/BlueprintRepository';
import { Blueprints } from '../blueprints';
import config from '../../config';
import { blueprintDefaultUnitConfig } from '../../service/units/unitTypes';
import { CompositeUtrConfigInterface } from '../compositeUtrConfigs';

// import { generic_ghg_emissions } from '../configs/gri2020/generic_ghg_emissions';
import { generic_energy_consumption } from '../configs/gri2020/generic_energy_consumption';

import { generic_pc_change_in_energy_from_initiatives } from '../configs/gri2020/generic_pc_change_in_energy_from_initiatives';
import { generic_pc_employee_working_days_lost } from '../configs/gri2020/generic_pc_employee_working_days_lost';
import { generic_pc_renewable_fuel_consumption } from '../configs/gri2020/generic_pc_renewable_fuel_consumption';
import { generic_pc_security_formal_training_human_rights } from '../configs/gri2020/generic_pc_security_formal_training_human_rights';
import { generic_pc_water_from_3rd_party } from '../configs/gri2020/generic_pc_water_from_3rd_party';
import { generic_pc_women_salary_to_men } from '../configs/gri2020/generic_pc_women_salary_to_men';
import { generic_scope_2 } from '../configs/gri2020/generic_scope_2';

import { sdg_1_2_pc_employees_temp_permanent_contracts } from '../configs/gri2020/sdg_1_2_pc_employees_temp_permanent_contracts';
import { sdg_3_2_pc_health_care_cover_families } from '../configs/gri2020/sdg_3_2_pc_health_care_cover_families';
import { sdg_4_5_ratio_training_gender } from '../configs/gri2020/sdg_4_5_ratio_training_gender';
import { sdg_5_4_retention_employees_parental_leave } from '../configs/gri2020/sdg_5_4_retention_employees_parental_leave';
import { sdg_6_4_pc_water_withdrawl_from_scarce } from '../configs/gri2020/sdg_6_4_pc_water_withdrawl_from_scarce';
import { sdg_8_1_economic_value_generated } from '../configs/gri2020/sdg_8_1_economic_value_generated';
import { sdg_8_2_company_transition_assistance_programmes } from '../configs/gri2020/sdg_8_2_company_transition_assistance_programmes';
import { sdg_8_5_pc_employees_management_from_local_community } from '../configs/gri2020/sdg_8_5_pc_employees_management_from_local_community';
import { sdg_8_6_pc_employee_turnover_18_30 } from '../configs/gri2020/sdg_8_6_pc_employee_turnover_18_30';
import { sdg_8_6_pc_new_hires_18_30 } from '../configs/gri2020/sdg_8_6_pc_new_hires_18_30';
import { sdg_9_1_pc_buildings_impact_assessment } from '../configs/gri2020/sdg_9_1_pc_buildings_impact_assessment';
import { sdg_9_4_pc_infrastructure_not_upgraded } from '../configs/gri2020/sdg_9_4_pc_infrastructure_not_upgraded';
import { sdg_10_2_pc_company_leadership_under_represented } from '../configs/gri2020/sdg_10_2_pc_company_leadership_under_represented';
import { sdg_10_3_pc_employees_receive_career_review } from '../configs/gri2020/sdg_10_3_pc_employees_receive_career_review';
import { sdg_10_4_pc_workers_salary_relative_to_ceo } from '../configs/gri2020/sdg_10_4_pc_workers_salary_relative_to_ceo';
import { sdg_12_2_pc_recycled_materials_in_manufacturing } from '../configs/gri2020/sdg_12_2_pc_recycled_materials_in_manufacturing';
import { sdg_12_4_significant_air_emissions } from '../configs/gri2020/sdg_12_4_significant_air_emissions';
import { sdg_12_5_pc_hazardous_waste_recycled } from '../configs/gri2020/sdg_12_5_pc_hazardous_waste_recycled';
import { sdg_12_5_pc_operational_waste_sustainably_eliminated } from '../configs/gri2020/sdg_12_5_pc_operational_waste_sustainably_eliminated';
import { sdg_13_1_investment_for_mitigating_climate_change_risks } from '../configs/gri2020/sdg_13_1_investment_for_mitigating_climate_change_risks';
import { sdg_14_1_pc_packaging_recyclable } from '../configs/gri2020/sdg_14_1_pc_packaging_recyclable';
import { sdg_14_2_pc_labelling_information_on_sourcing_marine } from '../configs/gri2020/sdg_14_2_pc_labelling_information_on_sourcing_marine';
import { sdg_14_2_pc_investment_partnerships_promote_marine_protection } from '../configs/gri2020/sdg_14_2_pc_investment_partnerships_promote_marine_protection';
import { sdg_15_1_species_monitor } from '../configs/gri2020/sdg_15_1_species_monitor';
import { sdg_15_1_pc_raw_materials_sustainably_sourced } from '../configs/gri2020/sdg_15_1_pc_raw_materials_sustainably_sourced';
import { sdg_16_5_pc_operations_assesed_corruption } from '../configs/gri2020/sdg_16_5_pc_operations_assesed_corruption';
import { sdg_16_5_pc_employees_training_anti_corruption } from '../configs/gri2020/sdg_16_5_pc_employees_training_anti_corruption';
import { sdg_16_5_pc_business_partners_anti_corruption_policies_communicated } from '../configs/gri2020/sdg_16_5_pc_business_partners_anti_corruption_policies_communicated';
import { sdg_16_6_conflict_interest_disclosed } from '../configs/gri2020/sdg_16_6_conflict_interest_disclosed';
import { sdg_16_7_monitor_nomination_governance_body } from '../configs/gri2020/sdg_16_7_monitor_nomination_governance_body';
import { sdg_16_7_stakeholder_consulation_governance } from '../configs/gri2020/sdg_16_7_stakeholder_consulation_governance';
import { sdg_16_10_num_substantiative_incidents_loss_data } from '../configs/gri2020/sdg_16_10_num_substantiative_incidents_loss_data';
import { sdg_16_10_num_incidents_customer_data } from '../configs/gri2020/sdg_16_10_num_incidents_customer_data';
import { sdg_17_1_tax_paid } from '../configs/gri2020/sdg_17_1_tax_paid';

import { adapter2020 } from '../configs/adapter2020/adapter2020';
import { sdbti_scope_1_2 } from '../configs/gri2020/sbti_scope_1_2';
// import { YesNoConfigs } from '../configs/gri2020/number_to_yes_no_configs';
import * as vc from '../../util/valueChain';


const configs = [
  // ...YesNoConfigs, // Must be at the start to set correct sourceType for other configs to composite
  generic_energy_consumption,
  // generic_ghg_emissions,
  generic_pc_change_in_energy_from_initiatives,
  generic_pc_employee_working_days_lost,
  generic_pc_renewable_fuel_consumption,
  generic_pc_security_formal_training_human_rights,
  generic_pc_water_from_3rd_party,
  generic_pc_women_salary_to_men,
  generic_scope_2,

  sdg_1_2_pc_employees_temp_permanent_contracts,
  sdg_3_2_pc_health_care_cover_families,
  sdg_4_5_ratio_training_gender,
  sdg_5_4_retention_employees_parental_leave,
  sdg_6_4_pc_water_withdrawl_from_scarce,
  sdg_8_1_economic_value_generated,
  sdg_8_2_company_transition_assistance_programmes,
  sdg_8_5_pc_employees_management_from_local_community,
  sdg_8_6_pc_employee_turnover_18_30,
  sdg_8_6_pc_new_hires_18_30,
  sdg_9_1_pc_buildings_impact_assessment,
  sdg_9_4_pc_infrastructure_not_upgraded,
  sdg_10_2_pc_company_leadership_under_represented,

  sdg_10_3_pc_employees_receive_career_review,
  sdg_10_4_pc_workers_salary_relative_to_ceo,
  sdg_12_2_pc_recycled_materials_in_manufacturing,
  sdg_12_4_significant_air_emissions,
  sdg_12_5_pc_hazardous_waste_recycled,
  sdg_12_5_pc_operational_waste_sustainably_eliminated,
  sdg_13_1_investment_for_mitigating_climate_change_risks,
  sdg_14_1_pc_packaging_recyclable,
  sdg_14_2_pc_investment_partnerships_promote_marine_protection,
  sdg_14_2_pc_labelling_information_on_sourcing_marine,
  sdg_15_1_species_monitor,
  sdg_15_1_pc_raw_materials_sustainably_sourced,
  sdg_16_5_pc_operations_assesed_corruption,
  sdg_16_5_pc_employees_training_anti_corruption,
  sdg_16_5_pc_business_partners_anti_corruption_policies_communicated,
  sdg_16_6_conflict_interest_disclosed,
  sdg_16_7_monitor_nomination_governance_body,
  sdg_16_7_stakeholder_consulation_governance,
  sdg_16_10_num_substantiative_incidents_loss_data,
  sdg_16_10_num_incidents_customer_data,
  sdg_17_1_tax_paid,

  sdbti_scope_1_2,
];

const { cdn } = config.assets;
const griIcon = `${cdn}/images/logos/gri.png`;
const getLinkPath = (file: string) => `${cdn}/standards/gri-standards-2019/${file}`;

export const gri2020: Blueprint = {
  references: [],
  name: 'GRI 2020',
  code: Blueprints.Gri2020,
  logo: {
    src: griIcon,
    alt: 'GRI',
  },
  additionalConfigs: [], // Written at the end of the file
  unitConfig: blueprintDefaultUnitConfig,
  forms: [
    {
      utrGroupConfig: {
        groupName: 'Key Normalization Questions: Financial',
        utrCodes: [
          'survey/generic/capex-capital-expenditure',
          'survey/generic/ebitda',
          'survey/generic/opex-operational-expenditure',
          'survey/generic/corporate-debt',
          'survey/generic/capex-lag-one-year',
          'survey/generic/capex-5-years-average',
          'survey/generic/capex-3-years-cumulative',
          'survey/generic/taxes-paid',
          'survey/generic/tax-rate',
          'survey/generic/average-salary',
          'survey/generic/supply-chain-spend',
          'survey/supply-chain-revenue',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'Key Normalization Questions: Operational',
        utrCodes: [
          'gri/2020/102-7/a',
          'survey/generic/num-working-days',
          'survey/sdg/15.2/total-forest-agriculture-aquaculture-managed',
          'survey/sdg/16.2/average-employee-working-hours',
          'survey/generic/volunteering-hours',
          'survey/generic/num-audits',
          'survey/generic/num-spills',
          'survey/generic/public-private-partnerships',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'Intensity',
        utrCodes: [
          'survey/generic/water-intensity',
          'survey/generic/water-intensity-denominator',
          'gri/2020/302-3/a',
          'gri/2020/302-3/b',
          'gri/2020/305-4/a',
          'gri/2020/305-4/b',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'SGX Core',
        utrCodes: [
          'sgx-core-1a',
          'sgx-custom-42',
          'sgx-custom-48',
          'sgx-custom-49',
          'sgx-custom-53',
          'sgx-custom-57',
          'sgx-custom-58',
          'sgx-custom-60',
          'sgx-custom-56',
          'sgx-custom-75',
          'sgx-core-28b',
          'sgx-core-28x',
          'sgx-core-28y',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'SGX Extended',
        utrCodes: [
          'sgx-custom-64',
          'sgx-custom-67',
          'sgx-custom-70',
          'sgx-custom-71',
          'sgx-custom-77',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'ERQ Metrics',
        utrCodes: [
          'erq-metrics-2022-risk-1.1',
          'erq-metrics-2022-risk-1.1/a',
          'erq-metrics-2022-risk-1.2',
          'erq-metrics-2022-risk-1.3',
          'erq-metrics-2022-risk-1.4',
          'erq-metrics-2022-gov-1.1',
          'erq-metrics-2022-gov-1.2',
          'erq-metrics-2022-gov-1.3',
          'erq-metrics-2022-gov-1.4',
          'erq-metrics-2022-gov-1.5',
          'erq-metrics-2022-gov-1.5/a',
          'erq-metrics-2022-met-1.1',
          'erq-metrics-2022-met-1.2',
          'erq-metrics-2022-met-1.2/a',
          'erq-metrics-2022-met-1.3',
          'erq-metrics-2022-sf-1.1',
          'erq-metrics-2022-sf-1.2',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'SDG Strategy',
        utrCodes: [
          'survey/generic/alignment-sdgs-investment',
          'survey/generic/spend-sdgs-initiatives',
          'survey/generic/investment-committed-sdgs',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'International Finance Corporation (IFC) ESG Metrics',
        utrCodes: [
          'ifc/ps1-1',
          'ifc/ps1-2',
          'ifc/ps1-3',
          'ifc/ps1-4',
          'ifc/ps1-5',
          'ifc/ps1-6',
          'ifc/ps2-7',
          'ifc/ps2-8',
          'ifc/ps2-9',
          'ifc/ps2-10',
          'ifc/ps2-11',
          'ifc/ps2-12',
          'ifc/ps2-13',
          'ifc/ps2-14',
          'ifc/ps2-15',
          'ifc/ps2sectesp-16',
          'ifc/ps2sectesp-17',
          'ifc/ps2sectesp-18',
          'ifc/ps3-19',
          'ifc/ps3-20',
          'ifc/ps3-21',
          'ifc/ps3-22',
          'ifc/ps4-23',
          'ifc/ps4-24',
          'ifc/ps4-25',
          'ifc/ps4sectesp-26',
          'ifc/ps4sectesp-27',
          'ifc/ps5-28',
          'ifc/ps5-29',
          'ifc/ps5-30',
          'ifc/ps6-31',
          'ifc/ps6-32',
          'ifc/ps6-33',
          'ifc/ps6-34',
          'ifc/ps6-35',
          'ifc/ps6-36',
          'ifc/ps6-37',
          'ifc/ps6-38',
          'ifc/ps6-39',
          'ifc/ps6-40',
          'ifc/ps7-41',
          'ifc/ps7-42',
          'ifc/ps8-43',
          'ifc/gp-a-1',
          'ifc/gp-a-2',
          'ifc/gp-a-3',
          'ifc/gp-b-4',
          'ifc/gp-b-5',
          'ifc/gp-b-6',
          'ifc/gp-b-7',
          'ifc/gp-b-8',
          'ifc/gp-c-9',
          'ifc/gp-c-10',
          'ifc/gp-c-11',
          'ifc/gp-c-12',
          'ifc/gp-d-13',
          'ifc/gp-d-14',
          'ifc/gp-e-15',
          'ifc/gp-e-16',
          'ifc/gp-e-17',
          'ifc/gp-e-18',
          'ifc/gp-e-19',
          'ifc/gp-f-20',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'SDG 1: No Poverty',
        utrCodes: [
          'survey/generic/supplier-living-wage',
          'survey/sdg/1.2/pc-difference-min-living-wage',
          'survey/sdg/1.3/pc-smallholders-living-wage',
          'survey/generic/pc-company-workforce-paid-national-living-wage',
          'survey/generic/pc-employees-basic-services',
          'survey/sdg/1.3/social-protection',
          'survey/sdg/1.4/suppliers-owned-women',
          'survey/generic/pc-spend-msmes-suppliers',
          'survey/sdg/1.4/pc-spend-women-suppliers',
          'survey/sdg/1.4/gender-related-barriers',
          'unctad/2020/a.3.1',
          'survey/sdg/1.5/risk-assessment',
          'survey/sdg/1.5/pc-facilities-risk-assessment',
          'unctad/2020/c.2.3',
        ],
      },
    },
    {
      utrGroupConfig: {
        groupName: 'SDG 2: Zero Hunger',
        utrCodes: [
          'survey/sdg/2.1/pc-employees-nutritious-food',
          'survey/sdg/2.1/pc-funding-programs-nutritous-food',
          'survey/sdg/2.1/pc-harvest-lost-post-harvest-loss',
          'survey/sdg/2.2/pc-healthy-products',
          'survey/sdg/2.2/pc-products-labelling-nutrition-information',
          'survey/sdg/2.3/pc-supply-chain-revenue-small-producers',
          'survey/sdg/2.3/investment-impact-investing',
          'survey/sdg/2.3/pc-operations-compete-local-communities',
          'survey/sdg/2.4/pc-spend-supply-chain-sustainable-agriculture',
          'survey/generic/pc-suppliers-report-sustainability-practices',
          'survey/generic/pc-dividend-shared-with-affected-indigenous-people-in-a-commitment-to-biopiracy',
          'survey/sdg/2.5/pc-production-biodiversity-friendly',
        ],
      },
    },
    {
      utrGroupConfig: {
        groupName: 'SDG 3: Good Health and Well-Being',
        utrCodes: [
          'survey/sdg/3.1/pc-pregnant-women-access-care',
          'survey/sdg/3.1/pc-eligible-workforce-access-services',
          'survey/sdg/3.2/pc-facilities-early-year-care',
          'survey/sdg/3.2/company-businesss-health-care-programme',
          'survey/sdg/3.3/proportion-workers-males-hiv-testing',
          'survey/sdg/3.3/proportion-workers-females-hiv-testing',
          'survey/sdg/3.3/pc-workforce-access-health-care',
          'survey/sdg/3.3/hiv-educated',
          'survey/sdg/3.4/pc-company-tobacco-smoke-free',
          'survey/sdg/2.1/pc-employees-nutritious-food',
          'survey/sdg/2.2/pc-products-labelling-nutrition-information',
          'survey/sdg/3.4/pc-investment-inform-public-risks-product',
          'survey/sdg/3.5/pc-workforce-access-substance-treatment',
          'survey/sdg/3.5/pc-workforce-support-addiction-help',
          'survey/sdg/3.5/company-awareness-alcohol',
          'survey/sdg/3.6/pc-company-road-users',
          'survey/sdg/3.7/pc-workforce-access-sex-health-care',
          'survey/sdg/3.7/pc-company-health-personnel-trained-needs-of-women',
          'survey/sdg/3.8/proportion-workers-access-health-services-work-related-accidents-paid-company',
          'survey/sdg/3.8/proportion-workers-access-health-services-non-work-related-accidents-paid-company',
          'survey/generic/pc-toxic-spills',
          'survey/sdg/3.9/pc-operations-use-hazardous-chemicals',
          'unctad/2020/c.3.1',
        ],
      },
    },
    {
      utrGroupConfig: {
        groupName: 'SDG 4: Quality Education',
        utrCodes: [
          'survey/sdg/4.1/volunteering-stem',
          'survey/sdg/4.1/chid-workforce-education',
          'survey/sdg/3.2/pc-facilities-early-year-care',
          'survey/sdg/4.2/childcare-education',
          'survey/sdg/4.3/ratio-training-male-female',
          'survey/sdg/4.3/pc-workforce-supported-through-education',
          'survey/sdg/4.4/vocational-technical-training',
          'survey/sdg/4.4/academic-partnerships',
          'survey/sdg/4.4/average-days-training',
          'survey/sdg/4.5/non-discriminative-access-training',
          'survey/sdg/4.7/pc-training-days-focus-sustainability',
          'survey/sdg/4.7/pc-bonuses-based-on-ees-topics',
        ],
      },
    },
    {
      utrGroupConfig: {
        groupName: 'SDG 5: Gender Equality',
        utrCodes: [
          'survey/sdg/5.1/employees-gender-discrimination-training',
          'survey/sdg/5.1/ratio-employee-turnover',
          'survey/generic/ratio-parental-leave-male-female',
          'survey/generic/opex-childcare-value',
          'survey/sdg/5.2/num-incidents-gender-based-violence',
          'survey/sdg/5.4/pc-workforce-flexible-working',
          'survey/sdg/5.5/female-share-management',
          'survey/sdg/5.5/proportion-women-leadership-trade-unions',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'SDG 6: Clean Water and Sanitation',
        utrCodes: [
          'survey/sdg/6.1/pc-workforce-accessible-drinking-water',
          'survey/sdg/6.1/pc-water-tested',
          'survey/sdg/6.2/pc-company-sanitation-facilities-functioning',
          'survey/sdg/6.3/pc-facilities-wastewater-management',
          'survey/generic/pc-wastewater-treated',
          'survey/sdg/6.4/pc-investment-water-recycling',
          'survey/sdg/6.4/pc-operations-at-risk-from-access-to-water',
          'survey/sdg/6.5/pc-operations-water-risk-management-plan',
          'survey/sdg/6.5/engage-water-policy',
          'survey/generic/water-pollution',
          'survey/sdg/6.6/water-risk-assessment',
          'survey/generic/pc-toxic-spills',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'SDG 7: Affordable and Clean Energy',
        utrCodes: [
          'survey/sdg/7.1/pc-revenue-generated-low-carbon-services',
          'survey/sdg/7.1/pc-capex-sustainable-energy',
          'survey/generic/paris-aligned-targets',
          'unctad/2020/b.5.2',
        ],
      },
    },
    {
      utrGroupConfig: {
        groupName: 'SDG 8: Decent Work and Economic Growth',
        utrCodes: [
          'survey/sdg/8.1/local-market-share-company-services',
          'survey/sdg/8.1/company-buying-practices-negative-impact',
          'survey/sdg/8.2/pc-company-company-productivity-due-tech',
          'survey/generic/suppliers-members-vulnerable-groups',
          'survey/generic/pc-spend-msmes-suppliers',
          'survey/sdg/8.3/pc-capex-deployed-tech-with-start-ups-local-needs',
          'survey/generic/pc-suppliers-msmes',
          'survey/sdg/8.4/pc-materials-used-physical-goods-that-can-be-repurposed',
          'survey/sdg/8.5/pc-employees-18-30',
          'survey/sdg/8.5/pc-employees-disability',
          'survey/sdg/8.5/pc-employees-minority-group',
          'survey/sdg/8.5/does-company-support-suppliers-to',
          'survey/sdg/8.6/pc-employees-18-30-training',
          'survey/sdg/8.7/child-labour-supply-chain-operations',
          'survey/sdg/8.7/pc-operations-risk-for-incidents-forced-labour',
          'survey/sdg/8.8/health-safety-training',
          'unctad/2020/c.3.1',
          'survey/sdg/8.9/purchase-from-local-providers',
          'survey/sdg/8.10/company-offer-financial-advice-employees',
          'survey/sdg/8.10/company-offer-equal-access-underrepresented-groups',
        ],
      },
    },
    {
      utrGroupConfig: {
        groupName: 'SDG 9: Industry, Innovation and Infrastructure',
        utrCodes: [
          'survey/sdg/9.1/pc-capex-resilient-services',
          'survey/generic/pc-investment-ict-considers-digital-access',
          'survey/generic/pc-investment-improved-road-safety',
          'survey/sdg/9.1/hours-lost-due-to-electrical-outages',
          'survey/generic/pc-spend-msmes-suppliers',
          'survey/sdg/9.3/timely-payments-suppliers',
          'survey/sdg/9.3/spend-msmes-sustainability',
          'unctad/2020/a.3.1',
          'survey/sdg/9.4/pc-operations-with-monitoring',
          'survey/sdg/9.4/sustainable-site-design',
          'survey/sdg/9.5/pc-opex-research-and-development',
          'survey/sdg/17.9/expenditure-r-and-d-tech-developing-countries',
          'survey/sdg/9.5/compnay-expenditure-third-party-education',
          'survey/sdg/9.5/pc-patent-applications-succesful',
          'survey/sdg/9.5/pc-work-force-researchers',
        ],
      },
    },
    {
      utrGroupConfig: {
        groupName: 'SDG 10: Reduced Inequalities',
        utrCodes: [
          'survey/generic/suppliers-members-vulnerable-groups',
          'survey/sdg/10.1/pc-employers-supply-chain-offer-training',
          'survey/generic/supplier-living-wage',
          'survey/generic/pc-company-workforce-paid-national-living-wage',
          'survey/sdg/10.2/pc-company-leadership-catagorised-under-represented',
          'survey/sdg/10.3/discrimination-policy',
          'survey/sdg/10.4/pc-employees-access-insurance',
          'unctad/2020/c.2.3',
          'survey/sdg/10.5/pc-operations-break-financial-regulations',
          'survey/sdg/10.5/is-company-regularly-audited',
          'survey/sdg/10.7/human-trafficking',
          'survey/sdg/10.7/pc-migrant-workers-trafficked-check',
        ],
      },
    },
    {
      utrGroupConfig: {
        groupName: 'SDG 11: Sustainable Cities and Communities',
        utrCodes: [
          'survey/sdg/11.1/pc-investment-water-needs-local-pop',
          'survey/sdg/11.1/pc-investment-energy-renewable-energy',
          'survey/sdg/11.1/affordable-housing',
          'survey/generic/pc-employees-basic-services',
          'survey/sdg/11.2/pc-employees-access-sustainable-transportation',
          'survey/sdg/11.2/pc-transport-respect-disabilities',
          'survey/generic/pc-investment-improved-road-safety',
          'survey/sdg/11.3/pc-projects-maintains-fpic',
          'survey/sdg/11.3/pc-projects-communicates-with-communities-urban',
          'survey/sdg/11.4/initiatives-enhance-local-culture',
          'survey/sdg/11.5/crisis-emergency-plan',
          'survey/sdg/11.5/pc-employees-drr-training',
          'survey/sdg/11.5/pc-capex-tech-devt-disaster-risk-management',
          'survey/sdg/11.5/pc-budget-post-disaster-reconstruction',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'SDG 12: Responsible Consumption and Production',
        utrCodes: [
          'survey/sdg/12.1/pc-r-and-d-sustainable-production-consumption',
          'survey/sdg/9.5/pc-opex-research-and-development',
          'survey/sdg/12.1/pc-partnerships-ngos-producer-countries',
          'survey/sdg/12.3/pc-reduction-food-waste',
          'survey/sdg/2.1/pc-harvest-lost-post-harvest-loss',
          'survey/sdg/12.4/chemical-management-policy',
          'survey/generic/pc-toxic-spills',
          'survey/sdg/12.4/pc-facilities-with-relevant-water-quality',
          'survey/sdg/12.4/pc-products-services-with-lifecycle-assessment',
          'survey/sdg/12.5/pc-waste-reduction-through-company-initiatives',
          'survey/sdg/12.5/does-company-implement-circular-business-model',
          'survey/generic/does-company-report-sustainable-development-risks',
          'survey/sdg/12.8/traceability-system',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'SDG 13: Climate Action',
        utrCodes: [
          'survey/sdg/13.1/pc-r-d-technology',
          'survey/sdg/13.1/pc-r-d-technology-spend',
          'survey/sdg/13.1/pc-revenue-from-low-carbon-initiatives',
          'survey/sdg/13.1/corporate-strategies-climate-change',
          'survey/generic/pc-suppliers-report-sustainability-practices',
          'survey/generic/paris-aligned-targets',
          'survey/sdg/13.2/internal-price-carbon',
          'survey/sdg/13.2/pc-partnerships-climate-related',
          'survey/sdg/13.3/internal-programmes-awareness-climate-change',
          'survey/sdg/13.3/pc-capex-envt-management',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'SDG 14: Life Below Water',
        utrCodes: [
          'survey/generic/water-pollution',
          'survey/generic/pc-wastewater-treated',
          'survey/generic/pc-toxic-spills',
          'survey/sdg/14.1/pc-plastic-waste-recycled',
          'survey/sdg/14.2/products-impact-marine-diversity',
          'survey/sdg/14.2/tools-habitat-areas-affected-water-discharge-by-company',
          'survey/sdg/14.2/marine-areas-protected',
          'survey/sdg/14.4/marine-ingredients-well-managed',
          'survey/sdg/14.4/spend-risk-fishing',
          'survey/sdg/14.4/science-based-fishing-practices',
          'survey/sdg/14.7/therapeutic-treatment-aquaculture',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'SDG 15: Life on Land',
        utrCodes: [
          'survey/generic/biodiversity-assessment',
          'survey/sdg/15.2/land-sustainably-managed',
          'survey/sdg/15.2/pc-production-monitor-raw-materials',
          'survey/sdg/15.2/forest-policies',
          'survey/sdg/15.3/desertification-management',
          'survey/sdg/15.3/pc-soil-contamination-cases-remediated',
          'survey/sdg/15.4/pc-sites-assessed-biodiversity-risk',
          'survey/sdg/15.5/spend-conservation',
          'survey/sdg/15.6/pc-benefit-sharing-agreements-genetic-resources',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'SDG 16: Peace, Justice and Strong Institutions',
        utrCodes: [
          'survey/sdg/16.1/suppliers-screened-harassment',
          'survey/sdg/16.1/pc-incidents-violence-workplace',
          'survey/sdg/16.2/num-children-employed-7-14',
          'survey/sdg/16.2/average-working-hours-children-7-14',
          'survey/sdg/16.2/child-labour-policy',
          'survey/sdg/16.3/company-grievance-channel',
          'survey/sdg/16.3/num-legal-actions-unethical-business',
          'survey/sdg/16.3/company-provide-legal-access',
          'survey/sdg/16.4/illicit-flows',
          'survey/sdg/16.4/pc-new-suppliers-due-diligence-organised-crime',
          'survey/sdg/16.4/pc-employees-training-trafficking',
          'survey/generic/does-company-report-sustainable-development-risks',
          'survey/sdg/16.10/human-rights-engagement-laws',
          'survey/sdg/16.10/pc-employees-trained-personal-data',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'SDG 17: Partnerships for the Goals',
        utrCodes: [
          'survey/sdg/17.1/pc-fines-paid-non-compliance-domestic-tax-laws',
          'survey/sdg/17.3/investment-sustainable-development-developing-countries',
          'survey/sdg/17.3/pc-profits-reinvested-home-countries',
          'survey/sdg/17.9/spend-partnerships-developing-countries',
          'survey/sdg/17.9/expenditure-r-and-d-tech-developing-countries',
          'survey/generic/does-company-report-sustainable-development-risks',
          'survey/sdg/17.16/pc-expenditure-joint-initiatives',
          'survey/sdg/17.16/pc-capacity-building-programmes-to-exchange-sustainable-development-knowledge',
          'survey/sdg/17.17/pc-opex-to-partnerships',
          'survey/sdg/17.17/pc-partnerships-outcome-available-outside-interested-party',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'SASB - Asset Management & Custody Activities',
        groupData: {
          alternativeCode: 'sasb'
        },
        utrCodes: [
          'sasb/2020/fn-ac-270a.1',
          'sasb/2020/fn-ac-270a.2',
          'sasb/2020/fn-ac-270a.3',
          'sasb/2020/fn-ac-330a.1',
          'sasb/2020/fn-ac-410a.1',
          'sasb/2020/fn-ac-410a.2',
          'sasb/2020/fn-ac-410a.3',
          'sasb/2020/fn-ac-510a.1',
          'sasb/2020/fn-ac-510a.2',
          'sasb/2020/fn-ac-550a.1',
          'sasb/2020/fn-ac-550a.2',
          'sasb/2020/fn-ac-550a.3',
          'sasb/2020/fn-ac-550a.4',
          'sasb/2020/fn-ac-000.a',
          'sasb/2020/fn-ac-000.b',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'SASB - Health Care',
        groupData: {
          alternativeCode: 'sasb'
        },
        utrCodes: [
          'sasb/2020/hc-bp-210a.1',
          'sasb/2020/hc-bp-210a.2',
          'sasb/2020/hc-bp-210a.3',
          'sasb/2020/hc-bp-240a.1',
          'sasb/2020/hc-bp-240a.2',
          'sasb/2020/hc-bp-240b.1',
          'sasb/2020/hc-bp-240b.2',
          'sasb/2020/hc-bp-240b.3',
          'sasb/2020/hc-bp-250a.1',
          'sasb/2020/hc-bp-250a.2',
          'sasb/2020/hc-bp-250a.3',
          'sasb/2020/hc-bp-250a.4',
          'sasb/2020/hc-bp-250a.5',
          'sasb/2020/hc-bp-260a.1',
          'sasb/2020/hc-bp-260a.2',
          'sasb/2020/hc-bp-260a.3',
          'sasb/2020/hc-bp-270a.1',
          'sasb/2020/hc-bp-270a.2',
          'sasb/2020/hc-bp-330a.1',
          'sasb/2020/hc-bp-330a.2',
          'sasb/2020/hc-bp-430a.1',
          'sasb/2020/hc-bp-510a.1',
          'sasb/2020/hc-bp-510a.2',
          'sasb/2020/hc-ms-240a.1',
          'sasb/2020/hc-ms-240a.2',
          'sasb/2020/hc-ms-250a.1',
          'sasb/2020/hc-ms-250a.2',
          'sasb/2020/hc-ms-250a.3',
          'sasb/2020/hc-ms-250a.4',
          'sasb/2020/hc-ms-270a.1',
          'sasb/2020/hc-ms-270a.2',
          'sasb/2020/hc-ms-410a.1',
          'sasb/2020/hc-ms-410a.2',
          'sasb/2020/hc-ms-430a.1',
          'sasb/2020/hc-ms-430a.2',
          'sasb/2020/hc-ms-430a.3',
          'sasb/2020/hc-ms-510a.1',
          'sasb/2020/hc-ms-510a.2',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'SASB - Software & IT Services',
        groupData: {
          alternativeCode: 'sasb'
        },
        utrCodes: [
          'sasb/2020/tc-si-130a.1',
          'sasb/2020/tc-si-130a.2',
          'sasb/2020/tc-si-130a.3',
          'sasb/2020/tc-si-220a.1',
          'sasb/2020/tc-si-220a.2',
          'sasb/2020/tc-si-220a.3',
          'sasb/2020/tc-si-220a.4',
          'sasb/2020/tc-si-220a.5',
          'sasb/2020/tc-si-230a.1',
          'sasb/2020/tc-si-230a.2',
          'sasb/2020/tc-si-330a.1',
          'sasb/2020/tc-si-330a.2',
          'sasb/2020/tc-si-330a.3',
          'sasb/2020/tc-si-520a.1',
          'sasb/2020/tc-si-550a.1',
          'sasb/2020/tc-si-550a.2',
          'sasb/2020/tc-si-000.a',
          'sasb/2020/tc-si-000.b',
          'sasb/2020/tc-si-000.c',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'SASB - Food & Beverage - Processed Foods',
        groupData: {
          alternativeCode: 'sasb'
        },
        utrCodes: [
          'sasb/2020/fb-pf-140a.2',
          'sasb/2020/fb-pf-250a.1',
          'sasb/2020/fb-pf-250a.3',
          'sasb/2020/fb-pf-270a.1',
          'sasb/2020/fb-pf-270a.2',
          'sasb/2020/fb-pf-270a.3',
          'sasb/2020/fb-pf-270a.4',
          'sasb/2020/fb-pf-410a.1',
          'sasb/2020/fb-pf-410a.2',
          'sasb/2020/fb-pf-430a.1',
          'sasb/2020/fb-pf-430a.2',
          'sasb/2020/fb-pf-440a.1',
          'sasb/2020/fb-pf-130a.1',
          'sasb/2020/fb-pf-140a.1',
          'sasb/2020/fb-pf-140a.3',
          'sasb/2020/fb-pf-250a.2',
          'sasb/2020/fb-pf-250a.4',
          'sasb/2020/fb-pf-260a.1',
          'sasb/2020/fb-pf-260a.2',
          'sasb/2020/fb-pf-440a.2',
          'sasb/2020/fb-pf-000.a',
          'sasb/2020/fb-pf-000.b',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'SASB - Telecommunication Services',
        groupData: {
          alternativeCode: 'sasb'
        },
        utrCodes: [
          'sasb/2020/tc-tl-130a.1',
          'sasb/2020/tc-tl-220a.1',
          'sasb/2020/tc-tl-220a.2',
          'sasb/2020/tc-tl-220a.3',
          'sasb/2020/tc-tl-220a.4',
          'sasb/2020/tc-tl-230a.1',
          'sasb/2020/tc-tl-230a.2',
          'sasb/2020/tc-tl-440a.1',
          'sasb/2020/tc-tl-520a.1',
          'sasb/2020/tc-tl-520a.2',
          'sasb/2020/tc-tl-520a.3',
          'sasb/2020/tc-tl-550a.1',
          'sasb/2020/tc-tl-550a.2',
          'sasb/2020/tc-tl-000.a',
          'sasb/2020/tc-tl-000.b',
          'sasb/2020/tc-tl-000.c',
          'sasb/2020/tc-tl-000.d',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'SASB - Internet Media & Services',
        groupData: {
          alternativeCode: 'sasb'
        },
        utrCodes: [
          'sasb/2020/tc-im-130a.1',
          'sasb/2020/tc-im-130a.2',
          'sasb/2020/tc-im-130a.3',
          'sasb/2020/tc-im-220a.1',
          'sasb/2020/tc-im-220a.2',
          'sasb/2020/tc-im-220a.3',
          'sasb/2020/tc-im-220a.4',
          'sasb/2020/tc-im-220a.5',
          'sasb/2020/tc-im-220a.6',
          'sasb/2020/tc-im-230a.1',
          'sasb/2020/tc-im-230a.2',
          'sasb/2020/tc-im-330a.1',
          'sasb/2020/tc-im-330a.2',
          'sasb/2020/tc-im-330a.3',
          'sasb/2020/tc-im-520a.1',
          'sasb/2020/tc-im-000.a',
          'sasb/2020/tc-im-000.b',
          'sasb/2020/tc-im-000.c',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'SASB - Oil & Gas - Midstream',
        groupData: {
          alternativeCode: 'sasb'
        },
        utrCodes: [
          'sasb/em-md-160a.1',
          'sasb/em-md-160a.2',
          'sasb/em-md-160a.3',
          'sasb/em-md-160a.4',
          'sasb/em-md-520a.1',
          'sasb/em-md-540a.1',
          'sasb/em-md-540a.2',
          'sasb/em-md-540a.3',
          'sasb/em-md-540a.4',
          'sasb/em-md-000.a',
          'sasb/em-md-110a.1',
          'sasb/em-md-110a.2',
          'sasb/em-md-120a.1',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'SASB - Oil & Gas - Exploration & Production',
        groupData: {
          alternativeCode: 'sasb'
        },
        utrCodes: [
          'sasb/em-ep-110a.1',
          'sasb/em-ep-110a.2',
          'sasb/em-ep-110a.3',
          'sasb/em-ep-120a.1',
          'sasb/em-ep-140a.1',
          'sasb/em-ep-140a.2',
          'sasb/em-ep-140a.3',
          'sasb/em-ep-140a.4',
          'sasb/em-ep-160a.1',
          'sasb/em-ep-160a.2',
          'sasb/em-ep-160a.3',
          'sasb/em-ep-210a.1',
          'sasb/em-ep-210a.2',
          'sasb/em-ep-210a.3',
          'sasb/em-ep-210b.1',
          'sasb/em-ep-210b.2',
          'sasb/em-ep-320a.1',
          'sasb/em-ep-320a.2',
          'sasb/em-ep-420a.1',
          'sasb/em-ep-420a.2',
          'sasb/em-ep-420a.3',
          'sasb/em-ep-420a.4',
          'sasb/em-ep-510a.1',
          'sasb/em-ep-510a.2',
          'sasb/em-ep-530a.1',
          'sasb/em-ep-540a.1',
          'sasb/em-ep-540a.2',
          'sasb/em-ep-000.a',
          'sasb/em-ep-000.b',
          'sasb/em-ep-000.c',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'SASB - Oil & Gas - Services',
        groupData: {
          alternativeCode: 'sasb'
        },
        utrCodes: [
          'sasb/em-sv-110a.1',
          'sasb/em-sv-110a.2',
          'sasb/em-sv-110a.3',
          'sasb/em-sv-140a.1',
          'sasb/em-sv-140a.2',
          'sasb/em-sv-150a.1',
          'sasb/em-sv-150a.2',
          'sasb/em-sv-160a.1',
          'sasb/em-sv-160a.2',
          'sasb/em-sv-320a.1',
          'sasb/em-sv-320a.2',
          'sasb/em-sv-510a.1',
          'sasb/em-sv-510a.2',
          'sasb/em-sv-530a.1',
          'sasb/em-sv-540a.1',
          'sasb/em-sv-000.a',
          'sasb/em-sv-000.b',
          'sasb/em-sv-000.c',
          'sasb/em-sv-000.d',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'SASB - Oil & Gas - Refining & Marketing',
        groupData: {
          alternativeCode: 'sasb'
        },
        utrCodes: [
          'sasb/em-rm-110a.1',
          'sasb/em-rm-110a.2',
          'sasb/em-rm-120a.1',
          'sasb/em-rm-120a.2',
          'sasb/em-rm-140a.1',
          'sasb/em-rm-140a.2',
          'sasb/em-rm-150a.1',
          'sasb/em-rm-150a.2',
          'sasb/em-rm-320a.1',
          'sasb/em-rm-320a.2',
          'sasb/em-rm-410a.1',
          'sasb/em-rm-410a.2',
          'sasb/em-rm-520a.1',
          'sasb/em-rm-530a.1',
          'sasb/em-rm-540a.1',
          'sasb/em-rm-540a.2',
          'sasb/em-rm-540a.3',
          'sasb/em-rm-000.a',
          'sasb/em-rm-000.b',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'CDP 2022 Climate Change Questionnaire',
        groupData: {
          alternativeCode: 'cdp_2022'
        },
        utrCodes: [
          'cdp/2022/climate/c0.3',
          'cdp/2022/climate/c-fs0.7',
          'cdp/2022/climate/c0.8',
          'cdp/2022/climate/c1.1b',
          'cdp/2022/climate/c1.1d',
          'cdp/2022/climate/c1.2',
          'cdp/2022/climate/c-fs1.4',
          'cdp/2022/climate/c-fs2.2b',
          'cdp/2022/climate/c-fs2.2c',
          'cdp/2022/climate/c-fs2.2d',
          'cdp/2022/climate/c-fs2.2e',
          'cdp/2022/climate/c2.3a',
          'cdp/2022/climate/c3.1',
          'cdp/2022/climate/c3.2',
          'cdp/2022/climate/c3.2a',
          'cdp/2022/climate/c3.2b',
          'cdp/2022/climate/c3.5',
          'cdp/2022/climate/c3.5a',
          'cdp/2022/climate/c-fs3.6',
          'cdp/2022/climate/c-fs3.6a',
          'cdp/2022/climate/c-fs3.6b',
          'cdp/2022/climate/c-fs3.6c',
          'cdp/2022/climate/c-fs3.7',
          'cdp/2022/climate/c-fs3.7a',
          'cdp/2022/climate/c-fs3.8',
          'cdp/2022/climate/c-fs3.8a',
          'cdp/2022/climate/c4.1',
          'cdp/2022/climate/c4.1a',
          'cdp/2022/climate/c4.1b',
          'cdp/2022/climate/c-fs4.1d',
          'cdp/2022/climate/c4.2a',
          'cdp/2022/climate/c4.2b',
          'cdp/2022/climate/c4.2c',
          'cdp/2022/climate/c4.5',
          'cdp/2022/climate/c4.5a',
          'cdp/2022/climate/c-fs4.5',
          'cdp/2022/climate/c-fs4.5a',
          'cdp/2022/climate/c5.1',
          'cdp/2022/climate/c5.1a',
          'cdp/2022/climate/c5.1b',
          'cdp/2022/climate/c5.1c',
          'cdp/2022/climate/c5.2',
          'cdp/2022/climate/c5.3',
          'cdp/2022/climate/c6.4a',
          'cdp/2022/climate/c6.5',
          'cdp/2022/climate/c6.5a',
          'cdp/2022/climate/c7.5',
          'cdp/2022/climate/c-ch8.2a',
          'cdp/2022/climate/c-st8.2a',
          'cdp/2022/climate/c8.2c',
          'cdp/2022/climate/c-ce8.2c',
          'cdp/2022/climate/c-ch8.2d',
          'cdp/2022/climate/c-eu8.2d',
          'cdp/2022/climate/c-st8.2d',
          'cdp/2022/climate/c8.2e',
          'cdp/2022/climate/c8.2g',
          'cdp/2022/climate/c-eu9.5a',
          'cdp/2022/climate/c11.1b',
          'cdp/2022/climate/c11.1c',
          'cdp/2022/climate/c12.1a',
          'cdp/2022/climate/c12.1b',
          'cdp/2022/climate/c-fs12.1b',
          'cdp/2022/climate/c-fs12.1c',
          'cdp/2022/climate/c12.2',
          'cdp/2022/climate/c12.2a',
          'cdp/2022/climate/c-fs12.2',
          'cdp/2022/climate/c-fs12.2a',
          'cdp/2022/climate/c12.3',
          'cdp/2022/climate/c12.3a',
          'cdp/2022/climate/c12.3b',
          'cdp/2022/climate/c12.3c',
          'cdp/2022/climate/c-fs12.5',
          'cdp/2022/climate/c-fs14.0',
          'cdp/2022/climate/c-fs14.1',
          'cdp/2022/climate/c-fs14.1a',
          'cdp/2022/climate/c-fs14.1b',
          'cdp/2022/climate/c-fs14.2',
          'cdp/2022/climate/c-fs14.2a',
          'cdp/2022/climate/c-fs14.2b',
          'cdp/2022/climate/c-fs14.2c',
          'cdp/2022/climate/c-fs14.2d',
          'cdp/2022/climate/c-fs14.3',
          'cdp/2022/climate/c-fs14.3a',
          'cdp/2022/climate/c15.1',
          'cdp/2022/climate/c15.2',
          'cdp/2022/climate/c15.3',
          'cdp/2022/climate/c15.4',
          'cdp/2022/climate/c15.5',
          'cdp/2022/climate/c15.6',
          'cdp/2022/climate/c16.1',
          'cdp/2022/climate/sc1.1',
          'cdp/2022/climate/sc2.2a',
          'cdp/2022/climate/fw-fs1.1',
          'cdp/2022/climate/fw-fs1.1a',
          'cdp/2022/climate/fw-fs1.1b',
          'cdp/2022/climate/fw-fs1.1c',
          'cdp/2022/climate/fw-fs1.2',
          'cdp/2022/climate/fw-fs2.1',
          'cdp/2022/climate/fw-fs2.1a',
          'cdp/2022/climate/fw-fs2.2',
          'cdp/2022/climate/fw-fs2.2a',
          'cdp/2022/climate/fw-fs2.3',
          'cdp/2022/climate/fw-fs2.3a',
          'cdp/2022/climate/fw-fs2.4',
          'cdp/2022/climate/fw-fs2.4a',
          'cdp/2022/climate/fw-fs3.1',
          'cdp/2022/climate/fw-fs3.2',
          'cdp/2022/climate/fw-fs3.3',
          'cdp/2022/climate/fw-fs3.3a',
          'cdp/2022/climate/fw-fs3.4',
          'cdp/2022/climate/fw-fs3.4a',
          'cdp/2022/climate/fw-fs3.5',
          'cdp/2022/climate/fw-fs4.1',
          'cdp/2022/climate/fw-fs4.1a',
          'cdp/2022/climate/fw-fs4.1b',
          'cdp/2022/climate/fw-fs4.2',
          'cdp/2022/climate/fw-fs4.3',
          'cdp/2022/climate/fw-fs4.3a',
          'cdp/2022/climate/fw-fs4.4',
          'cdp/2022/climate/fw-fs4.4a',
          'cdp/2022/climate/fw-fs5.1',
          'cdp/2022/climate/fw-fs5.2',
          'cdp/2022/climate/fw-fs5.2a',
          'cdp/2022/climate/fw-fs5.2b',
          'cdp/2022/climate/fw-fs6.1',
          'cdp/2022/water/w-fb0.1a',
          'cdp/2022/water/w-og0.1a',
          'cdp/2022/water/w0.3',
          'cdp/2022/water/w0.7',
          'cdp/2022/water/w1.3',
          'cdp/2022/water/w2.1a',
          'cdp/2022/water/w3.3a',
          'cdp/2022/water/w3.3b',
          'cdp/2022/water/w3.3c',
          'cdp/2022/water/w4.2',
          'cdp/2022/water/w4.2a',
          'cdp/2022/water/w5.1a',
          'cdp/2022/water/w6.2d',
          'cdp/2022/water/w6.3',
          'cdp/2022/water/w7.3',
          'cdp/2022/water/w7.3a',
          'cdp/2022/water/w7.5',
          'cdp/2022/water/w8.1a',
          'cdp/2022/forest/f0.6',
          'cdp/2022/forest/f-mm0.7',
          'cdp/2022/forest/f-mm0.8',
          'cdp/2022/forest/f-mm0.8a',
          'cdp/2022/forest/f1.2',
          'cdp/2022/forest/f1.3',
          'cdp/2022/forest/f1.5a',
          'cdp/2022/forest/f1.7',
          'cdp/2022/forest/f2.1a',
          'cdp/2022/forest/f2.1b',
          'cdp/2022/forest/f2.2',
          'cdp/2022/forest/f2.2a',
          'cdp/2022/forest/f3.1b',
          'cdp/2022/forest/f3.2a',
          'cdp/2022/forest/f4.1d',
          'cdp/2022/forest/f4.5a',
          'cdp/2022/forest/f4.5b',
          'cdp/2022/forest/f4.6a',
          'cdp/2022/forest/f4.6b',
          'cdp/2022/forest/f6.1a',
          'cdp/2022/forest/f6.3',
          'cdp/2022/forest/f6.3a',
          'cdp/2022/forest/f6.8',
          'cdp/2022/forest/f6.10',
          'cdp/2022/forest/f6.10a',
          'cdp/2022/forest/f6.10b',
          'cdp/2022/forest/f6.11',
          'cdp/2022/forest/f6.12',
          'cdp/2022/forest/f6.12a',
          'cdp/2022/forest/f8.1',
          'cdp/2022/forest/f8.2',
          'cdp/2022/forest/sf1.1a',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'CDP Climate Change Questionnaire - Full',
        groupData: {
          alternativeCode: 'cdp'
        },
        utrCodes: [
          "cdp-climate/language",
          "cdp-climate/c.01",
          "cdp-climate/c0.2",
          "cdp-climate/c0.3",
          "cdp-climate/c0.4",
          "cdp-climate/c0.5",
          "cdp-climate/c-ac0.6",
          "cdp-climate/c-ac0.6a",
          "cdp-climate/c-ac0.6b",
          "cdp-climate/c-ac0.6c",
          "cdp-climate/c-ac0.6d",
          "cdp-climate/c-ac0.6e",
          "cdp-climate/c-ac0.6f",
          "cdp-climate/c-ac0.6g",
          "cdp-climate/c-ac0.7a",
          "cdp-climate/c-ce0.7",
          "cdp-climate/c-ch0.7",
          "cdp-climate/c-co0.7",
          "cdp-climate/c-eu0.7",
          "cdp-climate/c-mm0.7",
          "cdp-climate/c-og0.7",
          "cdp-climate/c-st0.7",
          "cdp-climate/c-to0.7",
          "cdp-climate/c-cn0.7",
          "cdp-climate/c-fs0.7",
          "cdp-climate/c1.1",
          "cdp-climate/c1.1a",
          "cdp-climate/c1.1b",
          "cdp-climate/c1.1c",
          "cdp-climate/c1.2",
          "cdp-climate/c1.2a",
          "cdp-climate/c1.3",
          "cdp-climate/c1.3a",
          "cdp-climate/c-fs1.4",
          "cdp-climate/c2.1",
          "cdp-climate/c2.1a",
          "cdp-climate/c2.1b",
          "cdp-climate/c2.2",
          "cdp-climate/c2.2a",
          "cdp-climate/c-fs2.2b",
          "cdp-climate/c-fs2.2c",
          "cdp-climate/c-fs2.2d",
          "cdp-climate/c-fs2.2e",
          "cdp-climate/c-fs2.2f",
          "cdp-climate/c2.2g",
          "cdp-climate/c2.3",
          "cdp-climate/c2.3a",
          "cdp-climate/c2.3b",
          "cdp-climate/c2.4",
          "cdp-climate/c2.4a",
          "cdp-climate/c2.4b",
          "cdp-climate/c3.1",
          "cdp-climate/c3.1a",
          "cdp-climate/c3.1b",
          "cdp-climate/c3.2",
          "cdp-climate/c3.2a",
          "cdp-climate/c3.2b",
          "cdp-climate/c3.3",
          "cdp-climate/c3.4",
          "cdp-climate/c3.4a",
          "cdp-climate/c3.5",
          "cdp-climate/c-fs3.6",
          "cdp-climate/c-fs3.6a",
          "cdp-climate/c-fs3.6b",
          "cdp-climate/c-fs3.6c",
          "cdp-climate/c-fs3.7",
          "cdp-climate/c-fs3.7a",
          "cdp-climate/c-fs3.7b",
          "cdp-climate/c4",
          "cdp-climate/c4.1a",
          "cdp-climate/c4.1b",
          "cdp-climate/c4.1c",
          "cdp-climate/c4.2",
          "cdp-climate/c4.2a",
          "cdp-climate/c4.2b",
          "cdp-climate/c4.2c",
          "cdp-climate/c-co4.2d",
          "cdp-climate/c-og4.2d",
          "cdp-climate/c4.3",
          "cdp-climate/c4.3a",
          "cdp-climate/c4.3b",
          "cdp-climate/c4.3c",
          "cdp-climate/c4.3d",
          "cdp-climate/c-ac4.4",
          "cdp-climate/c-ac4.4a",
          "cdp-climate/c4.5",
          "cdp-climate/c4.5a",
          "cdp-climate/c-co4.6",
          "cdp-climate/c-eu4.6",
          "cdp-climate/c-og4.6",
          "cdp-climate/c-co4.7",
          "cdp-climate/c-og4.7",
          "cdp-climate/c-co4.7a",
          "cdp-climate/c-og4.7a",
          "cdp-climate/c-co4.7b",
          "cdp-climate/c-og4.7b",
          "cdp-climate/c-co4.8",
          "cdp-climate/c-og4.8",
          "cdp-climate/c-st4.9",
          "cdp-climate/c-ce4.9",
          "cdp-climate/c5.1",
          "cdp-climate/c5.2",
          "cdp-climate/c5.2a",
          "cdp-climate/c6.1",
          "cdp-climate/c6.2",
          "cdp-climate/c6.3",
          "cdp-climate/c6.4",
          "cdp-climate/c6.4a",
          "cdp-climate/c6.5",
          "cdp-climate/c-ac6.6",
          "cdp-climate/c-ac6.6a1",
          "cdp-climate/c-ac6.6a2",
          "cdp-climate/c-ac6.6a3",
          "cdp-climate/c-ac6.6a4",
          "cdp-climate/c-ac6.6b",
          "cdp-climate/c-cg6.6",
          "cdp-climate/c-cg6.6a",
          "cdp-climate/c-cn6.6",
          "cdp-climate/c-cn6.6a",
          "cdp-climate/c-cn6.6b",
          "cdp-climate/c-cn6.6c",
          "cdp-climate/c6.7",
          "cdp-climate/c6.7a",
          "cdp-climate/c-ac6.8",
          "cdp-climate/c-ac6.8a",
          "cdp-climate/c-ac6.9",
          "cdp-climate/c-ac6.9a",
          "cdp-climate/c6.10",
          "cdp-climate/c-ce6.11",
          "cdp-climate/c-og6.12",
          "cdp-climate/c-og6.13",
          "cdp-climate/c-st6.14",
          "cdp-climate/c-ts6.15",
          "cdp-climate/c7.1",
          "cdp-climate/c7.1a",
          "cdp-climate/c-co7.1b",
          "cdp-climate/c-eu7.1b",
          "cdp-climate/c-og7.1b",
          "cdp-climate/c7.2",
          "cdp-climate/c7.3",
          "cdp-climate/c7.3a",
          "cdp-climate/c7.3b",
          "cdp-climate/c7.3c",
          "cdp-climate/c-ac7.4",
          "cdp-climate/c-ac7.4a",
          "cdp-climate/c-ac7.4b",
          "cdp-climate/c-ac7.4c",
          "cdp-climate/c-ce7.4",
          "cdp-climate/c7.5",
          "cdp-climate/c7.6",
          "cdp-climate/c7.6a",
          "cdp-climate/c7.6b",
          "cdp-climate/c7.6c",
          "cdp-climate/c-ce7.7",
          "cdp-climate/c-ch7.8",
          "cdp-climate/c-ch7.8a",
          "cdp-climate/c-to7.8",
          "cdp-climate/c7.9",
          "cdp-climate/c7.9a",
          "cdp-climate/c7.9b",
          "cdp-climate/c-cg7.10",
          "cdp-climate/c-cg7.10a",
          "cdp-climate/c8.1",
          "cdp-climate/c8.2",
          "cdp-climate/c8.2a",
          "cdp-climate/c-ce8.2a",
          "cdp-climate/c-ch8.2a",
          "cdp-climate/c-mm8.2a",
          "cdp-climate/c-st8.2a",
          "cdp-climate/c8.2b",
          "cdp-climate/c8.2c",
          "cdp-climate/c-ce8.2c",
          "cdp-climate/c8.2d",
          "cdp-climate/c-ce8.2d",
          "cdp-climate/c-ch8.2d",
          "cdp-climate/c-eu8.2d",
          "cdp-climate/c-mm8.2d",
          "cdp-climate/c-st8.2d",
          "cdp-climate/c8.2e",
          "cdp-climate/c-ts8.2f",
          "cdp-climate/c-ch8.3",
          "cdp-climate/c-ch8.3a",
          "cdp-climate/c-ch8.3b",
          "cdp-climate/c-st8.3",
          "cdp-climate/c-eu8.4",
          "cdp-climate/c-eu8.4a",
          "cdp-climate/c-cg8.5",
          "cdp-climate/c-cg8.5a",
          "cdp-climate/c-to8.5",
          "cdp-climate/c-ts8.5",
          "cdp-climate/c9.1",
          "cdp-climate/c-co9.2a",
          "cdp-climate/c-og9.2a",
          "cdp-climate/c-co9.2b",
          "cdp-climate/c-og9.2b",
          "cdp-climate/c-og9.2c",
          "cdp-climate/c-og9.2d",
          "cdp-climate/c-og9.2e",
          "cdp-climate/c-to9.3",
          "cdp-climate/c-ch9.3a",
          "cdp-climate/c-co9.3a",
          "cdp-climate/c-mm9.3a",
          "cdp-climate/c-og9.3a",
          "cdp-climate/c-st9.3a",
          "cdp-climate/c-co9.3b",
          "cdp-climate/c-mm9.3b",
          "cdp-climate/c-og9.3b",
          "cdp-climate/c-st9.3b",
          "cdp-climate/c-og9.3c",
          "cdp-climate/c-og9.3d",
          "cdp-climate/c-og9.3e",
          "cdp-climate/c-co9.4a",
          "cdp-climate/c-eu9.5a",
          "cdp-climate/c-eu9.5b",
          "cdp-climate/c-ce9.6",
          "cdp-climate/c-ce9.6a",
          "cdp-climate/c-cg9.6a",
          "cdp-climate/c-ch9.6a",
          "cdp-climate/c-cn9.6a",
          "cdp-climate/c-co9.6a",
          "cdp-climate/c-mm9.6a",
          "cdp-climate/c-st9.6a",
          "cdp-climate/c-to9.6a",
          "cdp-climate/c-og9.7",
          "cdp-climate/c-og9.8",
          "cdp-climate/c-og9.8a",
          "cdp-climate/c-og9.8b",
          "cdp-climate/c-og9.8c",
          "cdp-climate/c-re9.9",
          "cdp-climate/c-re9.9a",
          "cdp-climate/c-cn9.10",
          "cdp-climate/c-cn9.10a",
          "cdp-climate/c-cn9.11",
          "cdp-climate/c10.1",
          "cdp-climate/c10.1a",
          "cdp-climate/c10.1b",
          "cdp-climate/c10.1c",
          "cdp-climate/c10.2",
          "cdp-climate/c10.2a",
          "cdp-climate/c11.1",
          "cdp-climate/c11.1a",
          "cdp-climate/c11.1b",
          "cdp-climate/c11.1c",
          "cdp-climate/c11.1d",
          "cdp-climate/c11.2",
          "cdp-climate/c11.2a",
          "cdp-climate/c11.3",
          "cdp-climate/c11.3a",
          "cdp-climate/c12.1",
          "cdp-climate/c12.1a",
          "cdp-climate/c12.1b",
          "cdp-climate/c12.1b-fs",
          "cdp-climate/c-fs12.1c",
          "cdp-climate/c12.1d",
          "cdp-climate/c12.1e",
          "cdp-climate/c-ac12.2",
          "cdp-climate/c-ac12.2a",
          "cdp-climate/c-ac12.2b",
          "cdp-climate/c-ac12.2c",
          "cdp-climate/c12.3",
          "cdp-climate/c12.3a",
          "cdp-climate/c12.3b",
          "cdp-climate/c12.3c",
          "cdp-climate/c12.3d",
          "cdp-climate/c12.3e",
          "cdp-climate/c12.3f",
          "cdp-climate/c12.3g",
          "cdp-climate/c12.4",
          "cdp-climate/c-fs12.5",
          "cdp-climate/c-ac13.1",
          "cdp-climate/c-ac13.1a",
          "cdp-climate/c-ac13.2",
          "cdp-climate/c-ac13.2a",
          "cdp-climate/c-fs14.1",
          "cdp-climate/c-fs14.1a",
          "cdp-climate/c-fs14.1b",
          "cdp-climate/c-fs14.1c",
          "cdp-climate/c-fs14.2",
          "cdp-climate/c-fs14.2a",
          "cdp-climate/c-fs14.2b",
          "cdp-climate/c-fs14.2c",
          "cdp-climate/c-fs14.3",
          "cdp-climate/c-fs14.3a",
          "cdp-climate/c-fs14.3b",
          "cdp-climate/c-fi",
          "cdp-climate/c15.1",
          "cdp-climate/sc0.0",
          "cdp-climate/sc0.1",
          "cdp-climate/sc0.2",
          "cdp-climate/sc0.2a",
          "cdp-climate/sc1.1",
          "cdp-climate/sc1.2",
          "cdp-climate/sc1.3",
          "cdp-climate/sc1.4",
          "cdp-climate/sc1.4a",
          "cdp-climate/sc1.4b",
          "cdp-climate/sc2.1",
          "cdp-climate/sc2.2",
          "cdp-climate/sc2.2a",
          "cdp-climate/sc4.1",
          "cdp-climate/sc4.1a",
          "cdp-climate/sc4.2a",
          "cdp-climate/sc4.2b",
          "cdp-climate/sc4.2c",
          "cdp-climate/sc4.2d",
          "cdp-climate/sc4.2e",
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'CDP Forests Questionnaire - Full',
        groupData: {
          alternativeCode: 'cdp'
        },
        utrCodes: [
          "cdp-forest/f0.1",
          "cdp-forest/f0.2",
          "cdp-forest/f0.3",
          "cdp-forest/f0.4",
          "cdp-forest/f0.5",
          "cdp-forest/f0.5a",
          "cdp-forest/f-mm0.6",
          "cdp-forest/f-mm0.7",
          "cdp-forest/f-mm0.7a",
          "cdp-forest/f1.1",
          "cdp-forest/f1.2",
          "cdp-forest/f1.3",
          "cdp-forest/f1.5",
          "cdp-forest/f1.5a",
          "cdp-forest/f1.5b",
          "cdp-forest/f1.5c",
          "cdp-forest/f1.6",
          "cdp-forest/f1.6a",
          "cdp-forest/f1.5d",
          "cdp-forest/f1.4",
          "cdp-forest/f1.5e",
          "cdp-forest/f2.1",
          "cdp-forest/f2.1a",
          "cdp-forest/f2.1b",
          "cdp-forest/f2.1c",
          "cdp-forest/f2.1d",
          "cdp-forest/f3.1",
          "cdp-forest/f3.1a",
          "cdp-forest/f3.1b",
          "cdp-forest/f3.1c",
          "cdp-forest/f3.2",
          "cdp-forest/f3.2a",
          "cdp-forest/f3.2b",
          "cdp-forest/f4.1",
          "cdp-forest/f1.4a",
          "cdp-forest/f4.1b",
          "cdp-forest/f4.1c",
          "cdp-forest/f4.2",
          "cdp-forest/f4.3",
          "cdp-forest/f4.3a",
          "cdp-forest/f4.4",
          "cdp-forest/f4.5",
          "cdp-forest/f4.5a",
          "cdp-forest/f4.5b",
          "cdp-forest/f4.6",
          "cdp-forest/f4.6a",
          "cdp-forest/f4.6b",
          "cdp-forest/f5.1",
          "cdp-forest/f6.1",
          "cdp-forest/f6.1a",
          "cdp-forest/f6.1b",
          "cdp-forest/f6.2",
          "cdp-forest/f6.2a",
          "cdp-forest/f6.2b",
          "cdp-forest/f6.3",
          "cdp-forest/f6.3a",
          "cdp-forest/f6.4",
          "cdp-forest/f6.4a",
          "cdp-forest/f6.5",
          "cdp-forest/f6.5a",
          "cdp-forest/f6.5b",
          "cdp-forest/f6.6",
          "cdp-forest/f6.6a",
          "cdp-forest/f6.7",
          "cdp-forest/f6.8",
          "cdp-forest/f6.9",
          "cdp-forest/f6.10",
          "cdp-forest/f6.11",
          "cdp-forest/f6.11a",
          "cdp-forest/f7.1",
          "cdp-forest/f7.1a",
          "cdp-forest/f8.1",
          "cdp-forest/f8.2",
          "cdp-forest/f-mm9.1",
          "cdp-forest/f-mm9.2",
          "cdp-forest/f-mm9.2a",
          "cdp-forest/f-mm9.3",
          "cdp-forest/f-mm9.3a",
          "cdp-forest/f-mm9.4",
          "cdp-forest/f-mm9.4a",
          "cdp-forest/f-mm9.5",
          "cdp-forest/f-mm9.5a",
          "cdp-forest/f-mm9.6",
          "cdp-forest/f-mm9.6a",
          "cdp-forest/f-mm10.1",
          "cdp-forest/f-mm10.1a",
          "cdp-forest/f-mm10.2",
          "cdp-forest/f-mm10.2a",
          "cdp-forest/f-mm10.2b",
          "cdp-forest/f-mm10.2c",
          "cdp-forest/f-mm10.3",
          "cdp-forest/f-mm10.3a",
          "cdp-forest/f11.1",
          "cdp-forest/f11.1a",
          "cdp-forest/f11.2",
          "cdp-forest/f11.2a",
          "cdp-forest/f11.2b",
          "cdp-forest/f11.3",
          "cdp-forest/11.3a",
          "cdp-forest/f-mm12.4",
          "cdp-forest/f-mm12.4a",
          "cdp-forest/f-mm12.1",
          "cdp-forest/f-mm12.1a",
          "cdp-forest/f-mm12.1b",
          "cdp-forest/f-mm12.2",
          "cdp-forest/f-mm12.3",
          "cdp-forest/f-mm12.3a",
          "cdp-forest/f-mm12.5",
          "cdp-forest/f-mm12.5a",
          "cdp-forest/f13.1",
          "cdp-forest/f14.1",
          "cdp-forest/f14.1a",
          "cdp-forest/f14.2",
          "cdp-forest/f14.3",
          "cdp-forest/f14.3a",
          "cdp-forest/f14.4",
          "cdp-forest/f14.4a",
          "cdp-forest/f14.5",
          "cdp-forest/f14.5a",
          "cdp-forest/f14.6",
          "cdp-forest/f14.6a",
          "cdp-forest/f14.7",
          "cdp-forest/f14.7a",
          "cdp-forest/f14.8",
          "cdp-forest/f14.8a",
          "cdp-forest/f-mm15.1",
          "cdp-forest/f-mm15.12",
          "cdp-forest/f-mm15.12a",
          "cdp-forest/f-mm15.13",
          "cdp-forest/f-mm15.13a",
          "cdp-forest/f-mm15.4",
          "cdp-forest/f-mm15.4a",
          "cdp-forest/f-mm15.5",
          "cdp-forest/f-mm15.5a",
          "cdp-forest/f-mm16.1",
          "cdp-forest/f-mm16.1a",
          "cdp-forest/f-fi",
          "cdp-forest/f-17.1",
          "cdp-forest/sf0.1",
          "cdp-forest/sf0.2",
          "cdp-forest/sf0.2a",
          "cdp-forest/sf1.1",
          "cdp-forest/sf1.1a",
          "cdp-forest/sf1.1b",
          "cdp-forest/sf2.1",
          "cdp-forest/sf2.2",
          "cdp-forest/sf2.2a",
          "cdp-forest/sf3.1",
          "cdp-forest/sf3.1a"
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'CDP Water Security Questionnaire - Full',
        groupData: {
          alternativeCode: 'cdp'
        },
        utrCodes: [
          "cdp-water/w0.1",
          "cdp-water/w-ch0.1a",
          "cdp-water/w-eu0.1a",
          "cdp-water/w-eu0.1b",
          "cdp-water/w-fb0.1a",
          "cdp-water/w-mm0.1a",
          "cdp-water/w-og0.1a",
          "cdp-water/w0.2",
          "cdp-water/w0.3",
          "cdp-water/w0.4",
          "cdp-water/w0.5",
          "cdp-water/w0.6",
          "cdp-water/w0.6a",
          "cdp-water/w1.1",
          "cdp-water/w-fb1.1a",
          "cdp-water/w1.2",
          "cdp-water/w-eu1.2a",
          "cdp-water/w1.2b",
          "cdp-water/w-og1.2c",
          "cdp-water/w1.2d",
          "cdp-water/w-fb1.2e",
          "cdp-water/w-fb1.2f",
          "cdp-water/w-fb1.2g",
          "cdp-water/w1.2h",
          "cdp-water/w1.2i",
          "cdp-water/w1.2j",
          "cdp-water/w-ch1.3",
          "cdp-water/w-ch1.3a",
          "cdp-water/w-eu1.3",
          "cdp-water/w-eu1.3a",
          "cdp-water/w-fb1.3",
          "cdp-water/w-fb1.3a",
          "cdp-water/w-fb1.3b",
          "cdp-water/w-mm1.3",
          "cdp-water/w-mm1.3a",
          "cdp-water/w-og1.3",
          "cdp-water/w-og1.3a",
          "cdp-water/w1.4",
          "cdp-water/w1.4a",
          "cdp-water/w1.4b",
          "cdp-water/w1.4c",
          "cdp-water/w1.4d",
          "cdp-water/w2.1",
          "cdp-water/w2.1a",
          "cdp-water/w2.2",
          "cdp-water/w2.2a",
          "cdp-water/w2.2b",
          "cdp-water/w-ch3.1",
          "cdp-water/w-ch3.1a",
          "cdp-water/w-eu3.1",
          "cdp-water/w-eu3.1a",
          "cdp-water/w-fb3.1",
          "cdp-water/w-fb3.1a",
          "cdp-water/w-og3.1",
          "cdp-water/w-og3.1a",
          "cdp-water/w-mm3.2",
          "cdp-water/w-mm3.2c",
          "cdp-water/w3.3",
          "cdp-water/w3.3a",
          "cdp-water/w3.3b",
          "cdp-water/w3.3c",
          "cdp-water/w3.3d",
          "cdp-water/w3.3e",
          "cdp-water/w-mm3.2a",
          "cdp-water/w-mm3.2b",
          "cdp-water/w4.1",
          "cdp-water/w4.1a",
          "cdp-water/w4.1b",
          "cdp-water/w4.1c",
          "cdp-water/w4.2",
          "cdp-water/w4.2a",
          "cdp-water/w4.2b",
          "cdp-water/w4.2c",
          "cdp-water/w4.3",
          "cdp-water/w4.3a",
          "cdp-water/4.3b",
          "cdp-water/w5.1",
          "cdp-water/w5.1a",
          "cdp-water/w6.1",
          "cdp-water/w6.1a",
          "cdp-water/w6.2",
          "cdp-water/w6.2a",
          "cdp-water/w6.2b",
          "cdp-water/w6.2c",
          "cdp-water/w6.3",
          "cdp-water/w6.4",
          "cdp-water/w6.4a",
          "cdp-water/w6.5",
          "cdp-water/w6.5a",
          "cdp-water/w6.6",
          "cdp-water/w7.1",
          "cdp-water/w7.2",
          "cdp-water/w7.3",
          "cdp-water/w7.3a",
          "cdp-water/w7.3b",
          "cdp-water/w7.4",
          "cdp-water/w8.1",
          "cdp-water/w8.1a",
          "cdp-water/w8.1b",
          "cdp-water/w8.1c",
          "cdp-water/w9.1",
          "cdp-water/w9.1a",
          "cdp-water/w-fi",
          "cdp-water/w10.1",
          "cdp-water/w10.2",
          "cdp-water/sw0.1",
          "cdp-water/sw0.2",
          "cdp-water/sw0.2a",
          "cdp-water/sw1.1",
          "cdp-water/sw1.1a",
          "cdp-water/sw1.2",
          "cdp-water/sw1.2a",
          "cdp-water/sw2.1",
          "cdp-water/sw2.2",
          "cdp-water/sw2.2a",
          "cdp-water/sw3.1",
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'UNCTAD Core SDG Indicators',
        groupData: {
          alternativeCode: 'unctad'
        },
        utrCodes: [
          'unctad/2020/a.1.3',
          'unctad/2020/a.3.1',
          'unctad/2020/a.3.2',
          'unctad/2020/b.1.1',
          'unctad/2020/b.1.2',
          'unctad/2020/b.2.1',
          'unctad/2020/b.2.2',
          'unctad/2020/b.4.1',
          'unctad/2020/c.2.2',
          'unctad/2020/c.3.1',
          'unctad/2020/d.1.3', // <<
          'unctad/2020/d.1.5',
          'unctad/2020/d.2.1', // <<
          'unctad/2020/d.2.2',
          'unctad/2020/c.2.3'
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: "Roundtable on Sustainable Palm Oil",
        groupData: {
          colour: '#fa6a01',
          alternativeCode: 'rspo'
        },
        utrCodes: [
          "rspo/1.1.1",
          "rspo/1.1.2",
          "rspo/1.1.3",
          "rspo/1.1.4",
          "rspo/1.1.5",
          "rspo/1.2.1",
          "rspo/1.2.2",
          "rspo/2.1.1",
          "rspo/2.1.2",
          "rspo/2.1.3",
          "rspo/2.2.1",
          "rspo/2.2.2",
          "rspo/2.2.3",
          "rspo/2.3.1",
          "rspo/2.3.2",
          "rspo/3.1.1",
          "rspo/3.1.2",
          "rspo/3.1.3",
          "rspo/3.2.1",
          "rspo/3.2.2",
          "rspo/3.3.1",
          "rspo/3.3.2",
          "rspo/3.3.3",
          "rspo/3.4.1",
          "rspo/3.4.2",
          "rspo/3.4.3",
          "rspo/3.5.1",
          "rspo/3.5.2",
          "rspo/3.6.1",
          "rspo/3.6.2",
          "rspo/3.7.1",
          "rspo/3.7.2",
          "rspo/3.7.3",
          "rspo/3.8.1",
          "rspo/3.8.2",
          "rspo/3.8.3",
          "rspo/3.8.4",
          "rspo/3.8.5",
          "rspo/3.8.6",
          "rspo/3.8.7",
          "rspo/3.8.8",
          "rspo/3.8.9",
          "rspo/3.8.10",
          "rspo/3.8.11",
          "rspo/3.8.12",
          "rspo/3.8.13",
          "rspo/3.8.14",
          "rspo/3.8.15",
          "rspo/3.8.16",
          "rspo/3.8.17",
          "rspo/4.1.1",
          "rspo/4.1.2",
          "rspo/4.2.1",
          "rspo/4.2.2",
          "rspo/4.2.3",
          "rspo/4.2.4",
          "rspo/4.3.1",
          "rspo/4.4.1",
          "rspo/4.4.2",
          "rspo/4.4.3",
          "rspo/4.4.4",
          "rspo/4.4.5",
          "rspo/4.4.6",
          "rspo/4.5.1",
          "rspo/4.5.2",
          "rspo/4.5.3",
          "rspo/4.5.4",
          "rspo/4.5.5",
          "rspo/4.5.6",
          "rspo/4.5.7",
          "rspo/4.5.8",
          "rspo/4.6.1",
          "rspo/4.6.2",
          "rspo/4.6.3",
          "rspo/4.6.4",
          "rspo/4.7.1",
          "rspo/4.7.2",
          "rspo/4.7.3",
          "rspo/4.8.1",
          "rspo/4.8.2",
          "rspo/4.8.3",
          "rspo/4.8.4",
          "rspo/5.1.1",
          "rspo/5.1.2",
          "rspo/5.1.3",
          "rspo/5.1.4",
          "rspo/5.1.5",
          "rspo/5.1.6",
          "rspo/5.1.7",
          "rspo/5.1.8",
          "rspo/5.1.9",
          "rspo/5.2.1",
          "rspo/5.2.2",
          "rspo/5.2.3",
          "rspo/5.2.4",
          "rspo/5.2.5",
          "rspo/6.1.1",
          "rspo/6.1.2",
          "rspo/6.1.3",
          "rspo/6.1.4",
          "rspo/6.1.5",
          "rspo/6.1.6",
          "rspo/6.2.1",
          "rspo/6.2.2",
          "rspo/6.2.3",
          "rspo/6.2.4",
          "rspo/6.2.5",
          "rspo/6.2.6",
          "rspo/6.2.7",
          "rspo/6.3.1",
          "rspo/6.3.2",
          "rspo/6.3.3",
          "rspo/6.4.1",
          "rspo/6.4.2",
          "rspo/6.4.3",
          "rspo/6.4.4",
          "rspo/6.5.1",
          "rspo/6.5.2",
          "rspo/6.5.3",
          "rspo/6.5.4",
          "rspo/6.6.1",
          "rspo/6.6.2",
          "rspo/6.7.1",
          "rspo/6.7.2",
          "rspo/6.7.3",
          "rspo/6.7.4",
          "rspo/6.7.5",
          "rspo/7.1.1",
          "rspo/7.1.2",
          "rspo/7.1.3",
          "rspo/7.2.1",
          "rspo/7.2.2",
          "rspo/7.2.3",
          "rspo/7.2.4",
          "rspo/7.2.5",
          "rspo/7.2.6",
          "rspo/7.2.7",
          "rspo/7.2.8",
          "rspo/7.2.9",
          "rspo/7.2.10",
          "rspo/7.2.11",
          "rspo/7.3.1",
          "rspo/7.3.2",
          "rspo/7.3.3",
          "rspo/7.4.1",
          "rspo/7.4.2",
          "rspo/7.4.3",
          "rspo/7.4.4",
          "rspo/7.5.1",
          "rspo/7.5.2",
          "rspo/7.5.3",
          "rspo/7.6.1",
          "rspo/7.6.2",
          "rspo/7.6.3",
          "rspo/7.7.1",
          "rspo/7.7.2",
          "rspo/7.7.3",
          "rspo/7.7.4",
          "rspo/7.7.5",
          "rspo/7.7.6",
          "rspo/7.7.7",
          "rspo/7.8.1",
          "rspo/7.8.2",
          "rspo/7.8.3",
          "rspo/7.8.4",
          "rspo/7.9.1",
          "rspo/7.10.1",
          "rspo/7.10.2",
          "rspo/7.10.3",
          "rspo/7.11.1",
          "rspo/7.11.2",
          "rspo/7.11.3",
          "rspo/7.12.1",
          "rspo/7.12.2",
          "rspo/7.12.3",
          "rspo/7.12.4",
          "rspo/7.12.5",
          "rspo/7.12.6",
          "rspo/7.12.7",
          "rspo/7.12.8"
        ]
      }
    },
    {
      utrGroupConfig: {
        groupName: 'Climate Action 100+ - Net Zero Company Benchmark',
        groupData: {
          alternativeCode: 'ca100'
        },
        utrCodes: [
          'survey/ca100/1.1',
          'survey/ca100/2.1',
          'survey/ca100/2.2',
          'survey/ca100/2.3',
          'survey/ca100/3.1',
          'survey/ca100/3.2',
          'survey/ca100/3.3',
          'survey/ca100/4.1',
          'survey/ca100/4.2',
          'survey/ca100/4.3',
          'survey/ca100/5.1',
          'survey/ca100/5.2',
          'survey/ca100/6.1',
          'survey/ca100/6.2',
          'survey/ca100/7.1',
          'survey/ca100/7.2',
          'survey/ca100/7.3',
          'survey/ca100/8.1',
          'survey/ca100/8.2',
          'survey/ca100/8.3',
          'survey/ca100/10.1',
          'survey/ca100/10.2',
        ],
        fragmentUtrConfiguration: {
        },
      }
    },
    {
      utrGroupConfig: {
        groupName: 'Impact Reporting & Investment Standards',
        groupData: {
          colour: '#980b59',
          alternativeCode: 'iris'
        },
        utrCodes: [
          'survey/iris/oi9624',
          'survey/iris/oi8825',
          'survey/iris/pi9878',
          'survey/iris/oi8709',
          'survey/iris/pi3687',
          'survey/iris/pi4874',
          'survey/iris/oi6912',
          'survey/iris/pi1081',
        ],
        fragmentUtrConfiguration: {
        },
      }
    },
    {
      utrGroupConfig: {
        groupName: 'IOGP International Association of Oil & Gas Producers',
        groupData: {
          alternativeCode: 'iogp'
        },
        utrCodes: [
          "iogp-1",
          "iogp-2",
          "iogp-3",
          "iogp-4",
          "iogp-5",
          "iogp-6",
          "iogp-7",
          "iogp-8",
          "iogp-9",
          "iogp-10",
          "iogp-11",
          "iogp-12",
          "iogp-13",
          "iogp-14",
          "iogp-15",
          "iogp-16",
          "iogp-17",
          "iogp-18",
          "iogp-19",
          "iogp-20",
          "iogp-21",
          "iogp-22",
          "iogp-23",
          "iogp-24",
          "iogp-25",
          "iogp-26",
          "iogp-27",
          "iogp-28",
          "iogp-29",
          "iogp-30",
          "iogp-31",
          "iogp-49",
          "iogp-50",
          "iogp-51",
          "iogp-52",
          "iogp-53",
          "iogp-54",
          "iogp-55",
          "iogp-32",
          "iogp-33",
          "iogp-34",
          "iogp-35",
          "iogp-36",
          "iogp-37",
          "iogp-56",
          "iogp-38",
          "iogp-39",
          "iogp-40",
          "iogp-41",
          "iogp-42",
          "iogp-43",
          "iogp-44",
          "iogp-45",
          "iogp-46",
          "iogp-47",
          "iogp-48",
          "iogp-e1.1",
          "iogp-e1.2",
          "iogp-e1.3",
          "iogp-e1.4",
          "iogp-e1.5",
          "iogp-e1.6",
          "iogp-e1.7",
          "iogp-e1.8",
          "iogp-e2.1",
          "iogp-e2.2",
          "iogp-e2.3",
          "iogp-e2.4",
          "iogp-e2.5",
          "iogp-e2.6",
          "iogp-e3.1",
          "iogp-e3.2",
          "iogp-e3.3",
          "iogp-e3.4",
          "iogp-e3.5",
          "iogp-e3.6",
          "iogp-e4.1",
          "iogp-e4.2",
          "iogp-e4.3",
          "iogp-e4.4",
          "iogp-e5.1",
          "iogp-e5.2",
          "iogp-e5.3",
          "iogp-e5.4",
          "iogp-e6.1",
          "iogp-e6.2",
          "iogp-e6.3",
          "iogp-e6.4",
          "iogp-e6.5",
          "iogp-e6.6",
          "iogp-e7.1",
          "iogp-e7.2",
          "iogp-e7.3",
          "iogp-e7.4",
          "iogp-e7.5",
          "iogp-e8.1",
          "iogp-e8.2",
          "iogp-e8.3",
          "iogp-e8.4",
          "iogp-e8.5",
          "iogp-e9.1",
          "iogp-e9.2",
          "iogp-e9.3",
          "iogp-e9.4",
          "iogp-e9.5",
          "iogp-e10.1",
          "iogp-e10.2",
          "iogp-e10.3",
          "iogp-e10.4",
          "iogp-e10.5",
        ],
      },
    },
    {
      utrGroupConfig: {
        groupName: 'IPIECA Sustainability Reporting Guidance',
        groupData: {
          colour: '#82236f',
          alternativeCode: 'ipieca'
        },
        utrCodes: [
          "ipieca-gov1-c1",
          "ipieca-gov1-c2",
          "ipieca-gov1-c3",
          "ipieca-gov1-c4",
          "ipieca-gov1-c5",
          "ipieca-gov1-a1",
          "ipieca-gov1-a2",
          "ipieca-gov1-a3",
          "ipieca-gov1-a4",
          "ipieca-gov1-a5",
          "ipieca-gov1-a6",
          "ipieca-gov1-a7",
          "ipieca-gov2-c1",
          "ipieca-gov2-c2",
          "ipieca-gov2-c3",
          "ipieca-gov2-c4",
          "ipieca-gov2-a1",
          "ipieca-gov2-a2",
          "ipieca-gov2-a3",
          "ipieca-gov2-a4",
          "ipieca-gov2-a5",
          "ipieca-gov3-c1",
          "ipieca-gov3-c2",
          "ipieca-gov3-c3",
          "ipieca-gov3-c4",
          "ipieca-gov3-a1",
          "ipieca-gov3-a2",
          "ipieca-gov3-a3",
          "ipieca-gov3-a4",
          "ipieca-gov4-c1",
          "ipieca-gov4-c2",
          "ipieca-gov4-c3",
          "ipieca-gov4-c4",
          "ipieca-gov4-a1",
          "ipieca-gov4-a2",
          "ipieca-gov4-a3",
          "ipieca-gov4-a4",
          "ipieca-gov5-c1",
          "ipieca-gov5-c2",
          "ipieca-gov5-a1",
          "ipieca-gov5-a2",
          "ipieca-gov5-a3",
          "ipieca-cce1-c1",
          "ipieca-cce1-c2",
          "ipieca-cce1-c3",
          "ipieca-cce1-c4",
          "ipieca-cce1-a1",
          "ipieca-cce1-a2",
          "ipieca-cce1-a3",
          "ipieca-cce1-a4",
          "ipieca-cce2-c1",
          "ipieca-cce2-c2",
          "ipieca-cce2-c3",
          "ipieca-cce2-c4",
          "ipieca-cce2-a1",
          "ipieca-cce2-a2",
          "ipieca-cce3-c1",
          "ipieca-cce3-c2",
          "ipieca-cce3-a1",
          "ipieca-cce3-a2",
          "ipieca-cce3-a3",
          "ipieca-cce3-a4",
          "ipieca-cce3-a5",
          "ipieca-cce3-a6",
          "ipieca-cce3-a7",
          "ipieca-cce3-a8",
          "ipieca-cce3-a9",
          "ipieca-cce4-c1",
          "ipieca-cce4-c2",
          "ipieca-cce4-c3",
          "ipieca-cce4-c4",
          "ipieca-cce4-a1",
          "ipieca-cce4-a2",
          "ipieca-cce4-a3",
          "ipieca-cce4-a4",
          "ipieca-cce4-a5",
          "ipieca-cce4-a6",
          "ipieca-cce4-a7",
          "ipieca-cce5-c1",
          "ipieca-cce5-c2",
          "ipieca-cce5-a1",
          "ipieca-cce5-a2",
          "ipieca-cce5-a3",
          "ipieca-cce5-a4",
          "ipieca-cce6-c1",
          "ipieca-cce6-c2",
          "ipieca-cce6-a1",
          "ipieca-cce6-a2",
          "ipieca-cce6-a3",
          "ipieca-cce6-a4",
          "ipieca-cce6-a5",
          "ipieca-cce7-c1",
          "ipieca-cce7-c2",
          "ipieca-cce7-c3",
          "ipieca-cce7-c4",
          "ipieca-cce7-c5",
          "ipieca-cce7-a1",
          "ipieca-cce7-a2",
          "ipieca-cce7-a3",
          "ipieca-env1-c1",
          "ipieca-env1-c2",
          "ipieca-env1-c3",
          "ipieca-env1-c4",
          "ipieca-env1-c5",
          "ipieca-env1-a1",
          "ipieca-env1-a2",
          "ipieca-env1-a3",
          "ipieca-env1-a4",
          "ipieca-env1-a5",
          "ipieca-env1-a6",
          "ipieca-env1-a7",
          "ipieca-env1-a8",
          "ipieca-env1-a9",
          "ipieca-env1-a10",
          "ipieca-env1-a11",
          "ipieca-env2-c1",
          "ipieca-env2-c2",
          "ipieca-env2-a1",
          "ipieca-env2-a2",
          "ipieca-env2-a3",
          "ipieca-env2-a4",
          "ipieca-env2-a5",
          "ipieca-env2-a6",
          "ipieca-env2-a7",
          "ipieca-env3-c1",
          "ipieca-env3-c2",
          "ipieca-env3-c3",
          "ipieca-env3-a1",
          "ipieca-env3-a2",
          "ipieca-env3-a3",
          "ipieca-env3-a4",
          "ipieca-env4-c1",
          "ipieca-env4-c2",
          "ipieca-env4-a1",
          "ipieca-env4-a2",
          "ipieca-env5-c1",
          "ipieca-env5-c2",
          "ipieca-env5-a1",
          "ipieca-env5-a2",
          "ipieca-env5-a3",
          "ipieca-env6-c1",
          "ipieca-env6-c2",
          "ipieca-env6-c3",
          "ipieca-env6-c4",
          "ipieca-env6-a1",
          "ipieca-env6-a2",
          "ipieca-env6-a3",
          "ipieca-env6-a4",
          "ipieca-env6-a5",
          "ipieca-env6-a6",
          "ipieca-env6-a7",
          "ipieca-env6-a8",
          "ipieca-env7-c1",
          "ipieca-env7-c2",
          "ipieca-env7-c3",
          "ipieca-env7-a1",
          "ipieca-env7-a2",
          "ipieca-env7-a3",
          "ipieca-env7-a4",
          "ipieca-env8-c1",
          "ipieca-env8-c2",
          "ipieca-env8-a1",
          "ipieca-env8-a2",
          "ipieca-env8-a3",
          "ipieca-env8-a4",
          "ipieca-shs1-c1",
          "ipieca-shs1-c2",
          "ipieca-shs1-c3",
          "ipieca-shs1-a1",
          "ipieca-shs1-a2",
          "ipieca-shs1-a3",
          "ipieca-shs2-c1",
          "ipieca-shs2-c2",
          "ipieca-shs2-c3",
          "ipieca-shs2-a1",
          "ipieca-shs2-a2",
          "ipieca-shs2-a3",
          "ipieca-shs2-a4",
          "ipieca-shs2-a5",
          "ipieca-shs3-c1",
          "ipieca-shs3-c2",
          "ipieca-shs3-c3",
          "ipieca-shs3-c4",
          "ipieca-shs3-a1",
          "ipieca-shs3-a2",
          "ipieca-shs3-a3",
          "ipieca-shs3-a4",
          "ipieca-shs4-c1",
          "ipieca-shs4-c2",
          "ipieca-shs4-c3",
          "ipieca-shs4-a1",
          "ipieca-shs4-a2",
          "ipieca-shs4-a3",
          "ipieca-shs4-a4",
          "ipieca-shs4-a5",
          "ipieca-shs4-a6",
          "ipieca-shs4-a7",
          "ipieca-shs4-a8",
          "ipieca-shs5-c1",
          "ipieca-shs5-c2",
          "ipieca-shs5-c3",
          "ipieca-shs5-a1",
          "ipieca-shs5-a2",
          "ipieca-shs5-a3",
          "ipieca-shs5-a4",
          "ipieca-shs6-c1",
          "ipieca-shs6-c2",
          "ipieca-shs6-c3",
          "ipieca-shs6-a1",
          "ipieca-shs6-a2",
          "ipieca-shs6-a3",
          "ipieca-shs6-a4",
          "ipieca-shs6-a5",
          "ipieca-shs6-a6",
          "ipieca-shs6-a7",
          "ipieca-shs7-c1",
          "ipieca-shs7-c2",
          "ipieca-shs7-c3",
          "ipieca-shs7-a1",
          "ipieca-shs7-a2",
          "ipieca-shs7-a3",
          "ipieca-soc1-c1",
          "ipieca-soc1-c2",
          "ipieca-soc1-c3",
          "ipieca-soc1-c4",
          "ipieca-soc1-c5",
          "ipieca-soc1-a1",
          "ipieca-soc1-a2",
          "ipieca-soc1-a3",
          "ipieca-soc2-c1",
          "ipieca-soc2-c2",
          "ipieca-soc2-a1",
          "ipieca-soc2-a2",
          "ipieca-soc2-a3",
          "ipieca-soc2-a4",
          "ipieca-soc3-c1",
          "ipieca-soc3-c2",
          "ipieca-soc3-c3",
          "ipieca-soc3-a1",
          "ipieca-soc3-a2",
          "ipieca-soc3-a3",
          "ipieca-soc3-a4",
          "ipieca-soc4-c1",
          "ipieca-soc4-c2",
          "ipieca-soc4-c3",
          "ipieca-soc4-a1",
          "ipieca-soc4-a2",
          "ipieca-soc4-a3",
          "ipieca-soc4-a4",
          "ipieca-soc4-a5",
          "ipieca-soc5-c1",
          "ipieca-soc5-c2",
          "ipieca-soc5-c3",
          "ipieca-soc5-a1",
          "ipieca-soc5-a2",
          "ipieca-soc5-a3",
          "ipieca-soc5-a4",
          "ipieca-soc5-c1",
          "ipieca-soc5-c2",
          "ipieca-soc5-a1",
          "ipieca-soc5-a2",
          "ipieca-soc5-a3",
          "ipieca-soc5-a4",
          "ipieca-soc6-c1",
          "ipieca-soc6-c2",
          "ipieca-soc6-a1",
          "ipieca-soc6-a2",
          "ipieca-soc6-a3",
          "ipieca-soc6-a4",
          "ipieca-soc7-c1",
          "ipieca-soc7-c2",
          "ipieca-soc7-a1",
          "ipieca-soc7-a2",
          "ipieca-soc8-c1",
          "ipieca-soc8-a1",
          "ipieca-soc8-a2",
          "ipieca-soc8-a3",
          "ipieca-soc8-a4",
          "ipieca-soc9-c1",
          "ipieca-soc9-c2",
          "ipieca-soc9-c3",
          "ipieca-soc9-a1",
          "ipieca-soc9-a2",
          "ipieca-soc10-c1",
          "ipieca-soc10-a1",
          "ipieca-soc10-a2",
          "ipieca-soc11-c1",
          "ipieca-soc11-c2",
          "ipieca-soc11-a1",
          "ipieca-soc11-a2",
          "ipieca-soc12-c1",
          "ipieca-soc12-c2",
          "ipieca-soc12-a1",
          "ipieca-soc12-a2",
          "ipieca-soc12-a3",
          "ipieca-soc13-c1",
          "ipieca-soc13-c2",
          "ipieca-soc13-a1",
          "ipieca-soc13-a2",
          "ipieca-soc13-a3",
          "ipieca-soc13-a4",
          "ipieca-soc13-a5",
          "ipieca-soc14-c1",
          "ipieca-soc14-a1",
          "ipieca-soc14-a2",
          "ipieca-soc14-a3",
          "ipieca-soc14-a4",
          "ipieca-soc14-a5",
          "ipieca-soc14-a6",
          "ipieca-soc14-a7",
          "ipieca-soc14-a8",
          "ipieca-soc15-c1",
          "ipieca-soc15-a1",
          "ipieca-soc15-a2",
          "ipieca-soc15-a3",
          "ipieca-soc15-a4"
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'Sustainable Finance Disclosure Regulation',
        groupData: {
          alternativeCode: 'sfdr'
        },
        utrCodes: [
          "sfdr/sfdr-1",
          "sfdr/sfdr-2",
          "sfdr/sfdr-3",
          "sfdr/sfdr-4",
          "sfdr/sfdr-5",
          "sfdr/sfdr-6",
          "sfdr/sfdr-7",
          "sfdr/sfdr-8",
          "sfdr/sfdr-9",
          "sfdr/sfdr-10",
          "sfdr/sfdr-11",
          "sfdr/sfdr-12",
          "sfdr/sfdr-13",
          "sfdr/sfdr-14",
          "sfdr/sfdr-15",
          "sfdr/sfdr-16",
          "sfdr/sfdr-17",
          "sfdr/sfdr-18",
          "sfdr/sfdr-19",
          "sfdr/sfdr-20",
          "sfdr/sfdr-21",
          "sfdr/sfdr-22"
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'Equal Employment Opportunity Commission',
        groupData: {
          alternativeCode: 'eeo'
        },
        utrCodes: [
          "eeo/company-information",
          "eeo/employee-break-down",
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'DJSI Corporate Sustainability Assessment (CSA)',
        groupData: {
          alternativeCode: 'sam_csa',
          colour: '#B0CA04',
        },
        utrCodes: [
          "sam-csa-info-denom-1",
          "sam-csa-info-denom-2",
          "sam-csa-info-denom-3",
          "sam-csa-info-denom-4",
          "sam-csa-eco-cg-1",
          "sam-csa-eco-cg-2",
          "sam-csa-eco-cg-3",
          "sam-csa-eco-cg-4",
          "sam-csa-eco-cg-5",
          "sam-csa-eco-cg-6",
          "sam-csa-eco-cg-7",
          "sam-csa-eco-cg-8",
          "sam-csa-eco-cg-9",
          "sam-csa-eco-cg-10",
          "sam-csa-eco-cg-11",
          "sam-csa-eco-cg-12",
          "sam-csa-eco-cg-13",
          "sam-csa-eco-cg-14",
          "sam-csa-eco-cg-15",
          "sam-csa-eco-cg-16",
          "sam-csa-eco-cg-17",
          "sam-csa-eco-cg-18",
          "sam-csa-eco-cg-19",
          "sam-csa-eco-cg-20",
          "sam-csa-eco-cg-21",
          "sam-csa-eco-cg-22",
          "sam-csa-eco-cg-23",
          "sam-csa-eco-cg-24",
          "sam-csa-eco-m-1",
          "sam-csa-eco-m-2",
          "sam-csa-eco-risk-1",
          "sam-csa-eco-risk-2",
          "sam-csa-eco-risk-3",
          "sam-csa-eco-risk-4",
          "sam-csa-eco-conduct-1",
          "sam-csa-eco-conduct-2",
          "sam-csa-eco-conduct-3",
          "sam-csa-eco-conduct-4",
          "sam-csa-eco-conduct-5",
          "sam-csa-eco-conduct-6",
          "sam-csa-eco-conduct-7",
          "sam-csa-eco-crm-1",
          "sam-csa-eco-crm-2",
          "sam-csa-eco-crm-3",
          "sam-csa-eco-crm-4",
          "sam-csa-eco-crm-5",
          "sam-csa-eco-pol-1",
          "sam-csa-eco-pol-2",
          "sam-csa-eco-pol-3",
          "sam-csa-eco-pol-4",
          "sam-csa-eco-pol-5",
          "sam-csa-eco-sc-1",
          "sam-csa-eco-sc-2",
          "sam-csa-eco-sc-3",
          "sam-csa-eco-sc-4",
          "sam-csa-eco-sc-5",
          "sam-csa-eco-sc-6",
          "sam-csa-eco-sc-7",
          "sam-csa-eco-sc-8",
          "sam-csa-eco-sc-9",
          "sam-csa-eco-sc-10",
          "sam-csa-eco-sc-11",
          "sam-csa-eco-sc-12",
          "sam-csa-eco-sc-13",
          "sam-csa-eco-sc-14",
          "sam-csa-eco-sc-15",
          "sam-csa-eco-sc-16",
          "sam-csa-eco-sc-17",
          "sam-csa-eco-bm-1",
          "sam-csa-eco-bm-2",
          "sam-csa-eco-bm-3",
          "sam-csa-eco-bm-4",
          "sam-csa-eco-tax-1",
          "sam-csa-eco-tax-2",
          "sam-csa-eco-tax-3",
          "sam-csa-eco-tax-4",
          "sam-csa-eco-tax-5",
          "sam-csa-eco-cyber-1",
          "sam-csa-eco-cyber-2",
          "sam-csa-eco-cyber-3",
          "sam-csa-eco-cyber-4",
          "sam-csa-eco-cyber-5",
          "sam-csa-eco-cyber-6",
          "sam-csa-eco-cyber-7",
          "sam-csa-eco-cyber-8",
          "sam-csa-eco-cyber-9",
          "sam-csa-eco-cyber-10",
          "sam-csa-eco-innovate-1",
          "sam-csa-eco-innovate-2",
          "sam-csa-eco-innovate-3",
          "sam-csa-eco-innovate-4",
          "sam-csa-eco-privacy-1",
          "sam-csa-eco-privacy-2",
          "sam-csa-eco-privacy-3",
          "sam-csa-eco-privacy-4",
          "sam-csa-env-reporting-1",
          "sam-csa-env-reporting-2",
          "sam-csa-env-reporting-3",
          "sam-csa-env-reporting-4",
          "sam-csa-env-systems-1",
          "sam-csa-env-systems-2",
          "sam-csa-env-systems-3",
          "sam-csa-env-systems-4",
          "sam-csa-env-systems-5",
          "sam-csa-env-ecoeff-1",
          "sam-csa-env-ecoeff-2",
          "sam-csa-env-ecoeff-3",
          "sam-csa-env-ecoeff-4",
          "sam-csa-env-ecoeff-5",
          "sam-csa-env-ecoeff-6",
          "sam-csa-env-ecoeff-7",
          "sam-csa-env-ecoeff-8",
          "sam-csa-env-ecoeff-9",
          "sam-csa-env-ecoeff-10",
          "sam-csa-env-ecoeff-11",
          "sam-csa-env-ecoeff-12",
          "sam-csa-env-ecoeff-13",
          "sam-csa-env-ecoeff-14",
          "sam-csa-env-ecoeff-15",
          "sam-csa-env-ecoeff-16",
          "sam-csa-env-ecoeff-17",
          "sam-csa-env-ecoeff-18",
          "sam-csa-env-ecoeff-19",
          "sam-csa-env-ecoeff-20",
          "sam-csa-env-ecoeff-21",
          "sam-csa-env-ecoeff-22",
          "sam-csa-env-ecoeff-23",
          "sam-csa-env-ecoeff-24",
          "sam-csa-env-ecoeff-25",
          "sam-csa-env-ecoeff-26",
          "sam-csa-env-ecoeff-27",
          "sam-csa-env-ecoeff-28",
          "sam-csa-env-ecoeff-29",
          "sam-csa-env-prodstew-1",
          "sam-csa-env-prodstew-2",
          "sam-csa-env-prodstew-3",
          "sam-csa-env-prodstew-4",
          "sam-csa-env-prodstew-5",
          "sam-csa-env-climatestrat-1",
          "sam-csa-env-climatestrat-2",
          "sam-csa-env-climatestrat-3",
          "sam-csa-env-climatestrat-4",
          "sam-csa-env-climatestrat-5",
          "sam-csa-env-climatestrat-6",
          "sam-csa-env-packaging-1",
          "sam-csa-env-packaging-2",
          "sam-csa-env-packaging-3",
          "sam-csa-env-packaging-4",
          "sam-csa-env-packaging-5",
          "sam-csa-env-packaging-6",
          "sam-csa-env-packaging-7",
          "sam-csa-env-packaging-8",
          "sam-csa-env-packaging-9",
          "sam-csa-env-packaging-10",
          "sam-csa-env-packaging-11",
          "sam-csa-soc-rep-1",
          "sam-csa-soc-rep-2",
          "sam-csa-soc-rep-3",
          "sam-csa-soc-labor-1",
          "sam-csa-soc-labor-2",
          "sam-csa-soc-labor-3",
          "sam-csa-soc-labor-4",
          "sam-csa-soc-labor-5",
          "sam-csa-soc-labor-6",
          "sam-csa-soc-labor-7",
          "sam-csa-soc-labor-8",
          "sam-csa-soc-labor-9",
          "sam-csa-soc-labor-10",
          "sam-csa-soc-labor-11",
          "sam-csa-soc-human-1",
          "sam-csa-soc-human-2",
          "sam-csa-soc-human-3",
          "sam-csa-soc-human-4",
          "sam-csa-soc-human-5",
          "sam-csa-soc-hcapdev-1",
          "sam-csa-soc-hcapdev-2",
          "sam-csa-soc-hcapdev-3",
          "sam-csa-soc-hcapdev-4",
          "sam-csa-soc-hcapdev-5",
          "sam-csa-soc-hcapdev-6",
          "sam-csa-soc-hcapdev-7",
          "sam-csa-soc-hcapdev-8",
          "sam-csa-soc-hcapdev-9",
          "sam-csa-soc-hcapdev-10",
          "sam-csa-soc-talent-1",
          "sam-csa-soc-talent-2",
          "sam-csa-soc-talent-3",
          "sam-csa-soc-talent-4",
          "sam-csa-soc-talent-5",
          "sam-csa-soc-talent-6",
          "sam-csa-soc-talent-7",
          "sam-csa-soc-philanth-1",
          "sam-csa-soc-philanth-2",
          "sam-csa-soc-philanth-3",
          "sam-csa-soc-philanth-4",
          "sam-csa-soc-hs-1",
          "sam-csa-soc-hs-2",
          "sam-csa-soc-hs-3",
          "sam-csa-soc-hs-4",
          "sam-csa-soc-hs-5",
          "sam-csa-soc-hs-6",
          "sam-csa-soc-hs-7",
          "sam-csa-soc-hs-8",
          "sam-csa-soc-hs-9",
          "sam-csa-soc-hs-10"
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'Vigeo Eiris ESG Assessment',
        groupData: {
          alternativeCode: 'vigeo_eiris',
        },
        utrCodes: [
          "vigeo-eiris/gi-i-1",
          "vigeo-eiris/gi-i-2",
          "vigeo-eiris/gi-i-3",
          "vigeo-eiris/gi-i-4",
          "vigeo-eiris/gi-i-5",
          "vigeo-eiris/gi-i-6",
          "vigeo-eiris/gi-i-7",
          "vigeo-eiris/gi-i-8",
          "vigeo-eiris/gi-i-9",
          "vigeo-eiris/gi-i-10",
          "vigeo-eiris/bbd-ps-1",
          "vigeo-eiris/bbd-ps-2",
          "vigeo-eiris/bbd-ps-3",
          "vigeo-eiris/bbd-ps-4",
          "vigeo-eiris/bbd-ps-5",
          "vigeo-eiris/bbd-ps-6",
          "vigeo-eiris/bbd-ps-7",
          "vigeo-eiris/bbd-ps-8",
          "vigeo-eiris/bbd-ps-9",
          "vigeo-eiris/bbd-ps-10",
          "vigeo-eiris/bbd-ps-11",
          "vigeo-eiris/bbd-ps-12",
          "vigeo-eiris/bbd-ps-13",
          "vigeo-eiris/bbd-ps-14",
          "vigeo-eiris/bbd-ps-15",
          "vigeo-eiris/bbd-info-1",
          "vigeo-eiris/bbd-info-2",
          "vigeo-eiris/bbd-info-3",
          "vigeo-eiris/bbd-info-4",
          "vigeo-eiris/bbd-info-5",
          "vigeo-eiris/bbd-info-6",
          "vigeo-eiris/bbd-info-7",
          "vigeo-eiris/bbd-env-sc-1",
          "vigeo-eiris/bbd-env-sc-2",
          "vigeo-eiris/bbd-env-sc-3",
          "vigeo-eiris/bbd-env-sc-4",
          "vigeo-eiris/bbd-env-sc-5",
          "vigeo-eiris/bbd-env-sc-6",
          "vigeo-eiris/bbd-env-sc-7",
          "vigeo-eiris/bbd-env-sc-8",
          "vigeo-eiris/bbd-env-sc-9",
          "vigeo-eiris/bbd-env-sc-10",
          "vigeo-eiris/bbd-ls-sc-1",
          "vigeo-eiris/bbd-ls-sc-2",
          "vigeo-eiris/bbd-ls-sc-3",
          "vigeo-eiris/bbd-ls-sc-4",
          "vigeo-eiris/bbd-ls-sc-5",
          "vigeo-eiris/bbd-ls-sc-6",
          "vigeo-eiris/bbd-ls-sc-7",
          "vigeo-eiris/bbd-ls-sc-8",
          "vigeo-eiris/bbd-ls-sc-9",
          "vigeo-eiris/bbd-ls-sc-10",
          "vigeo-eiris/bbd-ls-sc-11",
          "vigeo-eiris/bbd-ls-sc-12",
          "vigeo-eiris/bbd-ls-sc-13",
          "vigeo-eiris/bbd-c-1",
          "vigeo-eiris/bbd-c-2",
          "vigeo-eiris/bbd-c-3",
          "vigeo-eiris/bbd-c-4",
          "vigeo-eiris/bbd-c-5",
          "vigeo-eiris/bbd-c-6",
          "vigeo-eiris/bbd-c-7",
          "vigeo-eiris/bbd-c-8",
          "vigeo-eiris/bbd-c-9",
          "vigeo-eiris/bbd-c-10",
          "vigeo-eiris/bbd-c-11",
          "vigeo-eiris/bbd-c-12",
          "vigeo-eiris/bbd-anti-1",
          "vigeo-eiris/bbd-anti-2",
          "vigeo-eiris/bbd-anti-3",
          "vigeo-eiris/bbd-anti-4",
          "vigeo-eiris/bbd-anti-5",
          "vigeo-eiris/bbd-anti-6",
          "vigeo-eiris/bbd-anti-7",
          "vigeo-eiris/bbd-anti-8",
          "vigeo-eiris/bbd-anti-9",
          "vigeo-eiris/bbd-anti-10",
          "vigeo-eiris/bbd-rl-1",
          "vigeo-eiris/bbd-rl-2",
          "vigeo-eiris/bbd-rl-3",
          "vigeo-eiris/bbd-rl-4",
          "vigeo-eiris/bbd-rl-5",
          "vigeo-eiris/bbd-rl-6",
          "vigeo-eiris/bbd-rl-7",
          "vigeo-eiris/bbd-rl-8",
          "vigeo-eiris/bbd-rl-9",
          "vigeo-eiris/bbd-rl-10",
          "vigeo-eiris/hr-fhr-1",
          "vigeo-eiris/hr-fhr-2",
          "vigeo-eiris/hr-fhr-3",
          "vigeo-eiris/hr-fhr-4",
          "vigeo-eiris/hr-fhr-5",
          "vigeo-eiris/hr-flr-1",
          "vigeo-eiris/hr-flr-2",
          "vigeo-eiris/hr-flr-3",
          "vigeo-eiris/hr-flr-4",
          "vigeo-eiris/hr-flr-5",
          "vigeo-eiris/hr-flr-6",
          "vigeo-eiris/hr-flr-7",
          "vigeo-eiris/hr-flr-8",
          "vigeo-eiris/hr-ndd-1",
          "vigeo-eiris/hr-ndd-2",
          "vigeo-eiris/hr-ndd-3",
          "vigeo-eiris/hr-ndd-4",
          "vigeo-eiris/hr-ndd-5",
          "vigeo-eiris/hr-ndd-6",
          "vigeo-eiris/hr-ndd-7",
          "vigeo-eiris/hr-ndd-8",
          "vigeo-eiris/hr-ndd-9",
          "vigeo-eiris/hr-ndd-10",
          "vigeo-eiris/env-strat-1",
          "vigeo-eiris/env-strat-2",
          "vigeo-eiris/env-strat-3",
          "vigeo-eiris/env-strat-4",
          "vigeo-eiris/env-strat-5",
          "vigeo-eiris/env-ap-1",
          "vigeo-eiris/env-ap-2",
          "vigeo-eiris/env-ap-3",
          "vigeo-eiris/env-ap-4",
          "vigeo-eiris/env-ap-5",
          "vigeo-eiris/env-at-1",
          "vigeo-eiris/env-at-2",
          "vigeo-eiris/env-at-3",
          "vigeo-eiris/env-at-4",
          "vigeo-eiris/env-at-5",
          "vigeo-eiris/env-at-6",
          "vigeo-eiris/env-w-1",
          "vigeo-eiris/env-w-2",
          "vigeo-eiris/env-w-3",
          "vigeo-eiris/env-w-4",
          "vigeo-eiris/env-w-5",
          "vigeo-eiris/env-w-6",
          "vigeo-eiris/env-w-7",
          "vigeo-eiris/env-w-8",
          "vigeo-eiris/env-w-9",
          "vigeo-eiris/env-w-10",
          "vigeo-eiris/env-w-11",
          "vigeo-eiris/env-w-12",
          "vigeo-eiris/env-w-13",
          "vigeo-eiris/env-w-14",
          "vigeo-eiris/env-w-15",
          "vigeo-eiris/env-w-16",
          "vigeo-eiris/env-w-17",
          "vigeo-eiris/env-w-18",
          "vigeo-eiris/env-w-19",
          "vigeo-eiris/env-w-20",
          "vigeo-eiris/env-w-21",
          "vigeo-eiris/env-e-1",
          "vigeo-eiris/env-e-2",
          "vigeo-eiris/env-e-3",
          "vigeo-eiris/env-e-4",
          "vigeo-eiris/env-e-5",
          "vigeo-eiris/env-e-6",
          "vigeo-eiris/env-e-7",
          "vigeo-eiris/env-e-8",
          "vigeo-eiris/env-e-9",
          "vigeo-eiris/env-e-10",
          "vigeo-eiris/env-e-11",
          "vigeo-eiris/env-e-12",
          "vigeo-eiris/env-e-13",
          "vigeo-eiris/env-e-14",
          "vigeo-eiris/env-e-15",
          "vigeo-eiris/env-e-16",
          "vigeo-eiris/env-e-17",
          "vigeo-eiris/env-e-18",
          "vigeo-eiris/env-e-19",
          "vigeo-eiris/env-t-1",
          "vigeo-eiris/env-t-2",
          "vigeo-eiris/env-t-3",
          "vigeo-eiris/env-t-4",
          "vigeo-eiris/env-t-5",
          "vigeo-eiris/env-t-6",
          "vigeo-eiris/env-t-7",
          "vigeo-eiris/env-t-8",
          "vigeo-eiris/env-t-9",
          "vigeo-eiris/env-t-10",
          "vigeo-eiris/env-t-11",
          "vigeo-eiris/env-ecod-1",
          "vigeo-eiris/env-ecod-2",
          "vigeo-eiris/env-ecod-3",
          "vigeo-eiris/env-ecod-4",
          "vigeo-eiris/env-ecod-5",
          "vigeo-eiris/env-ecod-6",
          "vigeo-eiris/env-ecod-7",
          "vigeo-eiris/env-ecod-8",
          "vigeo-eiris/env-ecod-9",
          "vigeo-eiris/env-ecod-10",
          "vigeo-eiris/com-dev-1",
          "vigeo-eiris/com-dev-2",
          "vigeo-eiris/com-dev-3",
          "vigeo-eiris/com-dev-4",
          "vigeo-eiris/com-dev-5",
          "vigeo-eiris/com-dev-6",
          "vigeo-eiris/com-dev-7",
          "vigeo-eiris/com-dev-8",
          "vigeo-eiris/com-dev-9",
          "vigeo-eiris/com-dev-10",
          "vigeo-eiris/com-dev-11",
          "vigeo-eiris/com-dev-12",
          "vigeo-eiris/gov-board-1",
          "vigeo-eiris/gov-board-2",
          "vigeo-eiris/gov-board-3",
          "vigeo-eiris/gov-board-4",
          "vigeo-eiris/gov-board-5",
          "vigeo-eiris/gov-board-6",
          "vigeo-eiris/gov-board-7",
          "vigeo-eiris/gov-board-8",
          "vigeo-eiris/gov-board-9",
          "vigeo-eiris/gov-board-10",
          "vigeo-eiris/gov-board-11",
          "vigeo-eiris/gov-board-12",
          "vigeo-eiris/gov-board-13",
          "vigeo-eiris/gov-board-14",
          "vigeo-eiris/gov-board-15",
          "vigeo-eiris/gov-board-16",
          "vigeo-eiris/gov-board-17",
          "vigeo-eiris/gov-board-18",
          "vigeo-eiris/gov-board-19",
          "vigeo-eiris/gov-board-20",
          "vigeo-eiris/gov-audit-1",
          "vigeo-eiris/gov-audit-2",
          "vigeo-eiris/gov-audit-3",
          "vigeo-eiris/gov-audit-4",
          "vigeo-eiris/gov-audit-5",
          "vigeo-eiris/gov-audit-6",
          "vigeo-eiris/gov-audit-7",
          "vigeo-eiris/gov-audit-8",
          "vigeo-eiris/gov-audit-9",
          "vigeo-eiris/gov-sh-1",
          "vigeo-eiris/gov-sh-2",
          "vigeo-eiris/gov-sh-3",
          "vigeo-eiris/gov-sh-4",
          "vigeo-eiris/gov-sh-5",
          "vigeo-eiris/gov-sh-6",
          "vigeo-eiris/gov-sh-7",
          "vigeo-eiris/gov-sh-8",
          "vigeo-eiris/gov-sh-9",
          "vigeo-eiris/gov-sh-10",
          "vigeo-eiris/gov-sh-11",
          "vigeo-eiris/gov-sh-12",
          "vigeo-eiris/gov-sh-13",
          "vigeo-eiris/gov-sh-14",
          "vigeo-eiris/gov-sh-15",
          "vigeo-eiris/gov-sh-16",
          "vigeo-eiris/gov-sh-17",
          "vigeo-eiris/gov-execr-1",
          "vigeo-eiris/gov-execr-2",
          "vigeo-eiris/gov-execr-3",
          "vigeo-eiris/gov-execr-4",
          "vigeo-eiris/gov-execr-5",
          "vigeo-eiris/gov-execr-6",
          "vigeo-eiris/gov-execr-7",
          "vigeo-eiris/gov-execr-8",
          "vigeo-eiris/gov-execr-9",
          "vigeo-eiris/gov-execr-10",
          "vigeo-eiris/gov-execr-11",
          "vigeo-eiris/gov-execr-12",
          "vigeo-eiris/gov-execr-13",
          "vigeo-eiris/gov-execr-14",
          "vigeo-eiris/hr-dia-1",
          "vigeo-eiris/hr-dia-2",
          "vigeo-eiris/hr-dia-3",
          "vigeo-eiris/hr-dia-4",
          "vigeo-eiris/hr-dia-5",
          "vigeo-eiris/hr-dia-6",
          "vigeo-eiris/hr-dia-7",
          "vigeo-eiris/hr-dia-8",
          "vigeo-eiris/hr-dia-9",
          "vigeo-eiris/hr-dia-10",
          "vigeo-eiris/hr-reorg-1",
          "vigeo-eiris/hr-reorg-2",
          "vigeo-eiris/hr-reorg-3",
          "vigeo-eiris/hr-reorg-4",
          "vigeo-eiris/hr-reorg-5",
          "vigeo-eiris/hr-reorg-6",
          "vigeo-eiris/hr-reorg-7",
          "vigeo-eiris/hr-reorg-8",
          "vigeo-eiris/hr-reorg-9",
          "vigeo-eiris/hr-career-1",
          "vigeo-eiris/hr-career-2",
          "vigeo-eiris/hr-career-3",
          "vigeo-eiris/hr-career-4",
          "vigeo-eiris/hr-career-5",
          "vigeo-eiris/hr-career-6",
          "vigeo-eiris/hr-career-7",
          "vigeo-eiris/hr-career-8",
          "vigeo-eiris/hr-career-9",
          "vigeo-eiris/hr-career-10",
          "vigeo-eiris/hr-career-11",
          "vigeo-eiris/hr-career-12",
          "vigeo-eiris/hr-career-13",
          "vigeo-eiris/hr-career-14",
          "vigeo-eiris/hr-career-15",
          "vigeo-eiris/hr-career-16",
          "vigeo-eiris/hr-career-17",
          "vigeo-eiris/hr-career-18",
          "vigeo-eiris/hr-hs-1",
          "vigeo-eiris/hr-hs-2",
          "vigeo-eiris/hr-hs-3",
          "vigeo-eiris/hr-hs-4",
          "vigeo-eiris/hr-hs-5",
          "vigeo-eiris/hr-hs-6",
          "vigeo-eiris/hr-hs-7",
          "vigeo-eiris/hr-hs-8",
          "vigeo-eiris/hr-hs-9",
          "vigeo-eiris/hr-hs-10",
          "vigeo-eiris/hr-hs-11",
          "vigeo-eiris/hr-hs-12",
          "vigeo-eiris/add-employ-1",
          "vigeo-eiris/add-employ-2",
          "vigeo-eiris/add-employ-3",
          "vigeo-eiris/add-employ-4",
          "vigeo-eiris/add-employ-5",
          "vigeo-eiris/add-employ-6",
          "vigeo-eiris/add-employ-7",
          "vigeo-eiris/add-employ-8",
          "vigeo-eiris/add-wc-1",
          "vigeo-eiris/add-wc-2",
          "vigeo-eiris/add-wc-3",
          "vigeo-eiris/add-dia-1",
          "vigeo-eiris/add-dia-2",
          "vigeo-eiris/add-dia-3",
          "vigeo-eiris/add-dia-4",
          "vigeo-eiris/add-dia-5",
          "vigeo-eiris/add-dia-6",
          "vigeo-eiris/add-beh-1",
          "vigeo-eiris/add-beh-2",
          "vigeo-eiris/add-beh-3",
          "vigeo-eiris/add-beh-4",
          "vigeo-eiris/add-phys-impacts-1",
          "vigeo-eiris/add-phys-impacts-2",
          "vigeo-eiris/add-phys-impacts-3",
          "vigeo-eiris/add-phys-impacts-4",
          "vigeo-eiris/add-phys-impacts-5",
          "vigeo-eiris/add-phys-impacts-6",
          "vigeo-eiris/add-phys-impacts-7",
          "vigeo-eiris/add-cc-strat-1",
          "vigeo-eiris/add-cc-strat-2",
          "vigeo-eiris/add-cc-strat-3",
          "vigeo-eiris/add-cc-strat-4",
          "vigeo-eiris/add-cc-strat-5",
          "vigeo-eiris/add-cc-strat-6",
          "vigeo-eiris/add-cc-strat-7",
          "vigeo-eiris/add-cc-strat-8",
          "vigeo-eiris/add-cc-strat-9",
          "vigeo-eiris/add-cc-strat-10"
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 102: General Disclosures',
        groupData: {
          colour: '#335377',
          link: getLinkPath('gri-102-general-disclosures-2016.pdf'),
          icon: griIcon,
          alternativeCode: 'gri'
        },
        utrCodes: [
          'gri/2020/102-1',
          'gri/2020/102-2/a',
          'gri/2020/102-2/b',
          'gri/2020/102-3',
          'gri/2020/102-4',
          'gri/2020/102-5',
          'gri/2020/102-6',
          'gri/2020/102-7/a',
          'gri/2020/102-8/a',
          'gri/2020/102-8/b',
          'gri/2020/102-8/c',
          'gri/2020/102-8/d',
          'gri/2020/102-8/e',
          'gri/2020/102-8/f',
          'gri/2020/102-9',
          'gri/2020/102-10',
          'gri/2020/102-11',
          'gri/2020/102-12',
          'gri/2020/102-13',
          'gri/2020/102-14',
          'gri/2020/102-15',
          'gri/2020/102-16',
          'gri/2020/102-17',
          'gri/2020/102-18',
          'gri/2020/102-19',
          'gri/2020/102-20',
          'gri/2020/102-21',
          'gri/2020/102-22',
          'gri/2020/102-23',
          'gri/2020/102-24/a',
          'gri/2020/102-24/b',
          'gri/2020/102-25/a',
          'gri/2020/102-25/b',
          'gri/2020/102-26',
          'gri/2020/102-27',
          'gri/2020/102-28',
          'gri/2020/102-29/a',
          'gri/2020/102-29/b',
          'gri/2020/102-30',
          'gri/2020/102-31',
          'gri/2020/102-32',
          'gri/2020/102-33',
          'gri/2020/102-34/a',
          'gri/2020/102-34/b',
          'gri/2020/102-35/a',
          'gri/2020/102-35/b',
          'gri/2020/102-36',
          'gri/2020/102-37',
          'gri/2020/102-38',
          'gri/2020/102-39',
          'gri/2020/102-40',
          'gri/2020/102-41',
          'gri/2020/102-42',
          'gri/2020/102-43',
          'gri/2020/102-44',
          'gri/2020/102-45',
          'gri/2020/102-46',
          'gri/2020/102-47',
          'gri/2020/102-48',
          'gri/2020/102-49',
          'gri/2020/102-50',
          'gri/2020/102-51',
          'gri/2020/102-52',
          'gri/2020/102-53',
          'gri/2020/102-54',
          'gri/2020/102-55/a',
          'gri/2020/102-55/b',
          'gri/2020/102-56/a',
          'gri/2020/102-56/b',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 103: Management Approach',
        groupData: {
          colour: '#335377',
          link: getLinkPath('gri-103-management-approach-2016.pdf'),
          icon: griIcon,
          alternativeCode: 'gri'
        },
        utrCodes: [
          'gri/2020/103-1/a',
          'gri/2020/103-1/b',
          'gri/2020/103-1/c',
          'gri/2020/103-2/a',
          'gri/2020/103-2/b',
          'gri/2020/103-2/c',
          'gri/2020/103-3',
        ],
        fragmentUtrConfiguration: {
        },
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 201: Economic Performance',
        groupData: {
          colour: '#72327c',
          link: getLinkPath('gri-201-economic-performance-2016.pdf'),
          icon: griIcon,
          alternativeCode: 'gri'
        },
        utrCodes: [
          'gri/2020/201-1/a',
          'survey/gri/2020/201-1/a/baseline',
          'gri/2020/201-1/b',
          'gri/2020/201-2/a',
          'gri/2020/201-3/a',
          'gri/2020/201-3/b',
          'gri/2020/201-3/c',
          'gri/2020/201-3/d',
          'gri/2020/201-3/e',
          'gri/2020/201-4/a',
          'gri/2020/201-4/b',
          'gri/2020/201-4/c',
        ],
        fragmentUtrConfiguration: {
          'gri/2020/306-4/a': [vc.ProductServices, vc.Operations]
        },
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 202: Market Presence',
        groupData: {
          colour: '#72327c',
          link: getLinkPath('gri-202-market-presence-2016.pdf'),
          icon: griIcon,
          alternativeCode: 'gri'
        },
        utrCodes: [
          'gri/2020/202-1/a',
          'gri/2020/202-1/b',
          'gri/2020/202-1/c',
          'gri/2020/202-1/d',
          'gri/2020/202-2',
          'gri/2020/202-2/b',
          'gri/2020/202-2/c',
          'gri/2020/202-2/d',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 203: Indirect Economic Impacts',
        groupData: {
          colour: '#72327c',
          link: getLinkPath('gri-203-indirect-economic-impacts-2016.pdf'),
          icon: griIcon,
          alternativeCode: 'gri'
        },
        utrCodes: [
          'gri/2020/203-1/a',
          'gri/2020/203-1/b',
          'gri/2020/203-1/c',
          'gri/2020/203-2/a',
          'gri/2020/203-2/b',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 204: Procurement Practices',
        groupData: {
          colour: '#72327c',
          link: getLinkPath('gri-204-procurement-practices-2016.pdf'),
          icon: griIcon,
          alternativeCode: 'gri'
        },
        utrCodes: [
          'gri/2020/204-1/a',
          'gri/2020/204-1/b',
          'gri/2020/204-1/c',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 205: Anti-Corruption',
        groupData: {
          colour: '#72327c',
          link: getLinkPath('gri-205-anti-corruption-2016.pdf'),
          icon: griIcon,
          alternativeCode: 'gri'
        },
        utrCodes: [
          'gri/2020/205-1/a',
          'gri/2020/205-1/b',
          'gri/2020/205-2/a',
          'gri/2020/205-2/b',
          'gri/2020/205-2/c',
          'gri/2020/205-2/d',
          'gri/2020/205-2/e',
          'gri/2020/205-3/a',
          'gri/2020/205-3/b',
          'gri/2020/205-3/c',
          'gri/2020/205-3/d',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 206: Anti-competitive Behavior',
        groupData: {
          colour: '#72327c',
          link: getLinkPath('gri-206-anti-competitive-behavior-2016.pdf'),
          icon: griIcon,
          alternativeCode: 'gri'
        },
        utrCodes: [
          'gri/2020/206-1/a',
          'gri/2020/206-1/b',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 207: Tax',
        groupData: {
          colour: '#72327c',
          link: getLinkPath('gri-207-tax-2019.pdf'),
          icon: griIcon,
          alternativeCode: 'gri'
        },
        utrCodes: [
          'gri/2020/207-1',
          'gri/2020/207-2/a',
          'gri/2020/207-2/b',
          'gri/2020/207-2/c',
          'gri/2020/207-3',
          'gri/2020/207-4/a',
          'gri/2020/207-4/b',
          'gri/2020/207-4/c',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 301: Materials',
        groupData: {
          colour: '#17956d',
          link: getLinkPath('gri-301-materials-2016.pdf'),
          icon: griIcon,
          alternativeCode: 'gri'
        },
        utrCodes: [
          'gri/2020/301-1',
          'gri/2020/301-2',
          'gri/2020/301-3',
          'gri/2020/301-3/b',
        ]
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 302: Energy',
        groupData: {
          colour: '#17956d',
          link: getLinkPath('gri-302-energy-2016.pdf'),
          icon: griIcon,
          alternativeCode: 'gri'
        },
        utrCodes: [
          'gri/2020/302-1/a',
          'gri/2020/302-1/b',
          'gri/2020/302-1/c',
          'gri/2020/302-1/d',
          'gri/2020/302-1/e',
          'gri/2020/302-1/e/baseline',
          'gri/2020/302-1/f',
          'gri/2020/302-1/g',
          'gri/2020/302-2',
          'gri/2020/302-2/b',
          'gri/2020/302-2/c',
          // 'survey/generic/intensity-water-energy-ghg',
          'gri/2020/302-3/a',
          'gri/2020/302-3/b',
          'gri/2020/302-3/c',
          'gri/2020/302-3/d',
          'gri/2020/302-4/a',
          'gri/2020/302-4/b',
          'gri/2020/302-4/c',
          'gri/2020/302-4/d',
          'gri/2020/302-5',
          'gri/2020/302-5/b',
          'gri/2020/302-5/c',
        ],
        fragmentUtrConfiguration: {
          'gri/2020/302-2': [vc.ProductServices, vc.ExternalCsr],
          'gri/2020/302-1/d': vc.allValueChain,
          'gri/2020/302-1/c': vc.allValueChain
        },

      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 303: Water and Effluents',
        groupData: {
          colour: '#17956d',
          link: getLinkPath('gri-303-water-and-effluents-2018.pdf'),
          icon: griIcon,
          alternativeCode: 'gri'
        },
        utrCodes: [
          'gri/2020/303-1/a',
          'gri/2020/303-1/b',
          'gri/2020/303-1/c',
          'gri/2020/303-1/d',
          'gri/2020/303-2',
          'gri/2020/303-3/a',
          'gri/2020/303-3/b',
          'gri/2020/303-3/c',
          'gri/2020/303-3/d',
          'gri/2020/303-4/a',
          'gri/2020/303-4/b',
          'gri/2020/303-4/c',
          'gri/2020/303-4/d',
          'gri/2020/303-4/e',
          'gri/2020/303-5/a',
          'gri/2020/303-5/b',
          'gri/2020/303-5/c',
          'gri/2020/303-5/d',
        ],
        fragmentUtrConfiguration: {
          'gri/2020/303-5/c': vc.allValueChain,
          'gri/2020/303-5/b': vc.allValueChain,
          'gri/2020/303-5/a': vc.allValueChain,
          'gri/2020/303-4/c': vc.allValueChain,
          'gri/2020/303-4/b': vc.allValueChain,


        },
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 304: Biodiversity',
        groupData: {
          colour: '#17956d',
          link: getLinkPath('gri-304-biodiversity-2016.pdf'),
          icon: griIcon,
          alternativeCode: 'gri'
        },
        utrCodes: [
          'gri/2020/304-1/a',
          'gri/2020/304-2/a',
          'gri/2020/304-2/b',
          'gri/2020/304-3/a',
          'gri/2020/304-3/b',
          'gri/2020/304-3/c',
          'gri/2020/304-3/d',
          'gri/2020/304-4',
        ]
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 305: Emissions',
        groupData: {
          colour: '#17956d',
          link: getLinkPath('gri-305-emissions-2016.pdf'),
          icon: griIcon,
          alternativeCode: 'gri'
        },
        utrCodes: [
          'gri/2020/305-1/a',
          'gri/2020/305-1/b',
          'gri/2020/305-1/c',
          'gri/2020/305-1/d',
          'gri/2020/305-1/e',
          'gri/2020/305-1/f',
          'gri/2020/305-1/g',
          'gri/2020/305-2/a',
          'gri/2020/305-2/b',
          'gri/2020/305-2/c',
          'gri/2020/305-2/d',
          'gri/2020/305-2/e',
          'gri/2020/305-2/f',
          'gri/2020/305-2/g',
          'gri/2020/305-3/a',
          'gri/2020/305-3/b',
          'gri/2020/305-3/c',
          'gri/2020/305-3/d',
          'gri/2020/305-3/e',
          'gri/2020/305-3/f',
          'gri/2020/305-3/g',
          // 'survey/generic/intensity-water-energy-ghg',
          'gri/2020/305-4/a',
          'gri/2020/305-4/b',
          'gri/2020/305-4/c',
          'gri/2020/305-4/d',
          'gri/2020/305-5/a',
          'gri/2020/305-5/b',
          'gri/2020/305-5/c',
          'gri/2020/305-5/d',
          'gri/2020/305-5/e',
          'gri/2020/305-6',
          'gri/2020/305-6/b',
          'gri/2020/305-6/c',
          'gri/2020/305-6/d',
          'gri/2020/305-7/a',
          'gri/2020/305-7/b',
          'gri/2020/305-7/c',
        ],
        fragmentUtrConfiguration: {
          'gri/2020/305-6': vc.allValueChain,
          'gri/2020/305-5/a': vc.allValueChain,
          'gri/2020/305-3/c': vc.allValueChain,
          'gri/2020/305-1/c': vc.allValueChain,
        },
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 306: Waste',
        groupData: {
          colour: '#17956d',
          link: getLinkPath('gri-306-waste-2020.pdf'),
          icon: griIcon,
          alternativeCode: 'gri'
        },
        utrCodes: [
          'gri/2020/306-1/a',
          'gri/2020/306-2/a',
          'gri/2020/306-2/b',
          'gri/2020/306-2/c',
          'gri/2020/306-3/a',
          'gri/2020/306-3/b',
          'gri/2020/306-4/a',
          'gri/2020/306-4/b',
          'gri/2020/306-4/c',
          'gri/2020/306-4/d',
          'gri/2020/306-4/e',
          'gri/2020/306-5/a',
          'gri/2020/306-5/b',
          'gri/2020/306-5/c',
          'gri/2020/306-5/d',
          'gri/2020/306-5/e',
        ],
        fragmentUtrConfiguration: {
          'gri/2020/201-3/a': vc.allValueChain,
          'gri/2020/306-2/b': [vc.ProductServices, vc.Operations],
          'gri/2020/306-2/a': [vc.ProductServices, vc.Operations],
          'gri/2020/306-1/a': vc.allValueChain,
        },

      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 307: Environmental Compliance',
        groupData: {
          colour: '#17956d',
          link: getLinkPath('gri-307-environmental-compliance-2016.pdf'),
          icon: griIcon,
          alternativeCode: 'gri'
        },
        utrCodes: [
          'gri/2020/307-1/a',
          'gri/2020/307-1/b',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 308: Supplier Environmental Assessment',
        groupData: {
          colour: '#17956d',
          link: getLinkPath('gri-308-supplier-environmental-assessment-2016.pdf'),
          icon: griIcon,
          alternativeCode: 'gri'
        },
        utrCodes: [
          'gri/2020/308-1',
          'gri/2020/308-2/a',
          'gri/2020/308-2/b',
          'gri/2020/308-2/c',
          'gri/2020/308-2/d',
          'gri/2020/308-2/e',
        ]
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 401: Employment',
        groupData: {
          colour: '#fa6a01',
          link: getLinkPath('gri-401-employment-2016.pdf'),
          icon: griIcon,
          alternativeCode: 'gri'
        },
        utrCodes: [
          'gri/2020/401-1/a',
          'gri/2020/401-1/b',
          'gri/2020/401-2',
          'gri/2020/401-2/b',
          'gri/2020/401-3/a',
          'gri/2020/401-3/b',
          'gri/2020/401-3/c',
          'gri/2020/401-3/d',
          'gri/2020/401-3/e',
        ]
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 402: Labor/Management Relations',
        groupData: {
          colour: '#fa6a01',
          link: getLinkPath('gri-402-labor-management-relations-2016.pdf'),
          icon: griIcon,
          alternativeCode: 'gri'
        },
        utrCodes: [
          'gri/2020/402-1/a',
          'gri/2020/402-1/b',
        ]
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 403: Occupational Health and Safety',
        groupData: {
          colour: '#fa6a01',
          link: getLinkPath('gri-403-occupational-health-and-safety-2018.pdf'),
          icon: griIcon,
          alternativeCode: 'gri'
        },
        utrCodes: [
          'gri/2020/403-1/a',
          'gri/2020/403-1/b',
          'gri/2020/403-2/a',
          'gri/2020/403-2/b',
          'gri/2020/403-2/c',
          'gri/2020/403-2/d',
          'gri/2020/403-3',
          'gri/2020/403-4/a',
          'gri/2020/403-4/b',
          'gri/2020/403-5',
          'gri/2020/403-6/a',
          'gri/2020/403-6/b',
          'gri/2020/403-7',
          'gri/2020/403-8/a',
          'gri/2020/403-8/b',
          'gri/2020/403-8/c',
          'gri/2020/403-9/a',
          'gri/2020/403-9/b',
          'gri/2020/403-9/c',
          'gri/2020/403-9/d',
          'gri/2020/403-9/e',
          'gri/2020/403-9/f',
          'gri/2020/403-9/g',
          'gri/2020/403-10/a',
          'gri/2020/403-10/b',
          'gri/2020/403-10/c',
          'gri/2020/403-10/d',
          'gri/2020/403-10/e',
        ]
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 404: Training and Education',
        groupData: {
          colour: '#fa6a01',
          link: getLinkPath('gri-404-training-and-education-2016.pdf'),
          icon: griIcon,
          alternativeCode: 'gri'
        },
        utrCodes: [
          'gri/2020/404-1/a',
          'gri/2020/404-2/a',
          'gri/2020/404-2/b',
          'gri/2020/404-3',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 405: Diversity and Equal Opportunity',
        groupData: {
          colour: '#fa6a01',
          link: getLinkPath('gri-405-diversity-and-equal-opportunity-2016.pdf'),
          icon: griIcon,
          alternativeCode: 'gri'
        },
        utrCodes: [
          'gri/2020/405-1/a',
          'gri/2020/405-1/b',
          'gri/2020/405-2/a',
          'gri/2020/405-2/b',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 406: Non-discrimination',
        groupData: {
          colour: '#fa6a01',
          link: getLinkPath('gri-406-non-discrimination-2016.pdf'),
          icon: griIcon,
          alternativeCode: 'gri'
        },
        utrCodes: [
          'gri/2020/406-1/a',
          'gri/2020/406-1/b',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 407: Freedom of Association and Collective Bargaining',
        groupData: {
          colour: '#fa6a01',
          link: getLinkPath('gri-407-freedom-of-association-and-collective-bargaining-2016.pdf'),
          icon: griIcon,
          alternativeCode: 'gri'
        },
        utrCodes: [
          'gri/2020/407-1/a',
          'gri/2020/407-1/b',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 408: Child Labor',
        groupData: {
          colour: '#fa6a01',
          link: getLinkPath('gri-408-child-labor-2016.pdf'),
          icon: griIcon,
          alternativeCode: 'gri'
        },
        utrCodes: [
          'gri/2020/408-1/a',
          'gri/2020/408-1/b',
          'gri/2020/408-1/c',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 409: Forced or Compulsory Labor',
        groupData: {
          colour: '#fa6a01',
          link: getLinkPath('gri-409-forced-or-compulsory-labor-2016.pdf'),
          icon: griIcon,
        },
        utrCodes: [
          'gri/2020/409-1/a',
          'gri/2020/409-1/b',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 410: Security Practices',
        groupData: {
          colour: '#fa6a01',
          link: getLinkPath('gri-410-security-practices-2016.pdf'),
          icon: griIcon,
          alternativeCode: 'gri'
        },
        utrCodes: [
          'gri/2020/410-1/a',
          'gri/2020/410-1/b',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 411: Rights of Indigenous Peoples',
        groupData: {
          colour: '#fa6a01',
          link: getLinkPath('gri-411-rights-of-indigenous-peoples-2016.pdf'),
          icon: griIcon,
          alternativeCode: 'gri'
        },
        utrCodes: [
          'gri/2020/411-1/a',
          'gri/2020/411-1/b', // Table - multiple rows
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 412: Human Rights Assessment',
        groupData: {
          colour: '#fa6a01',
          link: getLinkPath('gri-412-human-rights-assessment-2016.pdf'),
          icon: griIcon,
          alternativeCode: 'gri'
        },
        utrCodes: [
          'gri/2020/412-1/a',
          'gri/2020/412-2/a',
          'gri/2020/412-2/b',
          'gri/2020/412-3/a',
          'gri/2020/412-3/b',
        ],
        fragmentUtrConfiguration: {
          'gri/2020/412-2/a': [vc.Operations, vc.ExternalCsr],
        },
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 413: Local Communities',
        groupData: {
          colour: '#fa6a01',
          link: getLinkPath('gri-413-local-communities-2016.pdf'),
          icon: griIcon,
          alternativeCode: 'gri'
        },
        utrCodes: [
          'gri/2020/413-1', // Expect a single number % (atm asking for breakdown)
          'gri/2020/413-2', // Breakdown operation location/ significant actual
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 414: Supplier Social Assessment',
        groupData: {
          colour: '#fa6a01',
          link: getLinkPath('gri-414-supplier-social-assessment-2016.pdf'),
          icon: griIcon,
          alternativeCode: 'gri'
        },
        utrCodes: [
          'gri/2020/414-1',
          'gri/2020/414-2/a',
          'gri/2020/414-2/b',
          'gri/2020/414-2/c',
          'gri/2020/414-2/d',
          'gri/2020/414-2/e',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 415: Public Policy',
        groupData: {
          colour: '#fa6a01',
          link: getLinkPath('gri-415-public-policy-2016.pdf'),
          icon: griIcon,
          alternativeCode: 'gri'
        },
        utrCodes: [
          'gri/2020/415-1',
          'gri/2020/415-1/b',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 416: Customer Health and Safety',
        groupData: {
          colour: '#fa6a01',
          link: getLinkPath('gri-416-customer-health-and-safety-2016.pdf'),
          icon: griIcon,
        },
        utrCodes: [
          'gri/2020/416-1',
          'gri/2020/416-2',
          'gri/2020/416-2/b',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 417: Marketing and Labeling',
        groupData: {
          colour: '#fa6a01',
          link: getLinkPath('gri-417-marketing-and-labeling-2016.pdf'),
          icon: griIcon,
          alternativeCode: 'gri'
        },
        utrCodes: [
          'gri/2020/417-1/a',
          'gri/2020/417-1/b',
          'gri/2020/417-2',
          'gri/2020/417-2/b',
          'gri/2020/417-3',
          'gri/2020/417-3/b',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 418: Customer Privacy',
        groupData: {
          colour: '#fa6a01',
          link: getLinkPath('gri-418-customer-privacy-2016.pdf'),
          icon: griIcon,
          alternativeCode: 'gri'
        },
        utrCodes: [
          'gri/2020/418-1/a',
          'gri/2020/418-1/b',
          'gri/2020/418-1/c',
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 419: Socioeconomic Compliance',
        groupData: {
          colour: '#fa6a01',
          link: getLinkPath('gri-419-socioeconomic-compliance-2016.pdf'),
          icon: griIcon,
          alternativeCode: 'gri'
        },
        utrCodes: [
          'gri/2020/419-1/a',
          'gri/2020/419-1/b',
          'gri/2020/419-1/c'
        ],
      }
    },
    {
      utrGroupConfig: {
        groupName: 'Business of Fashion: Fashion Sustainability Benchmark',
        groupData: {
          alternativeCode: 'bof'
        },
        utrCodes: [
          "bof/1.1",
          "bof/1.2",
          "bof/1.3",
          "bof/1.4",
          "bof/1.5",
          "bof/1.6",
          "bof/1.7",
          "bof/1.8",
          "bof/1.9",
          "bof/1.10",
          "bof/1.11",
          "bof/1.12",
          "bof/1.13",
          "bof/1.14",
          "bof/1.15",
          "bof/1.16",
          "bof/1.17",
          "bof/1.18",
          "bof/1.19",
          "bof/1.20",
          "bof/1.21",
          "bof/1.22",
          "bof/1.23",
          "bof/1.24",
          "bof/1.25",
          "bof/1.26",
          "bof/1.27",
          "bof/1.28",
          "bof/1.29",
          "bof/1.30",
          "bof/1.31",
          "bof/1.32",
          "bof/1.33",
          "bof/1.34",
          "bof/1.35",
          "bof/1.36",
          "bof/1.37",
          "bof/1.38",
          "bof/1.39",
          "bof/1.40",
          "bof/1.41",
          "bof/2.1",
          "bof/2.2",
          "bof/2.3",
          "bof/2.4",
          "bof/2.5",
          "bof/2.6",
          "bof/2.7",
          "bof/2.8",
          "bof/2.9",
          "bof/2.10",
          "bof/2.11",
          "bof/2.12",
          "bof/2.13",
          "bof/2.14",
          "bof/2.15",
          "bof/2.16",
          "bof/2.17",
          "bof/2.18",
          "bof/2.19",
          "bof/2.20",
          "bof/2.21",
          "bof/2.22",
          "bof/2.23",
          "bof/3.1",
          "bof/3.2",
          "bof/3.3",
          "bof/3.4",
          "bof/3.5",
          "bof/3.6",
          "bof/3.7",
          "bof/3.8",
          "bof/3.9",
          "bof/3.10",
          "bof/3.11",
          "bof/3.12",
          "bof/3.13",
          "bof/3.14",
          "bof/3.15",
          "bof/3.16",
          "bof/3.17",
          "bof/3.18",
          "bof/3.19",
          "bof/3.20",
          "bof/3.21",
          "bof/3.22",
          "bof/3.23",
          "bof/3.24",
          "bof/3.25",
          "bof/3.26",
          "bof/3.27",
          "bof/3.28",
          "bof/3.29",
          "bof/3.30",
          "bof/3.31",
          "bof/3.32",
          "bof/3.33",
          "bof/3.34",
          "bof/3.35",
          "bof/3.36",
          "bof/3.37",
          "bof/3.38",
          "bof/3.39",
          "bof/3.40",
          "bof/3.41",
          "bof/3.42",
          "bof/3.43",
          "bof/3.44",
          "bof/3.45",
          "bof/3.46",
          "bof/3.47",
          "bof/3.48",
          "bof/3.49",
          "bof/3.50",
          "bof/3.51",
          "bof/3.52",
          "bof/3.53",
          "bof/3.54",
          "bof/3.55",
          "bof/3.56",
          "bof/3.57",
          "bof/3.58",
          "bof/3.59",
          "bof/3.60",
          "bof/3.61",
          "bof/3.62",
          "bof/3.63",
          "bof/3.64",
          "bof/3.65",
          "bof/3.66",
          "bof/3.67",
          "bof/3.68",
          "bof/3.69",
          "bof/3.70",
          "bof/3.71",
          "bof/3.72",
          "bof/4.1",
          "bof/4.2",
          "bof/4.3",
          "bof/4.4",
          "bof/4.5",
          "bof/4.6",
          "bof/4.7",
          "bof/4.8",
          "bof/4.9",
          "bof/4.11",
          "bof/4.12",
          "bof/4.13",
          "bof/4.14",
          "bof/4.15",
          "bof/4.16",
          "bof/4.17",
          "bof/4.18",
          "bof/4.19",
          "bof/4.20",
          "bof/4.21",
          "bof/4.22",
          "bof/4.23",
          "bof/4.24",
          "bof/4.25",
          "bof/4.26",
          "bof/4.27",
          "bof/4.28",
          "bof/4.29",
          "bof/4.30",
          "bof/4.31",
          "bof/4.32",
          "bof/4.33",
          "bof/4.34",
          "bof/4.35",
          "bof/4.36",
          "bof/4.37",
          "bof/4.38",
          "bof/4.39",
          "bof/4.40",
          "bof/4.41",
          "bof/4.42",
          "bof/4.43",
          "bof/4.44",
          "bof/4.45",
          "bof/4.46",
          "bof/4.47",
          "bof/4.48",
          "bof/4.49",
          "bof/4.50",
          "bof/4.51",
          "bof/4.52",
          "bof/4.53",
          "bof/4.54",
          "bof/4.55",
          "bof/4.56",
          "bof/4.57",
          "bof/4.58",
          "bof/4.59",
          "bof/4.60",
          "bof/4.61",
          "bof/4.62",
          "bof/4.63",
          "bof/4.64",
          "bof/4.65",
          "bof/4.66",
          "bof/4.67",
          "bof/4.68",
          "bof/4.69",
          "bof/4.70",
          "bof/4.71",
          "bof/4.72",
          "bof/4.73",
          "bof/5.1",
          "bof/5.2",
          "bof/5.3",
          "bof/5.4",
          "bof/5.5",
          "bof/5.6",
          "bof/5.7",
          "bof/5.8",
          "bof/5.9",
          "bof/5.10",
          "bof/5.11",
          "bof/5.12",
          "bof/5.13",
          "bof/5.14",
          "bof/5.15",
          "bof/5.16",
          "bof/5.17",
          "bof/5.18",
          "bof/5.19",
          "bof/5.20",
          "bof/5.21",
          "bof/5.22",
          "bof/5.23",
          "bof/5.24",
          "bof/5.25",
          "bof/5.26",
          "bof/5.27",
          "bof/5.28",
          "bof/5.29",
          "bof/5.30",
          "bof/5.31",
          "bof/5.32",
          "bof/5.33",
          "bof/5.34",
          "bof/5.35",
          "bof/5.36",
          "bof/5.37",
          "bof/5.38",
          "bof/5.39",
          "bof/5.40",
          "bof/5.41",
          "bof/5.42",
          "bof/5.43",
          "bof/5.44",
          "bof/5.45",
          "bof/6.1",
          "bof/6.2",
          "bof/6.3",
          "bof/6.4",
          "bof/6.5",
          "bof/6.6",
          "bof/6.7",
          "bof/6.8",
          "bof/6.9",
          "bof/6.10",
          "bof/6.11",
          "bof/6.12",
          "bof/6.13",
          "bof/6.14",
          "bof/6.15",
          "bof/6.16",
          "bof/6.17",
          "bof/6.18",
          "bof/6.19",
          "bof/6.20",
          "bof/6.21",
          "bof/6.22",
          "bof/6.23",
          "bof/6.24",
          "bof/6.25",
          "bof/6.26",
          "bof/6.27",
          "bof/6.28",
          "bof/6.29",
          "bof/6.30",
          "bof/6.31",
          "bof/6.32",
          "bof/6.33",
          "bof/6.34",
          "bof/6.35",
          "bof/6.36",
          "bof/6.37",
          "bof/6.38",
          "bof/6.39",
          "bof/6.40",
          "bof/6.41",
          "bof/6.42",
          "bof/6.43",
          "bof/6.44",
          "bof/6.45",
          "bof/6.46",
          "bof/6.47",
          "bof/6.48",
          "bof/6.49",
          "bof/6.50",
          "bof/6.51",
          "bof/6.52",
          "bof/6.53",
          "bof/6.54",
          "bof/6.55",
          "bof/6.56",
          "bof/6.57",
          "bof/6.58",
          "bof/6.59",
          "bof/6.60",
          "bof/6.61",
          "bof/6.62",
          "bof/6.63",
          "bof/6.64",
          "bof/6.65",
          "bof/6.66",
          "bof/6.67",
          "bof/6.68",
          "bof/6.69",
          "bof/6.70",
          "bof/6.71",
          "bof/6.72",
          "bof/6.73",
          "bof/6.74",
          "bof/6.75",
          "bof/6.76",
          "bof/6.77",
          "bof/6.78",
          "bof/6.79",
          "bof/6.80",
          "bof/6.81",
          "bof/6.82",
          "bof/6.83",
          "bof/6.84",
        ],
      }
    },
  ],
};

export const gri2020ConfigCodeMap: { [key: string]: CompositeUtrConfigInterface } = {};
configs.forEach(c => {
  gri2020.additionalConfigs.push({ compositeConfig: c.code });
  gri2020ConfigCodeMap[c.code] = c;
});

gri2020.additionalConfigs = [
  ...(adapter2020.additionalConfigs ?? []),
  ...adapter2020.forms,
  ...gri2020.additionalConfigs
]
