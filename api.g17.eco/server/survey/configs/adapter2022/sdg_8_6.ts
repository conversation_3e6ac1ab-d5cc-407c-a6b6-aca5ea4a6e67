/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_8_6: CompositeUtrConfigInterface = {
  code: 'adapter2022/sdg/8.6',
  compositeUtrCode: 'sdg/8.6',
  groupName: 'SDG 8.6',
  groupData: {
    colour: '#f56a2f'
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'survey/sdg/8.6/pc-employees-18-30-training',
      },
      b: {
        code: 'survey/sdg/8.6/pc-new-hires-18-30',
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.WeightedSum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}',
              weight: 4
            },
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution(30-abs({b}-30),0,30,0,100)',
              weight: 1
            },
          ],
        },
      ]
    },
  }
};
