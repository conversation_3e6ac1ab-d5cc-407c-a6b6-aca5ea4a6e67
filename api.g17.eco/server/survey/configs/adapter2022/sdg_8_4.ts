/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_8_4: CompositeUtrConfigInterface = {
  code: 'adapter2022/sdg/8.4',
  compositeUtrCode: 'sdg/8.4',
  groupName: 'SDG 8.4',
  groupData: {
    colour: '#f56a2f'
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'survey/generic/pc-change-in-energy-from-initiatives',
      },
      b: {
        code: 'unctad/2020/b.1.1',
        valueListCode: 'pc_water'
      },
      c: {
        code: 'survey/generic/pc-renewable-fuel-consumption'
      },
      d: {
        code: 'survey/sdg/8.4/pc-materials-used-physical-goods-that-can-be-repurposed',
      },
      e: {
        code: 'survey/sdg/15.1/pc-raw-materials-sustainably-sourced',
      },
      f: {
        code: 'survey/generic/energy-consumption',
      },
      g: {
        code: 'gri/2020/102-7/a',
        valueListCode: 'org_scale_sales'
      },
      h: {
        code: 'gri/2020/302-3/a',
      },
      m: {
        code: 'survey/sdg/14.1/pc-packaging-recyclable',
      },
      i: {
        code: 'survey/sdg/12.2/pc-recycled-materials-in-manufacturing',
      },

    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.WeightedSum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution({a},0,60,0,100)',
              weight: 3/46
            },
            {
              type: CalculationType.Formula,
              formula: '{b}',
              weight: 5/46
            },
            {
              type: CalculationType.Formula,
              formula: '{c}',
              weight: 3/46
            },
            {
              type: CalculationType.Formula,
              formula: '{d}',
              weight: 15/46
            },
            {
              type: CalculationType.Formula,
              formula: '{e}',
              weight: 5/46
            },
            {
              type: CalculationType.Formula,
              formula: '(100-linearDistribution({f}/{g},0,60,0,100))',
              weight: 5/92
            },
            {
              type: CalculationType.Formula,
              formula: '(100-linearDistribution({h},0,60,0,100))',
              weight: 5/92
            },
            {
              type: CalculationType.Formula,
              formula: '{m}',
              weight: 5/46
            },
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution({i},0,80,0,100)',
              weight: 5/46
            },
          ]
        }
      ]
    },
  }
};
