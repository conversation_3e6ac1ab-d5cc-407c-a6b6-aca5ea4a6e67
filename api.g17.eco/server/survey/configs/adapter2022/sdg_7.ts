/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_7: CompositeUtrConfigInterface = {
  code: 'adapter2022/sdg/7',
  compositeUtrCode: 'sdg/7',
  importConfigurationData: {
    variables: {
      a: {
        code: 'sdg/7.1'
      },
      b: {
        code: 'sdg/7.2'
      },
      c: {
        code: 'sdg/7.3'
      },
      d: {
        code: 'sdg/7.a'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.WeightedSum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}',
            },
            {
              type: CalculationType.Formula,
              formula: '{b}',
            },
            {
              type: CalculationType.Formula,
              formula: '{c}',
            },
            {
              type: CalculationType.Formula,
              formula: '{d}', // 7.a
            },
            {
              type: CalculationType.Formula,
              formula: '0', // 7.b
            },
          ]
        }
      ]
    },
  }
};
