/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_8_7: CompositeUtrConfigInterface = {
  code: 'adapter2022/sdg/8.7',
  compositeUtrCode: 'sdg/8.7',
  groupName: 'SDG 8.7',
  groupData: {
    colour: '#f56a2f'
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'survey/sdg/8.7/child-labour-supply-chain-operations',
        valueListCode: 'supply_chain'
      },
      b: {
        code: 'survey/generic/supply-chain-spend'
      },
      c: {
        code: 'survey/sdg/8.7/pc-operations-risk-for-incidents-forced-labour',
        valueListCode: 'forced_labour'
      },
      d: {
        code: 'survey/sdg/8.7/pc-operations-risk-for-incidents-forced-labour',
        valueListCode: 'supply_chain'
      },
      e: {
        code: 'survey/sdg/10.7/human-trafficking',
      },
      f: {
        code: 'survey/sdg/16.2/num-children-employed-7-14',
      },
      g: {
        code: 'gri/2020/102-7/a',
        valueListCode: 'org_scale_employees_male'
      },
      h: {
        code: 'gri/2020/102-7/a',
        valueListCode: 'org_scale_employees_female'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.WeightedSum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '100*{a}/{b}',
              weight: 6
            },
            {
              type: CalculationType.Formula,
              formula: '(100*resolveString("{c}","yes",100,0)/resolveString("{d}","yes",100,0))',
              weight: 6
            },
            {
              type: CalculationType.Formula,
              formula: 'resolveString("{e}","yes",100,0)',
              weight: 2
            },
            {
              type: CalculationType.Formula,
              formula: '(100-linearDistribution(100*{f}/({g}+{h}),0,5,0,100))',
              weight: 3
            },
          ]
        }
      ]
    },
  }
};
