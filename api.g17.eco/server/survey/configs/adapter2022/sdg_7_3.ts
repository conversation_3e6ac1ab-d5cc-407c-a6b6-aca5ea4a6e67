/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_7_3: CompositeUtrConfigInterface = {
  code: 'adapter2022/sdg/7.3',
  compositeUtrCode: 'sdg/7.3',
  groupName: 'SDG 7.3',
  groupData: {
    colour: '#f56a2f'
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'survey/generic/pc-change-in-energy-from-initiatives',
      },
      b: {
        code: 'unctad/2020/b.5.2',
        valueListCode: 'net_value_add_change'
      },
      c: {
        code: 'survey/sdg/11.1/pc-investment-energy-renewable-energy',
      },
      d: {
        code: 'survey/generic/pc-renewable-fuel-consumption',
      },
      e: {
        code: 'gri/2020/302-3/a'
      },
      f: {
        code: 'survey/generic/energy-consumption',
      },
      g: {
        code: 'gri/2020/102-7/a',
        valueListCode: 'org_scale_sales'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.WeightedSum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution({a},0,60,0,100)',
              weight: 6
            },
            {
              type: CalculationType.Formula,
              formula: '{b}',
              weight: 30
            },
            {
              type: CalculationType.Formula,
              formula: '{c}',
              weight: 6
            },
            {
              type: CalculationType.Formula,
              formula: '(100-linearDistribution({f}/{g},0,60,0,100))',
              weight: 5
            },
            {
              type: CalculationType.Formula,
              formula: '(100-linearDistribution({e},0,60,0,100))',
              weight: 5
            },
            {
              type: CalculationType.Formula,
              formula: '{d}',
              weight: 6
            },
          ]
        }
      ]
    },
  }
};
