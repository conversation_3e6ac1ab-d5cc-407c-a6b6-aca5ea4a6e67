/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_score: CompositeUtrConfigInterface = {
  code: 'adapter2022/sdg/score',
  compositeUtrCode: 'sdg/score',
  importConfigurationData: {
    variables: {
      a: { code: 'sdg/1' },
      b: { code: 'sdg/2' },
      c: { code: 'sdg/3' },
      d: { code: 'sdg/4' },
      e: { code: 'sdg/5' },
      f: { code: 'sdg/6' },
      g: { code: 'sdg/7' },
      h: { code: 'sdg/8' },
      i: { code: 'sdg/9' },
      j: { code: 'sdg/10' },
      k: { code: 'sdg/11' },
      l: { code: 'sdg/12' },
      m: { code: 'sdg/13' },
      n: { code: 'sdg/14' },
      o: { code: 'sdg/15' },
      p: { code: 'sdg/16' },
      q: { code: 'sdg/17' },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.WeightedSum,
          variables: [
            { type: CalculationType.Formula, formula: '{a}' },
            { type: CalculationType.Formula, formula: '{b}' },
            { type: CalculationType.Formula, formula: '{c}' },
            { type: CalculationType.Formula, formula: '{d}' },
            { type: CalculationType.Formula, formula: '{e}' },
            { type: CalculationType.Formula, formula: '{f}' },
            { type: CalculationType.Formula, formula: '{g}' },
            { type: CalculationType.Formula, formula: '{h}' },
            { type: CalculationType.Formula, formula: '{i}' },
            { type: CalculationType.Formula, formula: '{j}' },
            { type: CalculationType.Formula, formula: '{k}' },
            { type: CalculationType.Formula, formula: '{l}' },
            { type: CalculationType.Formula, formula: '{m}' },
            { type: CalculationType.Formula, formula: '{n}' },
            { type: CalculationType.Formula, formula: '{o}' },
            { type: CalculationType.Formula, formula: '{p}' },
            { type: CalculationType.Formula, formula: '{q}' },
          ]
        }
      ]
    },
  }
};
