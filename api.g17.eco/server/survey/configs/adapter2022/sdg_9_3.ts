/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_9_3: CompositeUtrConfigInterface = {
  code: 'adapter2022/sdg/9.3',
  compositeUtrCode: 'sdg/9.3',
  groupName: 'SDG 9.3',
  groupData: {
    colour: '#f56a2f'
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'survey/generic/pc-spend-msmes-suppliers',
      },
      b: {
        code: 'survey/generic/supply-chain-spend',
      },
      c: {
        code: 'survey/sdg/9.3/timely-payments-suppliers',
      },
      d: {
        code: 'survey/sdg/9.3/spend-msmes-sustainability',
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.WeightedSum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution(100*{a}/{b},0,25,0,100)',
              weight: 1
            },
            {
              type: CalculationType.Formula,
              formula: 'resolveString("{c}","yes",100)',
              weight: 3
            },
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution(100*{d}/{a},0,25,0,100)',
              weight: 3
            },
          ]
        }
      ]
    },
  }
};

