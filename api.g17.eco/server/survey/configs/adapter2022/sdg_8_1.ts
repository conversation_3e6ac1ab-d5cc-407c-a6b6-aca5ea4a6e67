/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_8_1: CompositeUtrConfigInterface = {
  code: 'adapter2022/sdg/8.1',
  compositeUtrCode: 'sdg/8.1',
  groupName: 'SDG 8.1',
  groupData: {
    colour: '#f56a2f'
  },
  importConfigurationData: {
    variables: {
      b: {
        code: 'survey/sdg/8.1/company-buying-practices-negative-impact',
      },
      c: {
        code: 'survey/sdg/8.1/economic-value-generated',
      }
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.WeightedSum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '(100-linearDistribution({b},0,100,0,100))',
            },
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution({c},0,100,0,100)',
            },
          ]
        }
      ]
    },
  }
};
