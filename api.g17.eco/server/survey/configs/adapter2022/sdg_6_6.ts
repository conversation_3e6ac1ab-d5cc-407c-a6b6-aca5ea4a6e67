/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_6_6: CompositeUtrConfigInterface = {
  code: 'adapter2022/sdg/6.6',
  compositeUtrCode: 'sdg/6.6',
  groupName: 'SDG 6.6',
  groupData: {
    colour: '#f56a2f'
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'survey/generic/pc-toxic-spills',
        valueListCode: 'spills'
      },
      b: {
        code: 'survey/generic/num-spills',
      },
      c: {
        code: 'survey/generic/pc-water-from-3rd-party',
      },
      d: {
        code: 'survey/sdg/6.6/water-risk-assessment',
        valueListCode: 'current_status'
      },
      e: {
        code: 'survey/sdg/6.6/water-risk-assessment',
        valueListCode: 'potential_changes'
      },
      f: {
        code: 'survey/sdg/6.6/water-risk-assessment',
        valueListCode: 'scenario_analysis'
      },
      g: {
        code: 'survey/sdg/6.4/pc-water-withdrawl-from-scarce',
      },
      h: {
        code: 'survey/sdg/15.1/species-monitor',
      },
      i: {
        code: 'survey/sdg/2.3/pc-supply-chain-revenue-small-producers',
      },
      j: {
        code: 'gri/2020/102-7/a',
        valueListCode: 'org_scale_sales'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.WeightedSum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '(100-linearDistribution(100*{a}/{b},0,30,0,100))',
              weight: 3
            },
            {
              type: CalculationType.Formula,
              formula: '{c}',
              weight: 6
            },
            {
              type: CalculationType.Formula,
              formula: 'resolveString("{d}","yes",100,0)',
              weight: 4
            },
            {
              type: CalculationType.Formula,
              formula: 'resolveString("{e}","yes",100,0)',
              weight: 4
            },
            {
              type: CalculationType.Formula,
              formula: 'resolveString("{f}","yes",100,0)',
              weight: 4
            },
            {
              type: CalculationType.Formula,
              formula: '(100-resolveString("{e}","yes",100,0))',
              weight: 4
            },
            {
              type: CalculationType.Formula,
              formula: '{h}',
              weight: 3
            },
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution(100*{i}/{j},0,25,0,100)',
              weight: 3
            },
          ]
        }
      ]
    },
  }
};
