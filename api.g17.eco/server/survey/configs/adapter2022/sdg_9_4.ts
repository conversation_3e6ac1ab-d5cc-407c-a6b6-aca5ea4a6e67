/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_9_4: CompositeUtrConfigInterface = {
  code: 'adapter2022/sdg/9.4',
  compositeUtrCode: 'sdg/9.4',
  groupName: 'SDG 9.4',
  groupData: {
    colour: '#f56a2f'
  },
  importConfigurationData: {
    variables: {

      b: {
        code: 'gri/2020/305-1/a',
      },
      c: {
        code: 'gri/2020/102-7/a',
        valueListCode: 'org_scale_sales'
      },
      d: {
        code: 'gri/2020/305-2/a'
      },
      e: {
        code: 'gri/2020/305-2/b'
      },
      f: {
        code: 'gri/2020/305-3/a'
      },
      g: {
        code: 'survey/sdg/9.4/pc-operations-with-monitoring'
      },
      h: {
        code: 'survey/sdg/9.4/sustainable-site-design',
        valueListCode: 'manage_waste'
      },
      i: {
        code: 'survey/sdg/9.4/sustainable-site-design',
        valueListCode: 'light_pollution'
      },
      j: {
        code: 'survey/sdg/9.4/sustainable-site-design',
        valueListCode: 'air_quality'
      },
      k: {
        code: 'survey/sdg/9.4/sustainable-site-design',
        valueListCode: 'restore_habitat'
      },
      a: {
        code: 'survey/sdg/12.4/significant-air-emissions',
      },
      l: {
        code: 'gri/2020/102-7/a',
        valueListCode: 'org_scale_sales'
      },
      m: {
        code: 'survey/generic/energy-consumption',
      },
      n: {
        code: 'gri/2020/102-7/a',
        valueListCode: 'org_scale_sales'
      },
      o: {
        code: 'gri/2020/302-3/a',
      }
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.WeightedSum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '(100-linearDistribution({b}/{c},0,60,0,100))',
              weight: 1
            },
            {
              type: CalculationType.Formula,
              formula: '(100-linearDistribution(({d}+{e})/{c},0,60,0,100))',
              weight: 1
            },
            {
              type: CalculationType.Formula,
              formula: '(100-linearDistribution({f}/{c},0,60,0,100))',
              weight: 1
            },
            {
              type: CalculationType.Formula,
              formula: '{g}',
              weight: 6
            },
            {
              type: CalculationType.Formula,
              formula: '(resolveString("{h}","yes",25)+resolveString("{i}","yes",25)+resolveString("{j}","yes",25)+resolveString("{k}","yes",25))',
              weight: 6
            },
            {
              type: CalculationType.Formula,
              formula: '(100-linearDistribution({a}/{l},0,60,0,100))',
              weight: 2
            },
            {
              type: CalculationType.Formula,
              formula: '(100-linearDistribution({m}/{n},0,60,0,100))',
              weight: 1
            },
            {
              type: CalculationType.Formula,
              formula: '(100-linearDistribution({o},0,60,0,100))',
              weight: 1
            }
          ]
        }
      ]
    },
  }
};

