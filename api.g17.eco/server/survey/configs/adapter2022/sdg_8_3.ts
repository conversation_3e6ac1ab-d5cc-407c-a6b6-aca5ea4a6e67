/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_8_3: CompositeUtrConfigInterface = {
  code: 'adapter2022/sdg/8.3',
  compositeUtrCode: 'sdg/8.3',
  groupName: 'SDG 8.3',
  groupData: {
    colour: '#f56a2f'
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'survey/generic/suppliers-members-vulnerable-groups',
      },
      b: {
        code: 'survey/generic/supply-chain-spend',
      },
      c: {
        code: 'survey/generic/pc-spend-msmes-suppliers',
      },
      d: {
        code: 'gri/2020/204-1/a',
      },
      e: {
        code: 'survey/sdg/8.3/pc-capex-deployed-tech-with-start-ups-local-needs'
      },
      f: {
        code: 'survey/generic/capex-capital-expenditure'
      },
      g: {
        code: 'survey/sdg/10.3/discrimination-policy'
      },
      h: {
        code: 'survey/sdg/8.1/local-market-share-company-services',
      },
      i: {
        code: 'survey/sdg/8.6/pc-employee-turnover-18-30',
      },

    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.WeightedSum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution(100*{a}/{b},0,5,0,100)',
              weight: 5/69
            },
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution(100*{c}/{b},0,25,0,100)',
              weight: 3/31
            },
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution({d},0,30,0,100)',
              weight: 10/69
            },
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution(100*{e}/{f},0,5,0,100)',
              weight: 20/69
            },
            {
              type: CalculationType.Formula,
              formula: 'resolveString("{g}","yes",100,0)',
              weight: 3/69
            },
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution(30-abs({a}-30),0,30,0,100)',
              weight: 20/69
            },
            {
              type: CalculationType.Formula,
              formula: '(100-linearDistribution({i},0,30,0,100))',
              weight: 4/69
            },
          ]
        }
      ]
    },
  }
};
