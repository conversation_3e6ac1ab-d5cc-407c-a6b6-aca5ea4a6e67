/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_9: CompositeUtrConfigInterface = {
  code: 'adapter2022/sdg/9',
  compositeUtrCode: 'sdg/9',
  importConfigurationData: {
    variables: {
      a: {
        code: 'sdg/9.1'
      },
      b: {
        code: 'sdg/9.3'
      },
      c: {
        code: 'sdg/9.4'
      },
      d: {
        code: 'sdg/9.5'
      }
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.WeightedSum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}',
            },
            {
              type: CalculationType.Formula,
              formula: '0', // 9.2
            },
            {
              type: CalculationType.Formula,
              formula: '{b}',
            },
            {
              type: CalculationType.Formula,
              formula: '{c}',
            },
            {
              type: CalculationType.Formula,
              formula: '{d}',
            },
            {
              type: CalculationType.Formula,
              formula: '0', // 9.a
            },
            {
              type: CalculationType.Formula,
              formula: '0', // 9.b
            },
            {
              type: CalculationType.Formula,
              formula: '0', // 9.c
            },
          ]
        },
      ]
    },
  }
};
