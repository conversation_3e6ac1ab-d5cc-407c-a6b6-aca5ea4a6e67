/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_9_1: CompositeUtrConfigInterface = {
  code: 'adapter2022/sdg/9.1',
  compositeUtrCode: 'sdg/9.1',
  groupName: 'SDG 9.1',
  groupData: {
    colour: '#f56a2f'
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'survey/sdg/9.1/pc-buildings-impact-assessment',
      },
      b: {
        code: 'survey/sdg/9.1/pc-capex-resilient-services',
      },
      c: {
        code: 'survey/generic/capex-capital-expenditure',
      },
      d: {
        code: 'survey/generic/pc-investment-ict-considers-digital-access',
      },
      e: {
        code: 'survey/generic/pc-investment-improved-road-safety',
      },
      f: {
        code: 'survey/sdg/9.1/hours-lost-due-to-electrical-outages',
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.WeightedSum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}',
              weight: 2
            },
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution(100*{b}/{c},0,25,0,100)',
              weight: 2
            },
            {
              type: CalculationType.Formula,
              formula: '{d}',
              weight: 2
            },
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution(100*{e}/{c},0,5,0,100)',
              weight: 1
            },
            {
              type: CalculationType.Formula,
              formula: '(100-{f})',
              weight: 2
            },
          ]
        }
      ]
    },
  }
};

