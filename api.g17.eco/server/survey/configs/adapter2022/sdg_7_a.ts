/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_7_a: CompositeUtrConfigInterface = {
  code: 'adapter2022/sdg/7.a',
  compositeUtrCode: 'sdg/7.a',
  groupName: 'SDG 7.a',
  groupData: {
    colour: '#f56a2f'
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/305-1/a',
      },
      b: {
        code: 'gri/2020/102-7/a',
        valueListCode: 'org_scale_sales'
      },
      c: {
        code: 'survey/generic/scope-2',
      },
      d: {
        code: 'gri/2020/305-3/a'
      },
      e: {
        code: 'survey/sdg/11.1/pc-investment-energy-renewable-energy',
      },
      f: {
        code: 'unctad/2020/a.3.1',
        valueListCode: 'pc_envt_exp'
      },
      g: {
        code: 'survey/generic/pc-renewable-fuel-consumption',
      },
      h: {
        code: 'gri/2020/302-3/a',
      }
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.WeightedSum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '(100-linearDistribution({a}/{b},0,60,0,100))',
              weight: 5
            },
            {
              type: CalculationType.Formula,
              formula: '(100-linearDistribution({c}/{b},0,60,0,100))',
              weight: 5
            },
            {
              type: CalculationType.Formula,
              formula: '(100-linearDistribution({d}/{b},0,60,0,100))',
              weight: 5
            },
            {
              type: CalculationType.Formula,
              formula: '{e}',
              weight: 6
            },
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution({a},0,25,0,100)',
              weight: 6
            },
            {
              type: CalculationType.Formula,
              formula: '{g}',
              weight: 5
            },
            {
              type: CalculationType.Formula,
              formula: '(100-linearDistribution({h},0,60,0,100))',
              weight: 5
            }
          ]
        }
      ]
    },
  }
};
