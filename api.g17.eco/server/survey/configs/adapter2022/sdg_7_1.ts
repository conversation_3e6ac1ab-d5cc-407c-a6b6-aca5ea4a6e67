/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_7_1: CompositeUtrConfigInterface = {
  code: 'adapter2022/sdg/7.1',
  compositeUtrCode: 'sdg/7.1',
  groupName: 'SDG 7.1',
  groupData: {
    colour: '#f56a2f'
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'survey/sdg/7.1/pc-revenue-generated-low-carbon-services',
        valueListCode: 'revenue'
      },
      b: {
        code: 'gri/2020/102-7/a',
        valueListCode: 'org_scale_sales'
      },
      c: {
        code: 'survey/sdg/7.1/pc-capex-sustainable-energy'
      },
      d: {
        code: 'survey/sdg/11.1/pc-investment-energy-renewable-energy',
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.WeightedSum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution(100*{a}/{b},0,25,0,100)',
              weight: 5
            },
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution({c},0,20,0,100)',
              weight: 5
            },
            {
              type: CalculationType.Formula,
              formula: '{d}',
              weight: 1
            },
          ]
        }
      ]
    },
  }
};
