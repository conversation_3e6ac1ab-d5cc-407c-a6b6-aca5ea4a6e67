/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_6_5: CompositeUtrConfigInterface = {
  code: 'adapter2022/sdg/6.5',
  compositeUtrCode: 'sdg/6.5',
  groupName: 'SDG 6.5',
  groupData: {
    colour: '#f56a2f'
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'survey/sdg/6.5/pc-operations-water-risk-management-plan',
      },
      b: {
        code: 'gri/2020/102-7/a',
        valueListCode: 'org_scale_operations'
      },
      c: {
        code: 'survey/sdg/6.5/engage-water-policy',
        valueListCode: 'do_you_engage'
      },
      d: {
        code: 'survey/generic/water-pollution',
      },
      e: {
        code: 'survey/sdg/6.4/pc-water-withdrawl-from-scarce',
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.WeightedSum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '100*{a}/{b}',
              weight: 6
            },
            {
              type: CalculationType.Formula,
              formula: 'resolveString("{c}","yes",100,0)',
              weight: 6
            },
            {
              type: CalculationType.Formula,
              formula: 'resolveString("{d}","yes",100,0)',
              weight: 3
            },
            {
              type: CalculationType.Formula,
              formula: '(100-{e})',
              weight: 2
            },
          ]
        }
      ]
    },
  }
};
