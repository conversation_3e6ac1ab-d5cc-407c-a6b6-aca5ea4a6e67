/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_7_2: CompositeUtrConfigInterface = {
  code: 'adapter2022/sdg/7.2',
  compositeUtrCode: 'sdg/7.2',
  groupName: 'SDG 7.2',
  groupData: {
    colour: '#f56a2f'
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'survey/generic/pc-renewable-fuel-consumption',
      },
      b: {
        code: 'survey/generic/paris-aligned-targets',
        valueListCode: 'consumption_target'
      },
      c: {
        code: 'survey/generic/paris-aligned-targets',
        valueListCode: 'production_target'
      },
      d: {
        code: 'survey/sdg/11.1/pc-investment-energy-renewable-energy',
      },
      e: {
        code: 'survey/generic/paris-aligned-targets',
        valueListCode: 'paris_agreement_13'
      },
      f: {
        code: 'survey/generic/energy-consumption',
      },
      g: {
        code: 'gri/2020/102-7/a',
        valueListCode: 'org_scale_sales'
      },
      h: {
        code: 'gri/2020/302-3/a',
      },
      i: {
          code: 'survey/generic/pc-change-in-energy-from-initiatives',
      }
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.WeightedSum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}',
              weight: 6
            },
            {
              type: CalculationType.Formula,
              formula: '{b}',
              weight: 15
            },
            {
              type: CalculationType.Formula,
              formula: '{c}',
              weight: 15
            },
            {
              type: CalculationType.Formula,
              formula: '{d}',
              weight: 6
            },
            {
              type: CalculationType.Formula,
              formula: 'resolveString("{e}","yes",100,0)',
              weight: 15
            },
            {
              type: CalculationType.Formula,
              formula: '(100-linearDistribution({f}/{g},0,60,0,100))',
              weight: 5
            },
            {
              type: CalculationType.Formula,
              formula: '(100-linearDistribution({h},0,60,0,100))',
              weight: 6
            },
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution({i},0,60,0,100)',
              weight: 5
            },
          ]
        }
      ]
    },
  }
};
