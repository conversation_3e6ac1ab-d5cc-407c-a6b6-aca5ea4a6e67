/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_6_4: CompositeUtrConfigInterface = {
  code: 'adapter2022/sdg/6.4',
  compositeUtrCode: 'sdg/6.4',
  groupName: 'SDG 6.4',
  groupData: {
    colour: '#f56a2f'
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'unctad/2020/b.1.1',
        valueListCode: 'pc_water'
      },
      b: {
        code: 'survey/sdg/6.4/pc-water-withdrawl-from-scarce',
      },
      c: {
        code: 'survey/sdg/6.4/pc-operations-at-risk-from-access-to-water',
      },
      d: {
        code: 'gri/2020/102-7/a',
        valueListCode: 'org_scale_operations'
      },
      e: {
        code: 'survey/sdg/6.4/pc-investment-water-recycling',
      },
      f: {
        code: 'survey/generic/opex-operational-expenditure'
      },
      g: {
        code: 'unctad/2020/b.1.2',
        valueListCode: 'percentage'
      },
      h: {
        code: 'survey/generic/pc-water-from-3rd-party',
      },
      i: {
        code: 'gri/2020/303-5/a',
      },
      j: {
        code: 'survey/generic/water-intensity',
      }
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.WeightedSum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}',
              weight: 2
            },
            {
              type: CalculationType.Formula,
              formula: '(100-{b})',
              weight: 2
            },
            {
              type: CalculationType.Formula,
              formula: '(100-(100*{c}/{d}))',
              weight: 6
            },
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution(100*{e}/{f},0,10,0,100)',
              weight: 6
            },
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution({g},0,10,0,100)',
              weight: 6
            },
            {
              type: CalculationType.Formula,
              formula: '{h}',
              weight: 3
            },
            {
              type: CalculationType.Formula,
              formula: '(100-linearDistribution({i},0,60,0,100))',
              weight: 2
            },
            {
              type: CalculationType.Formula,
              formula: '(100-linearDistribution({j},0,60,0,100))',
              weight: 2
            },
          ]
        }
      ]
    },
  }
};
