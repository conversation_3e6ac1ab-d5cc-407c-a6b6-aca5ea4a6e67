/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_8_9: CompositeUtrConfigInterface = {
  code: 'adapter2022/sdg/8.9',
  compositeUtrCode: 'sdg/8.9',
  groupName: 'SDG 8.9',
  groupData: {
    colour: '#f56a2f'
  },
  importConfigurationData: {
    variables: {
      c: {
        code: 'survey/sdg/11.4/initiatives-enhance-local-culture',
        valueListCode: 'cultural_heritage_revenue'
      },
      d: {
        code: 'gri/2020/102-7/a',
        valueListCode: 'org_scale_sales'
      }
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.WeightedSum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: 'resolveString("{a}", "yes", 50)',
            },
            {
              type: CalculationType.Formula,
              formula: 'resolveString("{b}", "yes", 50)',
            },
          ]
        }
      ]
    },
  }
};
