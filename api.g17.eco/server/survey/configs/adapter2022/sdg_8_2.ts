/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_8_2: CompositeUtrConfigInterface = {
  code: 'adapter2022/sdg/8.2',
  compositeUtrCode: 'sdg/8.2',
  groupName: 'SDG 8.2',
  groupData: {
    colour: '#f56a2f'
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'survey/sdg/8.2/pc-company-company-productivity-due-tech',
      },
      b: {
        code: 'survey/sdg/8.2/company-transition-assistance-programmes',
      },
      c: {
        code: 'survey/sdg/8.8/health-safety-training',
      },
      e: {
        code: 'gri/2020/404-1/a',
        valueListCode: 'male'
      },
      f: {
        code: 'gri/2020/102-7/a',
        valueListCode: 'org_scale_employees_male'
      },
      g: {
        code: 'gri/2020/102-7/a',
        valueListCode: 'org_scale_employees_female'
      },
      h: {
        code: 'gri/2020/404-1/a',
        valueListCode: 'female'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.WeightedSum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}',
              weight: 8
            },
            {
              type: CalculationType.Formula,
              formula: '{b}',
              weight: 2
            },
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution(100*{c}/ (({e} * ({f} / ({f} + {g})) ) + ({h} * ({g} / ({f} + {g})) )),0,10,0,100)',
              weight: 1
            }
          ]
        }
      ]
    },
  }
};
