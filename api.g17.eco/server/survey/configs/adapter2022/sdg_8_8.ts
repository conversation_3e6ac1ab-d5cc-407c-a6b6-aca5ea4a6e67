/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_8_8: CompositeUtrConfigInterface = {
  code: 'adapter2022/sdg/8.8',
  compositeUtrCode: 'sdg/8.8',
  groupName: 'SDG 8.8',
  groupData: {
    colour: '#f56a2f'
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'survey/generic/pc-employee-working-days-lost',
      },
      b: {
        code: 'unctad/2020/c.3.1',
        valueListCode: 'percent_expenditure_hs'
      },
      c: {
        code:'gri/2020/403-10/a',
        valueListCode: 'health_fatalities'
      },
      d: {
        code:'gri/2020/403-10/a',
        valueListCode: 'health_ill_health'
      },
      e: {
        code: 'gri/2020/102-41',
      },
      f: {
        code: 'survey/sdg/3.2/pc-health-care-cover-families',
      },
      g: {
        code: 'survey/sdg/16.4/pc-new-suppliers-due-diligence-organised-crime',
      },
      h: {
        code: 'gri/2020/407-1/a',
        valueListCode: 'number_suppliers'
      },
      i: {
        code: 'gri/2020/414-1',
      },
      j: {
        code: 'survey/sdg/16.1/suppliers-screened-harassment',
      },
      k: {
        code: 'gri/2020/407-1/a',
        valueListCode: 'number_suppliers'
      },
      l: {
        code: 'survey/sdg/8.8/health-safety-training',
      },
      m: {
        code: 'gri/2020/404-1/a',
        valueListCode: 'male'
      },
      n: {
        code: 'gri/2020/102-7/a',
        valueListCode: 'org_scale_employees_male'
      },
      o: {
        code: 'gri/2020/102-7/a',
        valueListCode: 'org_scale_employees_female'
      },
      p: {
        code: 'gri/2020/404-1/a',
        valueListCode: 'female'
      },
      q: {
        code: 'gri/2020/403-1/b',
        valueListCode: 'pc_workers_covered'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.WeightedSum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '(100-linearDistribution({a},0,5,0,100))',
              weight: 6/107
            },
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution({b},0,10,0,100)',
              weight: 12/107
            },
            {
              type: CalculationType.Formula,
              formula: '(100-linearDistribution({c},0,5,0,100))',
              weight: 4/107
            },
            {
              type: CalculationType.Formula,
              formula: '(100-linearDistribution({d},0,5,0,100))',
              weight: 4/107
            },
            {
              type: CalculationType.Formula,
              formula: '{e}',
              weight: 12/107
            },
            {
              type: CalculationType.Formula,
              formula: '{f}',
              weight: 12/107
            },
            {
              type: CalculationType.Formula,
              formula: '(100*{g}/{h})',
              weight: 12/107
            },
            {
              type: CalculationType.Formula,
              formula: '{i}',
              weight: 6/107
            },
            {
              type: CalculationType.Formula,
              formula: '100*{j}/{k}',
              weight: 12/107
            },
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution(100*{l}/ (({m} * ({n} / ({n} + {o})) ) + ({p} * ({o} / ({n} + {o})) )),0,10,0,100)',
              weight: 3/107
            },
            {
              type: CalculationType.Formula,
              formula: '{q}',
              weight: 24/107
            },
          ]
        }
      ]
    },
  }
};
