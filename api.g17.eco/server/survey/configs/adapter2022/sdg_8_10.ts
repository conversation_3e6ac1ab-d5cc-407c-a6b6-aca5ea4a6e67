/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_8_10: CompositeUtrConfigInterface = {
  code: 'adapter2022/sdg/8.10',
  compositeUtrCode: 'sdg/8.10',
  groupName: 'SDG 8.10',
  groupData: {
    colour: '#f56a2f'
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'survey/sdg/8.10/company-offer-financial-advice-employees',
      },
      b: {
        code: 'survey/sdg/8.10/company-offer-equal-access-underrepresented-groups',
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.WeightedSum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}',
            },
            {
              type: CalculationType.Formula,
              formula: 'resolveString("{b}", "yes", 100)',
            },
          ]
        }
      ]
    },
  }
};
