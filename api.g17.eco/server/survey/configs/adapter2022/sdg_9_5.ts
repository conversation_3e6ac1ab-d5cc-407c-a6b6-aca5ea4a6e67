/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_9_5: CompositeUtrConfigInterface = {
  code: 'adapter2022/sdg/9.5',
  compositeUtrCode: 'sdg/9.5',
  groupName: 'SDG 9.5',
  groupData: {
    colour: '#f56a2f'
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'survey/sdg/9.5/pc-opex-research-and-development',
        valueListCode: 'r_and_d_revenue'
      },
      b: {
        code: 'survey/sdg/9.5/pc-opex-research-and-development',
        valueListCode: 'r_and_d_capitalised'
      },
      c: {
        code: 'survey/sdg/17.9/expenditure-r-and-d-tech-developing-countries',
      },
      d: {
        code: 'survey/sdg/9.5/pc-opex-research-and-development',
        valueListCode: 'r_and_d'
      },
      e: {
        code: 'survey/sdg/9.5/compnay-expenditure-third-party-education',
      },
      f: {
        code: 'survey/sdg/9.5/pc-patent-applications-succesful',
      },
      g: {
        code: 'survey/sdg/9.5/pc-work-force-researchers',
      },
      h: {
        code: 'survey/generic/opex-operational-expenditure'
      }
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.WeightedSum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution({a},0,15,0,100)',
              weight: 2
            },
            {
              type: CalculationType.Formula,
              formula: '{b}',
              weight: 2
            },
            {
              type: CalculationType.Formula,
              formula: '100*{c}/{d}',
              weight: 1
            },
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution(100*{e}/{h},0,3,0,100)',
              weight: 2
            },
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution({f},0,25,0,100)',
              weight: 2
            },
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution({g},0,15,0,100)',
              weight: 2
            },
          ]
        }
      ]
    },
  }
};

