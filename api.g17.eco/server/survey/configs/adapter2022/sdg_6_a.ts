/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_6_a: CompositeUtrConfigInterface = {
  code: 'adapter2022/sdg/6.a',
  compositeUtrCode: 'sdg/6.a',
  groupName: 'SDG 6.a',
  groupData: {
    colour: '#f56a2f'
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'survey/sdg/6.3/pc-facilities-wastewater-management',
        valueListCode: 'pc_facilities_water_management'
      },
      b: {
        code: 'gri/2020/303-5/a',
      },
      c: {
        code: 'survey/generic/water-intensity'
      }
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.WeightedSum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}',
              weight: 3
            },
            {
              type: CalculationType.Formula,
              formula: '(100-linearDistribution({b},0,60,0,100))',
              weight: 2
            },
            {
              type: CalculationType.Formula,
              formula: '(100-linearDistribution({c},0,60,0,100))',
              weight: 2
            },
          ]
        }
      ]
    },
  }
};
