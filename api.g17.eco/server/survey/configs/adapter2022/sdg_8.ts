/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_8: CompositeUtrConfigInterface = {
  code: 'adapter2022/sdg/8',
  compositeUtrCode: 'sdg/8',
  importConfigurationData: {
    variables: {
      a: {
        code: 'sdg/8.1'
      },
      b: {
        code: 'sdg/8.2'
      },
      c: {
        code: 'sdg/8.3'
      },
      d: {
        code: 'sdg/8.4'
      },
      e: {
        code: 'sdg/8.5'
      },
      f: {
        code: 'sdg/8.6'
      },
      g: {
        code: 'sdg/8.7'
      },
      h: {
        code: 'sdg/8.8'
      },
      i: {
        code: 'sdg/8.9'
      },
      j: {
        code: 'sdg/8.10'
      }
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.WeightedSum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}',
            },
            {
              type: CalculationType.Formula,
              formula: '{b}',
            },
            {
              type: CalculationType.Formula,
              formula: '{c}',
            },
            {
              type: CalculationType.Formula,
              formula: '{d}',
            },
            {
              type: CalculationType.Formula,
              formula: '{e}',
            },
            {
              type: CalculationType.Formula,
              formula: '{f}',
            },
            {
              type: CalculationType.Formula,
              formula: '{g}',
            },
            {
              type: CalculationType.Formula,
              formula: '{h}',
            },
            {
              type: CalculationType.Formula,
              formula: '{i}',
            },
            {
              type: CalculationType.Formula,
              formula: '{j}',
            },
            {
              type: CalculationType.Formula,
              formula: '0', // 8.a
            },
            {
              type: CalculationType.Formula,
              formula: '0', // 8.b
            },
          ]
        },
      ]
    },
  }
};
