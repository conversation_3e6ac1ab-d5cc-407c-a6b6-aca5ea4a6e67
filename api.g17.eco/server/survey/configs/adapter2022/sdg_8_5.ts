/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_8_5: CompositeUtrConfigInterface = {
  code: 'adapter2022/sdg/8.5',
  compositeUtrCode: 'sdg/8.5',
  groupName: 'SDG 8.5',
  groupData: {
    colour: '#f56a2f'
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'survey/generic/supplier-living-wage',
      },
      b: {
        code: 'survey/generic/supply-chain-spend',
      },
      c: {
        code: 'survey/sdg/10.3/discrimination-policy'
      },
      d: {
        code: 'survey/generic/pc-women-salary-to-men',
      },
      e: {
        code: 'gri/2020/405-1/a',
        valueListCode: 'female'
      },
      f: {
        code: 'gri/2020/405-1/b',
        valueListCode: 'male'
      },
      g: {
        code: 'gri/2020/405-1/b',
        valueListCode: 'female'
      },
      h: {
        code: 'survey/sdg/8.6/pc-new-hires-18-30',
      },
      i: {
        code: 'gri/2020/405-1/a',
        valueListCode: 'minority'
      },
      j: {
        code: 'survey/sdg/1.2/pc-employees-temp-permanent-contracts',
      },
      k: {
        code: 'survey/sdg/5.4/retention-employees-parental-leave',
      },
      l: {
        code: 'survey/sdg/8.2/company-transition-assistance-programmes',
      },
      m: {
        code: 'survey/sdg/8.5/pc-employees-18-30',
      },
      n: {
        code: 'survey/sdg/8.5/pc-employees-disability'
      },
      o: {
        code: 'survey/sdg/8.5/pc-employees-minority-group',
      },
      p: {
        code: 'survey/sdg/8.5/pc-employees-management-from-local-community',
      },
      q: {
        code: 'survey/sdg/8.5/does-company-support-suppliers-to',
        valueListCode: 'accommodation'
      },
      r: {
        code: 'survey/sdg/8.8/health-safety-training',
      },
      s: {
        code: 'gri/2020/404-1/a',
        valueListCode: 'male'
      },
      t: {
        code: 'gri/2020/102-7/a',
        valueListCode: 'org_scale_employees_male'
      },
      u: {
        code: 'gri/2020/102-7/a',
        valueListCode: 'org_scale_employees_female'
      },
      v: {
        code: 'gri/2020/404-1/a',
        valueListCode: 'female'
      },
      w: {
        code: 'survey/sdg/10.3/pc-employees-receive-career-review',
      },
      x: {
        code: 'gri/2020/405-1/a',
        valueListCode: 'disability'
      },
      y: {
        code: 'gri/2020/405-1/b',
        valueListCode: 'minority'
      },
      z: {
        code: 'gri/2020/405-1/b',
        valueListCode: 'disability'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.WeightedSum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '100*{a}/{b}',
              weight: 118/2883
            },
            {
              type: CalculationType.Formula,
              formula: 'resolveString("{c}","yes",100,0)',
              weight: 59/2883
            },
            {
              type: CalculationType.Formula,
              formula: '(linearDistribution({d}, 0.5, 1, 0, 100)-(100-linearDistribution(1/{d}, 0.5, 1, 0, 100)))',
              weight: 118/2883
            },
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution(50-abs({e}-50),0,50,0,100)',
              weight: 118/6727
            },
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution(50-abs({f}-50),0,50,0,100)',
              weight: 118/4805
            },
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution(50-abs({g}-50),0,50,0,100)',
              weight: 118/4805
            },
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution(30-abs({h}-30),0,30,0,100)',
              weight: 118/4805
            },
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution(50-abs({i}-30),0,30,0,100)',
              weight: 59/6727
            },
            {
              type: CalculationType.Formula,
              formula: '(100-{j})',
              weight: 118/2883
            },
            {
              type: CalculationType.Formula,
              formula: '{k}',
              weight: 118/2883
            },
            {
              type: CalculationType.Formula,
              formula: '{l}',
              weight: 59/961
            },
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution(30-abs({m}-30),0,30,0,100)',
              weight: 118/961
            },
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution({n},0,20,0,100)',
              weight: 118/961
            },
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution({o},0,50,0,100)',
              weight: 118/961
            },
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution({p},0,50,0,100)',
              weight: 118/961
            },
            {
              type: CalculationType.Formula,
              formula: 'resolveString("{q}", "yes", 100)',
              weight: 118/961
            },
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution(100*{r}/ (({s} * ({t} / ({t} + {u})) ) + ({v} * ({u} / ({t} + {u})) )),0,10,0,100)',
              weight: 59/3844
            },
            {
              type: CalculationType.Formula,
              formula: '{w}',
              weight: 118/2883
            },
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution(50-abs({x}-5),0,5,0,100)',
              weight: 59/6727
            },
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution(50-abs({y}-30),0,30,0,100)',
              weight: 118/4805
            },
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution(50-abs({z}-5),0,5,0,100)',
              weight: 118/4805
            },
          ],
        },
      ]
    },
  }
};
