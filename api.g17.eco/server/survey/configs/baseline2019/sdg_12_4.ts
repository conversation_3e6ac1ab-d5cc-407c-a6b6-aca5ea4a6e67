/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const sdg_12_4: CompositeUtrConfigInterface = {
  code: 'baseline2019/sdg/12.4',
  compositeUtrCode: 'sdg/12.4',
  groupName: 'SDG 12.4',
  groupData: {
    colour: '#d48a37'
  },
  fragmentUtrCodes: [
    'survey/sdg/12.4/pc-ghg-emissions-1-2',
    'survey/generic/spend-on-environmental-protection',
    'survey/generic/opex-operational-expenditure',
    'survey/generic/num-toxic-spills',
    'survey/generic/num-spills',
    'survey/sdg/12.4/pc-facilities-with-relevant-water-quality',
    'survey/sdg/12.4/pc-hazardous-waste-recycled',
    'survey/sdg/12.4/pc-products-services-with-lifecycle-assessment',
  ],
  fragmentUtrConfiguration: {
    'survey/sdg/12.4/pc-ghg-emissions-1-2': [],
    'survey/generic/spend-on-environmental-protection': vc.allValueChain,
    'survey/generic/opex-operational-expenditure': vc.allValueChain,
    'survey/generic/num-toxic-spills': vc.allValueChain,
    'survey/generic/num-spills': vc.allValueChain,
    'survey/sdg/12.4/pc-facilities-with-relevant-water-quality': [],
    'survey/sdg/12.4/pc-hazardous-waste-recycled': [],
    'survey/sdg/12.4/pc-products-services-with-lifecycle-assessment': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'survey/sdg/12.4/pc-ghg-emissions-1-2',
      },
      b: {
        code: 'survey/generic/spend-on-environmental-protection',
      },
      c: {
        code: 'survey/generic/opex-operational-expenditure',
      },
      d: {
        code: 'survey/generic/num-toxic-spills',
      },
      e: {
        code: 'survey/generic/num-spills',
      },
      f: {
        code: 'survey/sdg/12.4/pc-facilities-with-relevant-water-quality',
      },
      g: {
        code: 'survey/sdg/12.4/pc-hazardous-waste-recycled',
      },
      h: {
        code: 'survey/sdg/12.4/pc-products-services-with-lifecycle-assessment',
      }
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '(100-{a})/6'
            },
            {
              type: CalculationType.Formula,
              formula: '(100*{b}/{c})/6'
            },
            {
              type: CalculationType.Formula,
              formula: '(100-(100*({d}/{e})))/6'
            },
            {
              type: CalculationType.Formula,
              formula: '{f}/6'
            },
            {
              type: CalculationType.Formula,
              formula: '{g}/6'
            },
            {
              type: CalculationType.Formula,
              formula: '{h}/6'
            }
          ]
        }
      ]
    }
  }
};
