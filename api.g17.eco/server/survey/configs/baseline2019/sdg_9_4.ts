/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

// const ghgMax = 15;
const ghgMultiplier = 1; //100 / ghgMax;

export const sdg_9_4: CompositeUtrConfigInterface = {
  code: 'baseline2019/sdg/9.4',
  compositeUtrCode: 'sdg/9.4',
  groupName: 'SDG 9.4',
  groupData: {
    colour: '#f56a2f'
  },
  fragmentUtrCodes: [
    'survey/generic/spend-on-environmental-protection',
    'survey/generic/opex-operational-expenditure',
    'survey/generic/ghg-emissions',
    'survey/generic/ebitda',
    'survey/sdg/9.4/pc-operations-with-monitoring',
    'survey/sdg/9.4/pc-infrastructure-not-upgraded',
    'survey/sdg/9.4/pc-carbon-equivalent-from-energy-in-products-services',
  ],
  fragmentUtrConfiguration: {
    'survey/generic/spend-on-environmental-protection': vc.allValueChain,
    'survey/generic/opex-operational-expenditure': vc.allValueChain,
    'survey/generic/ghg-emissions': vc.allValueChain,
    'survey/generic/ebitda': vc.allValueChainUseComposite,
    'survey/sdg/9.4/pc-operations-with-monitoring': vc.allValueChain,
    'survey/sdg/9.4/pc-infrastructure-not-upgraded': vc.allValueChain,
    'survey/sdg/9.4/pc-carbon-equivalent-from-energy-in-products-services': vc.allValueChain,
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'survey/generic/spend-on-environmental-protection',
      },
      b: {
        code: 'survey/generic/opex-operational-expenditure',
      },
      c: {
        code: 'survey/generic/ghg-emissions',
      },
      d: {
        code: 'survey/generic/ebitda',
      },
      e: {
        code: 'survey/sdg/9.4/pc-operations-with-monitoring',
      },
      f: {
        code: 'survey/sdg/9.4/pc-infrastructure-not-upgraded',
      },
      g: {
        code: 'survey/sdg/9.4/pc-carbon-equivalent-from-energy-in-products-services',
      }
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '0.2*(100*{a}/{b})'
            },
            {
              type: CalculationType.Formula,
              formula: '0.2*(100-(' + ghgMultiplier + '*({c}/({d} * 1000000))))'
            },
            {
              type: CalculationType.Formula,
              formula: '0.2*{e}'
            },
            {
              type: CalculationType.Formula,
              formula: '0.2*{f}'
            },
            {
              type: CalculationType.Formula,
              formula: '0.2*{g}'
            },
          ]
        }
      ]
    },
  }
};

