/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const sdg_14: CompositeUtrConfigInterface = {
  code: 'composite/sdg/14',
  compositeUtrCode: 'sdg/14',
  fragmentUtrCodes: [
    'sdg/14.1',
  ],
  fragmentUtrConfiguration: {
    'sdg/14.1': vc.allValueChain,

  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'sdg/14.1'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '(0.3*{a})'
            },
          ]
        }
      ]
    },
  }
};
