/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const sdg_6: CompositeUtrConfigInterface = {
  code: 'composite/sdg/6',
  compositeUtrCode: 'sdg/6',
  fragmentUtrCodes: [
    // 'sdg/6.1',
    // 'sdg/6.2',
    'sdg/6.3',
    'sdg/6.4',
    // 'sdg/6.5',
    'sdg/6.6',
  ],
  fragmentUtrConfiguration: {
    // 'sdg/6.1': [ vc.Operations ],
    // 'sdg/6.2': [ vc.Operations ],
    'sdg/6.3': vc.allValueChain,
    'sdg/6.4': vc.allValueChain,
    // 'sdg/6.5': [ vc.ExternalCSR ],
    'sdg/6.6': [vc.Operations, vc.ProductServices],
  },
  importConfigurationData: {
    variables: {
      // a: {
      //   code: 'sdg/6.1'
      // },
      // b: {
      //   code: 'sdg/6.2'
      // },
      c: {
        code: 'sdg/6.3'
      },
      d: {
        code: 'sdg/6.4'
      },
      // e: {
      //   code: 'sdg/6.5'
      // },
      f: {
        code: 'sdg/6.6'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            // {
            //   type: CalculationType.Formula,
            //   formula: '(0.2*{a})'
            // },
            // {
            //   type: CalculationType.Formula,
            //   formula: '(0.2*{b})'
            // },
            {
              type: CalculationType.Formula,
              formula: '(0.2*{c})'
            },
            {
              type: CalculationType.Formula,
              formula: '(0.15*{d})'
            },
            // {
            //   type: CalculationType.Formula,
            //   formula: '(0.15*{e})'
            // },
            {
              type: CalculationType.Formula,
              formula: '(0.1*{f})'
            }
          ]
        }
      ]
    },
  }
};
