/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const sdg_6_3: CompositeUtrConfigInterface = {
  code: 'baseline2019/sdg/6.3',
  compositeUtrCode: 'sdg/6.3',
  groupName: 'SDG 6.3',
  groupData: {
    colour: '#00aed9'
  },
  fragmentUtrCodes: [
    'survey/sdg/6.3/unplanned-waste-water-discharges',
    'survey/sdg/14.1/normalization/total-water-discharged',
    'survey/sdg/6.3/pc-waste-water-treated',
  ],
  fragmentUtrConfiguration: {
    'survey/sdg/6.3/unplanned-waste-water-discharges': vc.allValueChain,
    'survey/sdg/14.1/normalization/total-water-discharged': vc.allValueChain,
    'survey/sdg/6.3/pc-waste-water-treated': vc.allValueChain,
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'survey/sdg/6.3/unplanned-waste-water-discharges',
      },
      b: {
        code: 'survey/sdg/14.1/normalization/total-water-discharged',
      },
      c: {
        code: 'survey/sdg/6.3/pc-waste-water-treated',
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '0.5*(100-(100*{a}/{b}))'
            },
            {
              type: CalculationType.Formula,
              formula: '0.5*{c}'
            },
          ]
        }
      ]
    },
  }
};
