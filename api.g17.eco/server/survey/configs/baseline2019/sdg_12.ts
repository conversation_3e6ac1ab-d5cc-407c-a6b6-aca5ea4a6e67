/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const sdg_12: CompositeUtrConfigInterface = {
  code: 'composite/sdg/12',
  compositeUtrCode: 'sdg/12',
  fragmentUtrCodes: [
    // 'sdg/12.1',
    'sdg/12.2',
    // 'sdg/12.3',
    'sdg/12.4',
    // 'sdg/12.5',
    // 'sdg/12.6',
    // 'sdg/12.8',
  ],
  fragmentUtrConfiguration: {
    // 'sdg/12.1': vc.allValueChain,
    'sdg/12.2': vc.allValueChain,
    // 'sdg/12.3': vc.allValueChain,
    'sdg/12.4': vc.allValueChain,
    // 'sdg/12.5': vc.allValueChain,
    // 'sdg/12.8': vc.allValueChain,
  },
  importConfigurationData: {
    variables: {
      // a: {
      //   code: 'sdg/12.1'
      // },
      b: {
        code: 'sdg/12.2'
      },
      // c: {
      //   code: 'sdg/12.3'
      // },
      d: {
        code: 'sdg/12.4'
      },
      // e: {
      //   code: 'sdg/12.5'
      // },
      // f: {
      //   code: 'sdg/12.6'
      // },
      // g: {
      //   code: 'sdg/12.8'
      // },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            // {
            //   type: CalculationType.Formula,
            //   formula: '(0.05*{a})'
            // },
            {
              type: CalculationType.Formula,
              formula: '(0.2*{b})'
            },
            // {
            //   type: CalculationType.Formula,
            //   formula: '(0.12*{c})'
            // },
            {
              type: CalculationType.Formula,
              formula: '(0.15*{d})'
            },
            // {
            //   type: CalculationType.Formula,
            //   formula: '(0.2*{e})'
            // },
            // {
            //   type: CalculationType.Formula,
            //   formula: '(0.15*{f})'
            // },
            // {
            //   type: CalculationType.Formula,
            //   formula: '(0.08*{g})'
            // }
          ]
        }
      ]
    },
  }
};
