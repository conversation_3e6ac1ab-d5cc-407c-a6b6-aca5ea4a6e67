/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import { ConditionTypes } from '../../../rules/condition';
import * as vc from '../../../util/valueChain';

export const sdg_6_4: CompositeUtrConfigInterface = {
  code: 'baseline2019/sdg/6.4',
  compositeUtrCode: 'sdg/6.4',
  groupName: 'SDG 6.4',
  groupData: {
    colour: '#00aed9'
  },
  fragmentUtrCodes: [
    'survey/generic/pc-water-recycled',
    'survey/sdg/6.4/pc-water-withdrawl-from-scarce',
    'survey/sdg/6.4/pc-operations-at-risk-from-access-to-water',
    'survey/sdg/6.4/pc-premises-water-tech-deployed',
    'survey/sdg/6.4/pc-change-water-consumption-after-tech',
    'survey/sdg/6.4/water-consumption',
    'survey/sdg/6.4/total-water-withdrawl'
  ],
  fragmentUtrConfiguration: {
    'survey/generic/pc-water-recycled': vc.allValueChain,
    'survey/sdg/6.4/pc-water-withdrawl-from-scarce': vc.allValueChain,
    'survey/sdg/6.4/pc-operations-at-risk-from-access-to-water': vc.allValueChain,
    'survey/sdg/6.4/pc-premises-water-tech-deployed': vc.allValueChain,
    'survey/sdg/6.4/pc-change-water-consumption-after-tech': vc.allValueChain,
    'survey/sdg/6.4/water-consumption': vc.allValueChain,
    'survey/sdg/6.4/total-water-withdrawl': vc.allValueChain,
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'survey/generic/pc-water-recycled',
      },
      b: {
        code: 'survey/sdg/6.4/pc-water-withdrawl-from-scarce',
      },
      c: {
        code: 'survey/sdg/6.4/pc-operations-at-risk-from-access-to-water',
      },
      // d: {
      //   code: 'survey/sdg/6.4/pc-premises-water-tech-deployed',
      // },
      e: {
        code: 'survey/sdg/6.4/pc-change-water-consumption-after-tech',
      },
      f: {
        code: 'survey/sdg/6.4/water-consumption',
      },
      g: {
        code: 'survey/sdg/6.4/total-water-withdrawl',
      }
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          conditions: [
            {
              type: ConditionTypes.Formula,
              condition: '{g}',
              result: {
                gte: 1
              },
            },
          ],
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}/5'
            },
            {
              type: CalculationType.Formula,
              formula: '(100-{b})/5'
            },
            {
              type: CalculationType.Formula,
              formula: '(100-{c})/5'
            },
            // {
            //   type: CalculationType.Formula,
            //   formula: '100-{d}'
            // },
            {
              type: CalculationType.Formula,
              formula: '(100-{e})/5'
            },
            {
              type: CalculationType.Formula,
              formula: '(100-(100*{f}/{g}))/5'
            },
          ]
        }
      ]
    },
  }
};
