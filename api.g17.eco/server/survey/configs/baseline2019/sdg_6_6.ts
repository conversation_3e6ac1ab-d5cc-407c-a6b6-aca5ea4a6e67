/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const sdg_6_6: CompositeUtrConfigInterface = {
  code: 'baseline2019/sdg/6.6',
  compositeUtrCode: 'sdg/6.6',
  groupName: 'SDG 6.6',
  groupData: {
    colour: '#00aed9'
  },
  fragmentUtrCodes: [
    'survey/generic/num-toxic-spills',
    'survey/generic/num-spills',
    'survey/generic/pc-water-from-3rd-party',
  ],
  fragmentUtrConfiguration: {
    'survey/generic/num-toxic-spills': vc.allValueChain,
    'survey/generic/num-spills': vc.allValueChain,
    'survey/generic/pc-water-from-3rd-party': vc.allValueChain,
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'survey/generic/num-toxic-spills',
      },
      b: {
        code: 'survey/generic/num-spills',
      },
      c: {
        code: 'survey/generic/pc-water-from-3rd-party',
      }
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '0.5*(100-(100*{a}/{b})))'
            },
            {
              type: CalculationType.Formula,
              formula: '0.5*{c}'
            },
          ]
        }
      ]
    },
  }
};
