/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const sdg_7_2: CompositeUtrConfigInterface = {
  code: 'baseline2019/sdg/7.2',
  compositeUtrCode: 'sdg/7.2',
  groupName: 'SDG 7.2',
  groupData: {
    colour: '#ffb437'
  },
  fragmentUtrCodes: [
    'survey/generic/pc-renewable-fuel-consumption',
    'survey/sdg/7.2/progress-towards-renewable-energy-production-target',
    'survey/sdg/7.2/progress-towards-renewable-energy-consumption-target',
  ],
  fragmentUtrConfiguration: {
    'survey/generic/pc-renewable-fuel-consumption': vc.allValueChain,
    'survey/sdg/7.2/progress-towards-renewable-energy-production-target': [],
    'survey/sdg/7.2/progress-towards-renewable-energy-consumption-target': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'survey/generic/pc-renewable-fuel-consumption',
      },
      b: {
        code: 'survey/sdg/7.2/progress-towards-renewable-energy-production-target',
      },
      c: {
        code: 'survey/sdg/7.2/progress-towards-renewable-energy-consumption-target',
      }
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}/3'
            },
            {
              type: CalculationType.Formula,
              formula: '{b}/3'
            },
            {
              type: CalculationType.Formula,
              formula: '{c}/3'
            },
          ],
        }
      ]
    },
  }
};
