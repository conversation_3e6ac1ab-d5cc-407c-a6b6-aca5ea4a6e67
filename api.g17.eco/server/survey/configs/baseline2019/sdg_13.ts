/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const sdg_13: CompositeUtrConfigInterface = {
  code: 'composite/sdg/13',
  compositeUtrCode: 'sdg/13',
  fragmentUtrCodes: [
    'sdg/13.1',
    // 'sdg/13.2',
    // 'sdg/13.3',
  ],
  fragmentUtrConfiguration: {
    'sdg/13.1': vc.allValueChain,
    // 'sdg/13.2': vc.allValueChain,
    // 'sdg/13.3': vc.allValueChain,
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'sdg/13.1'
      },
      // b: {
      //   code: 'sdg/13.2'
      // },
      // c: {
      //   code: 'sdg/13.3'
      // },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '((2/3)*{a})'
            },
            // {
            //   type: CalculationType.Formula,
            //   formula: '((1/6)*{b})'
            // },
            // {
            //   type: CalculationType.Formula,
            //   formula: '((1/6)*{c})'
            // }
          ]
        }
      ]
    },
  }
};
