/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { Blueprint } from '../../../repository/BlueprintRepository';
import { sdg_5 } from './sdg_5';
import { sdg_6 } from './sdg_6';
import { sdg_7 } from './sdg_7';
import { sdg_8 } from './sdg_8';
import { sdg_9 } from './sdg_9';
import { sdg_12 } from './sdg_12';
import { sdg_13 } from './sdg_13';
import { sdg_14 } from './sdg_14';
import { sdg_5_1 } from './sdg_5_1';
import { sdg_6_3 } from './sdg_6_3';
import { sdg_6_4 } from './sdg_6_4';
import { sdg_6_6 } from './sdg_6_6';
import { sdg_7_2 } from './sdg_7_2';
import { sdg_7_3 } from './sdg_7_3';
import { sdg_8_4 } from './sdg_8_4';
import { sdg_9_4 } from './sdg_9_4';
import { sdg_12_2 } from './sdg_12_2';
import { sdg_12_4 } from './sdg_12_4';
import { sdg_13_1 } from './sdg_13_1';
import { sdg_14_1 } from './sdg_14_1';
import { Blueprints } from '../../blueprints';
import { sdg_score } from './sdg_score';
import { blueprintDefaultUnitConfig } from '../../../service/units/unitTypes';
import config from '../../../config';

const assetsCDN = config.assets.cdn;

export const baseline2019: Blueprint = {
  name: 'Baseline 2019',
  code: Blueprints.Baseline2019,
  unitConfig: blueprintDefaultUnitConfig,
  references: [],
  logo: {
    src: assetsCDN + '/images/logos/wwg.svg',
    alt: 'WWG',
  },
  additionalConfigs: [
    { compositeConfig: sdg_score.code, },
    { compositeConfig: sdg_5.code, },
    { compositeConfig: sdg_6.code, },
    { compositeConfig: sdg_7.code, },
    { compositeConfig: sdg_8.code, },
    { compositeConfig: sdg_9.code, },
    { compositeConfig: sdg_12.code, },
    { compositeConfig: sdg_13.code, },
    { compositeConfig: sdg_14.code, },
  ],
  forms: [
    {
      utrGroupConfig: {
        groupName: 'Company Level',
        utrCodes: [
          'survey/generic/capex-capital-expenditure',
          'survey/generic/ebitda',
        ]
      }
    },
    { compositeConfig: sdg_5_1.code, },
    { compositeConfig: sdg_6_3.code, },
    { compositeConfig: sdg_6_4.code, },
    { compositeConfig: sdg_6_6.code, },
    { compositeConfig: sdg_7_2.code, },
    { compositeConfig: sdg_7_3.code, },
    { compositeConfig: sdg_8_4.code, },
    { compositeConfig: sdg_9_4.code, },
    { compositeConfig: sdg_12_2.code, },
    { compositeConfig: sdg_12_4.code, },
    { compositeConfig: sdg_13_1.code, },
    { compositeConfig: sdg_14_1.code, },
  ],
};

export const baseline2019ConfigCodeMap = {
  [sdg_5.code]: sdg_5,
  [sdg_6.code]: sdg_6,
  [sdg_7.code]: sdg_7,
  [sdg_8.code]: sdg_8,
  [sdg_9.code]: sdg_9,
  [sdg_12.code]: sdg_12,
  [sdg_13.code]: sdg_13,
  [sdg_14.code]: sdg_14,
  [sdg_5_1.code]: sdg_5_1,
  [sdg_6_3.code]: sdg_6_3,
  [sdg_6_4.code]: sdg_6_4,
  [sdg_6_6.code]: sdg_6_6,
  [sdg_7_2.code]: sdg_7_2,
  [sdg_7_3.code]: sdg_7_3,
  [sdg_8_4.code]: sdg_8_4,
  [sdg_9_4.code]: sdg_9_4,
  [sdg_12_2.code]: sdg_12_2,
  [sdg_12_4.code]: sdg_12_4,
  [sdg_13_1.code]: sdg_13_1,
  [sdg_14_1.code]: sdg_14_1,
  [sdg_score.code]: sdg_score,
};
