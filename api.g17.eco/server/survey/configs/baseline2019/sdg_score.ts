/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const sdg_score: CompositeUtrConfigInterface = {
  code: 'composite/sdg/score',
  compositeUtrCode: 'sdg/score',
  fragmentUtrCodes: [
    'sdg/5',
    'sdg/6',
    'sdg/7',
    'sdg/8',
    'sdg/9',
    'sdg/12',
    'sdg/13',
    'sdg/14',
  ],
  fragmentUtrConfiguration: {
    'sdg/5': vc.allValueChain,
    'sdg/6': vc.allValueChain,
    'sdg/7': vc.allValueChain,
    'sdg/8': vc.allValueChain,
    'sdg/9': vc.allValueChain,
    'sdg/12': vc.allValueChain,
    'sdg/13': vc.allValueChain,
    'sdg/14': vc.allValueChain,
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'sdg/5'
      },
      b: {
        code: 'sdg/6'
      },
      c: {
        code: 'sdg/7'
      },
      d: {
        code: 'sdg/8'
      },
      e: {
        code: 'sdg/9'
      },
      f: {
        code: 'sdg/12'
      },
      g: {
        code: 'sdg/13'
      },
      h: {
        code: 'sdg/14'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Average,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}'
            },
            {
              type: CalculationType.Formula,
              formula: '{b}'
            },
            {
              type: CalculationType.Formula,
              formula: '{c}'
            },
            {
              type: CalculationType.Formula,
              formula: '{d}'
            },
            {
              type: CalculationType.Formula,
              formula: '{e}'
            },
            {
              type: CalculationType.Formula,
              formula: '{f}'
            },
            {
              type: CalculationType.Formula,
              formula: '{g}'
            },
            {
              type: CalculationType.Formula,
              formula: '{h}'
            },
          ]
        }
      ]
    },
  }
};
