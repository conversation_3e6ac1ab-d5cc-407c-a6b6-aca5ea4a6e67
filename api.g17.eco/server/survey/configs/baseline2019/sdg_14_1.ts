/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const sdg_14_1: CompositeUtrConfigInterface = {
  code: 'baseline2019/sdg/14.1',
  compositeUtrCode: 'sdg/14.1',
  groupName: 'SDG 14.1',
  groupData: {
    colour: '#007db8'
  },
  fragmentUtrCodes: [
    'survey/sdg/14.1/normalization/total-water-discharged',
    'survey/sdg/14.1/pc-water-discharge-recycled',
    'survey/sdg/14.1/total-waste-water-discharged',
    'survey/sdg/14.1/num-toxic-spills-marine-environment',
    'survey/generic/num-spills',
    'survey/sdg/14.1/pc-packaging-recyclable',
    'survey/sdg/14.1/pc-plastic-waste-recycled'
  ],
  fragmentUtrConfiguration: {
    'survey/sdg/14.1/normalization/total-water-discharged': vc.allValueChain,
    'survey/sdg/14.1/pc-water-discharge-recycled': [],
    'survey/sdg/14.1/total-waste-water-discharged': [vc.ProductServices, vc.Operations],
    'survey/sdg/14.1/num-toxic-spills-marine-environment': [],
    'survey/generic/num-spills': vc.allValueChain,
    'survey/sdg/14.1/pc-packaging-recyclable': [],
    'survey/sdg/14.1/pc-plastic-waste-recycled': []
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'survey/sdg/14.1/normalization/total-water-discharged',
      },
      b: {
        code: 'survey/sdg/14.1/pc-water-discharge-recycled',
      },
      c: {
        code: 'survey/sdg/14.1/total-waste-water-discharged',
      },
      d: {
        code: 'survey/sdg/14.1/num-toxic-spills-marine-environment',
      },
      e: {
        code: 'survey/generic/num-spills',
      },
      f: {
        code: 'survey/sdg/14.1/pc-packaging-recyclable',
      },
      g: {
        code: 'survey/sdg/14.1/pc-plastic-waste-recycled',
      }
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Average,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '(100-(100*{c}/{a}))/6'
            },
            {
              type: CalculationType.Formula,
              formula: '{b}/6'
            },
            {
              type: CalculationType.Formula,
              formula: '(100-(100*{d}/{e}))/6'
            },
            {
              type: CalculationType.Formula,
              formula: '{f}/6'
            },
            {
              type: CalculationType.Formula,
              formula: '{g}/6'
            },
          ]
        },
      ]
    },
  }
};
