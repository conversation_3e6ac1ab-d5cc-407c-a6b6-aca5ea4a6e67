/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const sdg_8_4: CompositeUtrConfigInterface = {
  code: 'baseline2019/sdg/8.4',
  compositeUtrCode: 'sdg/8.4',
  groupName: 'SDG 8.4',
  groupData: {
    colour: '#921036'
  },
  fragmentUtrCodes: [
    'survey/generic/pc-change-in-energy-from-initiatives',
    'survey/generic/pc-water-recycled',
    'survey/generic/pc-renewable-fuel-consumption',
    'survey/sdg/8.4/pc-materials-used-physical-goods-that-can-be-repurposed',
  ],
  fragmentUtrConfiguration: {
    'survey/generic/pc-change-in-energy-from-initiatives': vc.allValueChain,
    'survey/generic/pc-water-recycled': vc.allValueChain,
    'survey/generic/pc-renewable-fuel-consumption': vc.allValueChain,
    'survey/sdg/8.4/pc-materials-used-physical-goods-that-can-be-repurposed': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'survey/generic/pc-change-in-energy-from-initiatives',
      },
      b: {
        code: 'survey/generic/pc-water-recycled',
      },
      c: {
        code: 'survey/generic/pc-renewable-fuel-consumption',
      },
      d: {
        code: 'survey/sdg/8.4/pc-materials-used-physical-goods-that-can-be-repurposed',
      }
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '(100-{a})/4'
            },
            {
              type: CalculationType.Formula,
              formula: '{b}/4'
            },
            {
              type: CalculationType.Formula,
              formula: '{c}/4'
            },
            {
              type: CalculationType.Formula,
              formula: '{d}/4'
            },
          ],
        }
      ]
    },
  }
};
