/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

const energyMax = 0.01;
const energyMultiplier = 1; //100 / energyMax;

export const sdg_7_3: CompositeUtrConfigInterface = {
  code: 'baseline2019/sdg/7.3',
  compositeUtrCode: 'sdg/7.3',
  groupName: 'SDG 7.3',
  groupData: {
    colour: '#ffb437'
  },
  fragmentUtrCodes: [
    'survey/generic/pc-change-in-energy-from-initiatives',
    'survey/generic/energy-consumption',
    'survey/generic/ebitda'
  ],
  fragmentUtrConfiguration: {
    'survey/generic/pc-change-in-energy-from-initiatives': vc.allValueChain,
    'survey/generic/energy-consumption': vc.allValueChain,
    'survey/generic/ebitda': vc.allValueChainUseComposite
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'survey/generic/pc-change-in-energy-from-initiatives',
      },
      b: {
        code: 'survey/generic/energy-consumption',
      },
      c: {
        code: 'survey/generic/ebitda',
      }
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '0.5*(100-{a})'
            },
            {
              type: CalculationType.Formula,
              formula: '0.5*(100 - (' + energyMultiplier + ' * (100 * {b} * 0.0002777777778 / ({c} * 1000000) )))'
            }
          ],
        }
      ]
    },
  }
};
