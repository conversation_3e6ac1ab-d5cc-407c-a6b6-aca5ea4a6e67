/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

const ghgMax = 15;
const ghgMultiplier = 1; //100 / ghgMax;
const co2Max = 15;
const co2Multiplier = 1; //100 / co2Max;

export const sdg_13_1: CompositeUtrConfigInterface = {
  code: 'baseline2019/sdg/13.1',
  compositeUtrCode: 'sdg/13.1',
  groupName: 'SDG 13.1',
  groupData: {
    colour: '#497744'
  },
  fragmentUtrCodes: [
    'survey/sdg/13.1/pc-r-d-technology',
    'survey/sdg/13.1/investment-for-mitigating-climate-change-risks',
    'survey/generic/capex-capital-expenditure',
    'survey/generic/ghg-emissions',
    'survey/sdg/13.1/co2-emissions',
    'survey/generic/ebitda',
    'survey/sdg/13.1/pc-r-d-technology-spend',
    'survey/sdg/13.1/pc-revenue-from-low-carbon-initiatives'
  ],
  fragmentUtrConfiguration: {
    'survey/sdg/13.1/pc-r-d-technology': vc.allValueChain,
    'survey/sdg/13.1/investment-for-mitigating-climate-change-risks': vc.allValueChain,
    'survey/generic/capex-capital-expenditure': vc.allValueChainUseComposite,
    'survey/generic/ghg-emissions': vc.allValueChain,
    'survey/sdg/13.1/co2-emissions': vc.allValueChain,
    'survey/generic/ebitda': vc.allValueChainUseComposite,
    'survey/sdg/13.1/pc-r-d-technology-spend': vc.allValueChain,
    'survey/sdg/13.1/pc-revenue-from-low-carbon-initiatives': vc.allValueChain
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'survey/sdg/13.1/pc-r-d-technology',
      },
      b: {
        code: 'survey/sdg/13.1/investment-for-mitigating-climate-change-risks',
      },
      c: {
        code: 'survey/generic/capex-capital-expenditure',
      },
      d: {
        code: 'survey/generic/ghg-emissions',
      },
      e: {
        code: 'survey/sdg/13.1/co2-emissions',
      },
      f: {
        code: 'survey/generic/ebitda',
      },
      g: {
        code: 'survey/sdg/13.1/pc-r-d-technology-spend',
      },
      h: {
        code: 'survey/sdg/13.1/pc-revenue-from-low-carbon-initiatives',
      }
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}/6'
            },
            {
              type: CalculationType.Formula,
              formula: '(100*{b}/{c})/6'
            },
            {
              type: CalculationType.Formula,
              formula: '(100 - (' + ghgMultiplier + ' * ({d} / ({f} * 1000000) )))/6',
            },
            {
              type: CalculationType.Formula,
              formula: '(100 - (' + co2Multiplier + ' * ({e} / ({f} * 1000000) )))/6',
            },
            {
              type: CalculationType.Formula,
              formula: '{g}/6'
            },
            {
              type: CalculationType.Formula,
              formula: '{h}/6'
            },
          ]
        }
      ]
    },
  }
};
