/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const sdg_12_2: CompositeUtrConfigInterface = {
  code: 'baseline2019/sdg/12.2',
  compositeUtrCode: 'sdg/12.2',
  groupName: 'SDG 12.2',
  groupData: {
    colour: '#d48a37'
  },
  fragmentUtrCodes: [
    'survey/generic/pc-change-in-energy-from-initiatives',
    'survey/sdg/12.2/pc-energy-from-renewables',
    'survey/generic/pc-water-from-3rd-party',
    'survey/generic/pc-renewable-fuel-consumption',
    'survey/sdg/12.2/pc-recycled-materials-in-manufacturing',
  ],
  fragmentUtrConfiguration: {
    'survey/generic/pc-change-in-energy-from-initiatives': vc.allValueChain,
    'survey/sdg/12.2/pc-energy-from-renewables': vc.allValueChain,
    'survey/generic/pc-water-from-3rd-party': vc.allValueChain,
    'survey/generic/pc-renewable-fuel-consumption': vc.allValueChain,
    'survey/sdg/12.2/pc-recycled-materials-in-manufacturing': vc.allValueChain,
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'survey/generic/pc-change-in-energy-from-initiatives',
      },
      b: {
        code: 'survey/sdg/12.2/pc-energy-from-renewables',
      },
      c: {
        code: 'survey/generic/pc-water-from-3rd-party',
      },
      d: {
        code: 'survey/generic/pc-renewable-fuel-consumption',
      },
      e: {
        code: 'survey/sdg/12.2/pc-recycled-materials-in-manufacturing',
      }
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '(100-{a})/6'
            },
            {
              type: CalculationType.Formula,
              formula: '{b}/6'
            },
            {
              type: CalculationType.Formula,
              formula: '{c}/6'
            },
            {
              type: CalculationType.Formula,
              formula: '{d}/6'
            },
            {
              type: CalculationType.Formula,
              formula: '{e}/6'
            }
          ]
        }
      ]
    }
  }
};
