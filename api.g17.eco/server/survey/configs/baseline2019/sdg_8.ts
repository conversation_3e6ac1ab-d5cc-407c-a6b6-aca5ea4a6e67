/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const sdg_8: CompositeUtrConfigInterface = {
  code: 'composite/sdg/8',
  compositeUtrCode: 'sdg/8',
  fragmentUtrCodes: [
    'sdg/8.4',
  ],
  fragmentUtrConfiguration: {
    'sdg/8.4': vc.allValueChain,
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'sdg/8.4'
      }
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '0.1*{a}'
            }
          ]
        }
      ]
    },
  }
};
