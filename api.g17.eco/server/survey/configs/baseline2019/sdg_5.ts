/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const sdg_5: CompositeUtrConfigInterface = {
  code: 'baseline2019/sdg/5',
  compositeUtrCode: 'sdg/5',
  groupName: 'SDG 5',
  groupData: {
    colour: '#f83631'
  },
  fragmentUtrCodes: [
    'sdg/5.1',
  ],
  fragmentUtrConfiguration: {
    'sdg/5.1': vc.allValueChain,
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'sdg/5.1',
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Average,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '0.25*{a}'
            },
          ]
        }
      ]
    },
  }
};
