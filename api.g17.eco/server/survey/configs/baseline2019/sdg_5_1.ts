/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const sdg_5_1: CompositeUtrConfigInterface = {
  code: 'baseline2019/sdg/5.1',
  compositeUtrCode: 'sdg/5.1',
  groupName: 'SDG 5.1',
  groupData: {
    colour: '#f83631'
  },
  fragmentUtrCodes: [
    'survey/sdg/generic/pc-women-salary-to-men',
  ],
  fragmentUtrConfiguration: {
    'survey/sdg/generic/pc-women-salary-to-men': vc.allValueChain,
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'survey/sdg/generic/pc-women-salary-to-men',
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '0.25*{a}'
            },
          ]
        }
      ]
    },
  }
};
