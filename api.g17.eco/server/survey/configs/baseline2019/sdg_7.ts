/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const sdg_7: CompositeUtrConfigInterface = {
  code: 'composite/sdg/7',
  compositeUtrCode: 'sdg/7',
  fragmentUtrCodes: [
    // 'sdg/7.1',
    'sdg/7.2',
    'sdg/7.3',
  ],
  fragmentUtrConfiguration: {
    // 'sdg/7.1': vc.allValueChain,
    'sdg/7.2': vc.allValueChain,
    'sdg/7.3': vc.allValueChain,
  },
  importConfigurationData: {
    variables: {
      // a: {
      //   code: 'sdg/7.1'
      // },
      b: {
        code: 'sdg/7.2'
      },
      c: {
        code: 'sdg/7.3'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            // {
            //   type: CalculationType.Formula,
            //   formula: '{a}/3'
            // },
            {
              type: CalculationType.Formula,
              formula: '{b}/3'
            },
            {
              type: CalculationType.Formula,
              formula: '{c}/3'
            }
          ]
        }
      ]
    },
  }
};
