/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const sdg_9: CompositeUtrConfigInterface = {
  code: 'composite/sdg/9',
  compositeUtrCode: 'sdg/9',
  fragmentUtrCodes: [
    'sdg/9.4'
  ],
  fragmentUtrConfiguration: {
    'sdg/9.4': vc.allValueChain,
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'sdg/9.4'
      }
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '(0.2 * {a})',
            },
          ]
        },
      ]
    },
  }
};
