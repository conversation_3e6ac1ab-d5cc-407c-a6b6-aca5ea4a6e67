/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const generic_pc_employee_working_days_lost: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/generic/pc-employee-working-days-lost',
  compositeUtrCode: 'survey/generic/pc-employee-working-days-lost',
  fragmentUtrCodes: [
    'gri/2020/403-9/a',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/403-9/a': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/403-9/a',
        valueListCode: 'injuries_company_employees_high-consequence_work-related_injuries_number'
      },
      b: {
        code: 'gri/2020/403-9/a',
        valueListCode: 'injuries_company_employees_recordable_injuries_number'
      },
      c: {
        code: 'gri/2020/403-9/a',
        valueListCode: 'injuries_company_employees_hours_worked'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              // 200000 = assumes 100 workers at 2000 working hours/year
              formula: '200000 * ({a}+{b})/{c}'
            },
          ]
        },
      ]
    },
  }
};
