/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_16_5_pc_business_partners_anti_corruption_policies_communicated: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/16.5/pc-business-partners-anti-corruption-policies-communicated',
  compositeUtrCode: 'survey/sdg/16.5/pc-business-partners-anti-corruption-policies-communicated',
  fragmentUtrCodes: [
    'gri/2020/205-2/c',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/205-2/c': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/205-2/c',
        valueListCode: 'number'
      },
      b: {
        code: 'gri/2020/205-2/c',
        valueListCode: 'number_business_partners'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '100*{a}/{b}'
            },
          ],
        }
      ]
    },
  }
};
