/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const generic_pc_women_salary_to_men: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/generic/pc-women-salary-to-men',
  compositeUtrCode: 'survey/generic/pc-women-salary-to-men',
  fragmentUtrCodes: [
    'gri/2020/405-2/a',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/405-2/a': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/405-2/a',
        valueListCode: 'numerator_female'
      },
      b: {
        code: 'gri/2020/405-2/a',
        valueListCode: 'denominator_male'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}/{b}'
            },
          ],
        }
      ]
    },
  }
};
