/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_15_1_pc_raw_materials_sustainably_sourced: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/15.1/pc-raw-materials-sustainably-sourced',
  compositeUtrCode: 'survey/sdg/15.1/pc-raw-materials-sustainably-sourced',
  fragmentUtrCodes: [
    'gri/2020/301-1',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/301-1': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/301-1',
        valueListCode: 'renewable_materials'
      },
      b: {
        code: 'gri/2020/301-1',
        valueListCode: 'non_renewable_materials'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '100*{a}/({a}+ifNA("{b}",0))'
            },
          ],
        },
      ]
    },
  }
};
