/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const sdg_12_4_pc_ghg_emissions_1_2: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/12.4/pc-ghg-emissions-1-2',
  compositeUtrCode: 'survey/sdg/12.4/pc-ghg-emissions-1-2',
  fragmentUtrCodes: [
    'gri/2020/305-1/a',
    'gri/2020/305-2/a',
    'gri/2020/305-2/b',
    'gri/2020/305-3/a',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/305-1/a': vc.allValueChain,
    'gri/2020/305-2/a': vc.allValueChain,
    'gri/2020/305-2/b': vc.allValueChain,
    'gri/2020/305-3/a': vc.allValueChain,
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/305-1/a',
      },
      b: {
        code: 'gri/2020/305-2/a',
      },
      c: {
        code: 'gri/2020/305-2/b',
      },
      d: {
        code: 'gri/2020/305-3/a',
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              // We used to have conditions here if variables were not defined. scope function anyone?
              formula: '100*({a}+{b}+{c})/({a}+{b}+{c}+{d})'
            },
          ],
        }
      ]
    },
  }
};
