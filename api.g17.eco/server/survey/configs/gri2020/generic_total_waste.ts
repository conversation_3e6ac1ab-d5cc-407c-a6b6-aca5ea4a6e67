/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const generic_total_waste: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/generic/total-waste',
  compositeUtrCode: 'survey/generic/total-waste',
  fragmentUtrCodes: [
    'gri/2020/306-3/a',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/306-3/a': [vc.ProductServices, vc.Operations],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/306-3/a',
        valueListCode: 'hazardous_waste'
      },
      b: {
        code: 'gri/2020/306-3/a',
        valueListCode: 'non_hazardous'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}+{b}'
            },
          ],
        }
      ]
    },
  }
};
