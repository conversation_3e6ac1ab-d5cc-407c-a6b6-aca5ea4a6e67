/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_1_2_pc_employees_temp_permanent_contracts: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/1.2/pc-employees-temp-permanent-contracts',
  compositeUtrCode: 'survey/sdg/1.2/pc-employees-temp-permanent-contracts',
  fragmentUtrCodes: [
    'gri/2020/102-8/a',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/102-8/a': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/102-8/a',
        valueListCode: 'employees_contract_temporary'
      },
      b: {
        code: 'gri/2020/102-8/a',
        valueListCode: 'employees_contract_permanent'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '100*{a}/({a}+ifNA("{b}",0))'
            },
          ]
        },
      ]
    },
  }
};
