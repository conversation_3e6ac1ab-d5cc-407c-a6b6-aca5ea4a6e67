/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_8_6_pc_new_hires_18_30: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/8.6/pc-new-hires-18-30',
  compositeUtrCode: 'survey/sdg/8.6/pc-new-hires-18-30',
  fragmentUtrCodes: [
    'gri/2020/401-1/a',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/401-1/a': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/401-1/a',
        valueListCode: 'under_30'
      },
      b: {
        code: 'gri/2020/401-1/a',
        valueListCode: 'total_number_new_hires'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '100*{a}/{b}'
            },
          ]
        }
      ]
    },
  }
};
