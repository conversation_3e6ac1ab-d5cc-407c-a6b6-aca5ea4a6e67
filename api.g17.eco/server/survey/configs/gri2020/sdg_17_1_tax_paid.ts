/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_17_1_tax_paid: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/17.1/tax-paid',
  compositeUtrCode: 'survey/sdg/17.1/tax-paid',
  fragmentUtrCodes: [
    'gri/2020/207-4/b',
    'gri/2020/102-7/a'
  ],
  fragmentUtrConfiguration: {
    'gri/2020/207-4/b': [],
    'gri/2020/102-7/a': []
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/207-4/b',
        valueListCode: 'financial_info_cash_basis_income_tax'
      },
      b: {
        code: 'gri/2020/102-7/a',
        valueListCode: 'org_scale_sales'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '100*{a}/{b}'
            },
          ],
        }
      ]
    },
  }
};
