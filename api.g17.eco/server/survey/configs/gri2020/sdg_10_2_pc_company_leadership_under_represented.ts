/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_10_2_pc_company_leadership_under_represented: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/10.2/pc-company-leadership-under-represented',
  compositeUtrCode: 'survey/sdg/10.2/pc-company-leadership-under-represented',
  fragmentUtrCodes: [
    'survey/sdg/10.2/pc-company-leadership-catagorised-under-represented',
  ],
  fragmentUtrConfiguration: {
    'survey/sdg/10.2/pc-company-leadership-catagorised-under-represented': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'survey/sdg/10.2/pc-company-leadership-catagorised-under-represented',
        valueListCode: 'disabilities'
      },
      b: {
        code: 'survey/sdg/10.2/pc-company-leadership-catagorised-under-represented',
        valueListCode: 'refugees'
      },
      c: {
        code: 'survey/sdg/10.2/pc-company-leadership-catagorised-under-represented',
        valueListCode: 'indigenous'
      },
      d: {
        code: 'survey/sdg/10.2/pc-company-leadership-catagorised-under-represented',
        valueListCode: 'ethnic_minorities'
      },
      e: {
        code: 'survey/sdg/10.2/pc-company-leadership-catagorised-under-represented',
        valueListCode: 'women'
      },
      f: {
        code: 'survey/sdg/10.2/pc-company-leadership-catagorised-under-represented',
        valueListCode: 'other'
      },
      g: {
        code: 'survey/sdg/10.2/pc-company-leadership-catagorised-under-represented',
        valueListCode: 'total'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '100*{a}/{g}'
            },
            {
              type: CalculationType.Formula,
              formula: '100*{b}/{g}'
            },
            {
              type: CalculationType.Formula,
              formula: '100*{c}/{g}'
            },
            {
              type: CalculationType.Formula,
              formula: '100*{d}/{g}'
            },
            {
              type: CalculationType.Formula,
              formula: '100*{e}/{g}'
            },
            {
              type: CalculationType.Formula,
              formula: '100*{f}/{g}'
            },
          ],
        },
      ]
    },
  }
};
