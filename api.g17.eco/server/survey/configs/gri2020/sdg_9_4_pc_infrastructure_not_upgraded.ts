/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';


export const sdg_9_4_pc_infrastructure_not_upgraded: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/9.4/pc-infrastructure-not-upgraded',
  compositeUtrCode: 'survey/sdg/9.4/pc-infrastructure-not-upgraded',
  fragmentUtrCodes: [
    'gri/2020/203-1/a',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/203-1/a': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/203-1/a',
        valueListCode: 'pc_infrastructure_upgraded'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}'
            },
          ]
        }
      ]
    },
  }
};
