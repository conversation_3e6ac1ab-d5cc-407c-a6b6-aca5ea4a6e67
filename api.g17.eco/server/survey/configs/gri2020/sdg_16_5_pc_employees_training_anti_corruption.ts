/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_16_5_pc_employees_training_anti_corruption: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/16.5/pc-employees-training-anti-corruption',
  compositeUtrCode: 'survey/sdg/16.5/pc-employees-training-anti-corruption',
  fragmentUtrCodes: [
    'gri/2020/205-2/b',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/205-2/b': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/205-2/b',
        valueListCode: 'number'
      },
      b: {
        code: 'gri/2020/205-2/b',
        valueListCode: 'total_number_employees'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '100*{a}/{b}'
            },
          ],
        }
      ]
    },
  }
};
