/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_14_2_pc_investment_partnerships_promote_marine_protection: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/14.2/pc-investment-partnerships-promote-marine-protection',
  compositeUtrCode: 'survey/sdg/14.2/pc-investment-partnerships-promote-marine-protection',
  fragmentUtrCodes: [
    'gri/2020/304-3/a',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/304-3/a': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/304-3/a',
        valueListCode: 'pc_marine_areas_capex'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}'
            },
          ],
        },
      ]
    },
  }
};
