/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const sdg_12_5_pc_hazardous_waste_recycled: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/12.5/pc-hazardous-waste-recycled',
  compositeUtrCode: 'survey/sdg/12.5/pc-hazardous-waste-recycled',
  fragmentUtrCodes: [
    'gri/2020/306-4/b',
    'gri/2020/306-3/a'
  ],
  fragmentUtrConfiguration: {
    'gri/2020/306-4/b': [vc.ProductServices, vc.Operations],
    'gri/2020/306-3/a': [vc.ProductServices, vc.Operations],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/306-4/b',
        valueListCode: 'reuse',
        getValue: ({ utrv }) => utrv.valueData?.data?.reuse ?? 0
      },
      b: {
        code: 'gri/2020/306-4/b',
        valueListCode: 'recycling',
        getValue: ({ utrv }) => utrv.valueData?.data?.recycling ?? 0
      },
      c: {
        code: 'gri/2020/306-4/b',
        valueListCode: 'other_recovery',
        getValue: ({ utrv }) => utrv.valueData?.data?.other_recovery ?? 0
      },
      d: {
        code: 'gri/2020/306-3/a',
        valueListCode: 'hazardous_waste'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '100*(ifNA("{a}",0)+ifNA("{b}",0)+ifNA("{c}",0))/{d}'
            },
          ],
        }
      ]
    },
  }
};
