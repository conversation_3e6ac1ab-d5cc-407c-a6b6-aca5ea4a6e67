/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_15_5_size_land_protected: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/15.5/size-land-protected',
  compositeUtrCode: 'survey/sdg/15.5/size-land-protected',
  fragmentUtrCodes: [
    'gri/2020/304-3/a',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/304-3/a': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/304-3/a',
        valueListCode: 'habitat_areas_size'
      },
      b: {
        code: 'gri/2020/304-3/a',
        valueListCode: 'total_size_land'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '100*{a}/{b}'
            },
          ],
        }
      ]
    },
  }
};
