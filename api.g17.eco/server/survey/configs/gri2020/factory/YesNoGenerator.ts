/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import {
  CompositeUtrConfigInterface, FragmentUtrConfiguration,
  ImportConfigurationVariables,
} from '../../../compositeUtrConfigs';
import {
  CalculationStrategy,
  CalculationType,
  ValueRule,
} from '../../../../rules/rule';
import { ConditionTypes } from '../../../../rules/condition';

export interface YesNoConfig {
  compositeUtrCode: string;
  fragmentUtrCodes: string[];
  code?: string;
}

export const DEFAULT_CODE_PREFIX = 'generated/';
export const generateConfigCode = (utrCode: string) => DEFAULT_CODE_PREFIX + utrCode;

const generateVariables = (fragmentUtrCodes: string[]): ImportConfigurationVariables => {

  const letters = [...Array(26)].map((x,i)=>String.fromCharCode(i + 97));
  if (letters.length < fragmentUtrCodes.length) {
    throw new Error(`Too many fragmentUtrCodes (${fragmentUtrCodes.length}), max ${letters.length} is allowed`)
  }
  return fragmentUtrCodes.reduce((a, code, index) => {
    const letter = letters[index];
    a[letter] = { code }
    return a;
  }, {} as ImportConfigurationVariables);

}

const generateConfiguration = (variables: string[]): ValueRule[] => {
  return variables.map(variable => ({
    type: CalculationType.Text,
    formula: 'yes',
    conditions: [
      {
        type: ConditionTypes.Operator,
        condition: [variable],
        result: { gt: 0 },
      },
    ],
  }));
};

export const generateYesNoQuestion = (config: YesNoConfig): CompositeUtrConfigInterface => {

  const { compositeUtrCode, fragmentUtrCodes, code } = config;
  const variables = generateVariables(fragmentUtrCodes);
  const variablesKeys = Object.keys(variables);

  return {
    compositeUtrCode,
    fragmentUtrCodes,
    fragmentUtrConfiguration: fragmentUtrCodes.reduce((a, c) => {
      a[c] = [];
      return a;
    }, {} as FragmentUtrConfiguration),
    importConfigurationData: {
      calculation: {
        type: CalculationStrategy.FirstMatch,
        values: [
          ...generateConfiguration(variablesKeys),
          // Default
          { type: CalculationType.Text, formula: 'no' }
        ]
      },
      variables,
    },
    code: code ?? generateConfigCode(compositeUtrCode),
  }
}
