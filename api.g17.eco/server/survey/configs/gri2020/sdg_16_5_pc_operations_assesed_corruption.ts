/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_16_5_pc_operations_assesed_corruption: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/16.5/pc-operations-assesed-corruption',
  compositeUtrCode: 'survey/sdg/16.5/pc-operations-assesed-corruption',
  fragmentUtrCodes: [
    'gri/2020/205-1/a',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/205-1/a': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/205-1/a',
        valueListCode: 'corruption_percentage'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}'
            },
          ],
        }
      ]
    },
  }
};
