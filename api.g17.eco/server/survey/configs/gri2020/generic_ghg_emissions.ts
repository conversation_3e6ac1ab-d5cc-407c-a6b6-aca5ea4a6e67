/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const generic_ghg_emissions: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/generic/ghg-emissions',
  compositeUtrCode: 'survey/generic/ghg-emissions',
  fragmentUtrCodes: [
    'gri/2020/305-1/a',
    'gri/2020/305-2/a',
    'gri/2020/305-2/b',
    'gri/2020/305-3/a',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/305-1/a': vc.allValueChain,
    'gri/2020/305-2/a': vc.allValueChain,
    'gri/2020/305-2/b': vc.allValueChain,
    'gri/2020/305-3/a': vc.allValue<PERSON>hai<PERSON>,
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/305-1/a',
      },
      b: {
        code: 'gri/2020/305-2/a',
      },
      c: {
        code: 'gri/2020/305-2/b',
      },
      d: {
        code: 'gri/2020/305-3/a',
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: 'resolveNaNr("{a}")'
            },
            {
              type: CalculationType.Formula,
              formula: 'resolveNaNr("{b}")'
            },
            {
              type: CalculationType.Formula,
              formula: 'resolveNaNr("{c}")'
            },
            {
              type: CalculationType.Formula,
              formula: 'resolveNaNr("{d}")'
            },
          ],
        },
      ]
    },
  }
};
