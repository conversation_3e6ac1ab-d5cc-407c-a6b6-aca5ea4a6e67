/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const generic_scope_2: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/generic/scope-2',
  compositeUtrCode: 'survey/generic/scope-2',
  fragmentUtrCodes: [
    'gri/2020/305-2/a',
    'gri/2020/305-2/b',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/305-2/a': [],
    'gri/2020/305-2/b': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/305-2/a',
      },
      b: {
        code: 'gri/2020/305-2/b',
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: 'ifNA("{a}",0)+ifNA("{b}",0)'
            },
          ],
        }
      ]
    },
  }
};
