/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const generic_pc_renewable_fuel_consumption: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/generic/pc-renewable-fuel-consumption',
  compositeUtrCode: 'survey/generic/pc-renewable-fuel-consumption',
  fragmentUtrCodes: [
    'gri/2020/302-1/a',
    'gri/2020/302-1/b',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/302-1/a': vc.allValueChain,
    'gri/2020/302-1/b': vc.allValueChain,
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/302-1/a',
        valueListCode: 'total_energy_nonrenewable'
      },
      b: {
        code: 'gri/2020/302-1/b',
        valueListCode: 'total_energy_renewable'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '100*{b}/(ifNA("{a}",0)+{b})'
            },
          ]
        }
      ]
    },
  }
};
