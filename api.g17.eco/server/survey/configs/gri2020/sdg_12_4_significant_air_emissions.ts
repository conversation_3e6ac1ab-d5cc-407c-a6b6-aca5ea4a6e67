/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_12_4_significant_air_emissions: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/12.4/significant-air-emissions',
  compositeUtrCode: 'survey/sdg/12.4/significant-air-emissions',
  fragmentUtrCodes: [
    'gri/2020/305-7/a'
  ],
  fragmentUtrConfiguration: {
    'gri/2020/305-7/a': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/305-7/a',
        valueListCode: 'nox'
      },
      b: {
        code: 'gri/2020/305-7/a',
        valueListCode: 'sox'
      },
      c: {
        code: 'gri/2020/305-7/a',
        valueListCode: 'pop'
      },
      d: {
        code: 'gri/2020/305-7/a',
        valueListCode: 'voc'
      },
      e: {
        code: 'gri/2020/305-7/a',
        valueListCode: 'hap'
      },
      f: {
        code: 'gri/2020/305-7/a',
        valueListCode: 'pm'
      },
      g: {
        code: 'gri/2020/305-7/a',
        valueListCode: 'other_standard_air_categories'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}'
            },
            {
              type: CalculationType.Formula,
              formula: '{b}'
            },
            {
              type: CalculationType.Formula,
              formula: '{c}'
            },
            {
              type: CalculationType.Formula,
              formula: '{d}'
            },
            {
              type: CalculationType.Formula,
              formula: '{e}'
            },
            {
              type: CalculationType.Formula,
              formula: '{f}'
            },
            {
              type: CalculationType.Formula,
              formula: '{g}'
            },
          ],
        }
      ]
    },
  }
};
