/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_16_6_conflict_interest_disclosed: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/16.6/conflict-interest-disclosed',
  compositeUtrCode: 'survey/sdg/16.6/conflict-interest-disclosed',
  fragmentUtrCodes: [
    'gri/2020/102-25/b',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/102-25/b': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/102-25/b',
        valueListCode: 'cross_board'
      },
      b: {
        code: 'gri/2020/102-25/b',
        valueListCode: 'cross_shareholding'
      },
      c: {
        code: 'gri/2020/102-25/b',
        valueListCode: 'existence_of_controlling_shareholder'
      },
      d: {
        code: 'gri/2020/102-25/b',
        valueListCode: 'related_party_disclosure'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: 'resolveString("{a}","yes",25)+resolveString("{b}","yes",25)+resolveString("{c}","yes",25)+resolveString("{d}","yes",25)'
            },
          ],
        }
      ]
    },
  }
};
