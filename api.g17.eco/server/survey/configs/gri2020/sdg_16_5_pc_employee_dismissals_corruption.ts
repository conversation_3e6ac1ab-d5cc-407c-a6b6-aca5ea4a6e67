/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_16_5_pc_employee_dismissals_corruption: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/16.5/pc-employee-dismissals-corruption',
  compositeUtrCode: 'survey/sdg/16.5/pc-employee-dismissals-corruption',
  fragmentUtrCodes: [
    'gri/2020/205-3/b',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/205-3/b': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/205-3/b',
        valueListCode: 'dismissals_corruption'
      },
      b: {
        code: 'gri/2020/205-3/b',
        valueListCode: 'dismissals'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '100*{a}/{b}'
            },
          ],
        }
      ]
    },
  }
};
