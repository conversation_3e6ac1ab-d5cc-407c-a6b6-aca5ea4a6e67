/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import { ConditionTypes } from '../../../rules/condition';
import * as vc from '../../../util/valueChain';

export const generic_pc_toxic_spills: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/generic/pc-toxic-spills',
  compositeUtrCode: 'survey/generic/pc-toxic-spills',
  fragmentUtrCodes: [
    'gri/2020/306-3/a',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/306-3/a': [vc.ProductServices, vc.Operations],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/306-3/a',
        valueListCode: 'toxic_spills'
      },
      b: {
        code: 'gri/2020/306-3/a',
        valueListCode: 'significant_spills_total_number'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '0'
            },
          ],
          conditions: [
            {
              type: ConditionTypes.Formula,
              condition: '{b}',
              result: {
                lte: 0
              },
            },
          ]
        },
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '100*{a}/{b}'
            },
          ]
        }
      ]
    },
  }
};
