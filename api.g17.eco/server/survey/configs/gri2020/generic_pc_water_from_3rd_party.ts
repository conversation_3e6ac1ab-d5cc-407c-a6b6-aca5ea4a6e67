/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const generic_pc_water_from_3rd_party: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/generic/pc-water-from-3rd-party',
  compositeUtrCode: 'survey/generic/pc-water-from-3rd-party',
  fragmentUtrCodes: [
    'gri/2020/303-3/a',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/303-3/a': vc.allValueChain,
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/303-3/a',
        valueListCode: 'third_party_water',
      },
      b: {
        code: 'gri/2020/303-3/a',
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '100*{a}/{b}'
            },
          ]
        }
      ]
    },
  }
};
