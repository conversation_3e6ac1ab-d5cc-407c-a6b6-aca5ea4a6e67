/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_12_2_pc_recycled_materials_in_manufacturing: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/12.2/pc-recycled-materials-in-manufacturing',
  compositeUtrCode: 'survey/sdg/12.2/pc-recycled-materials-in-manufacturing',
  fragmentUtrCodes: [
    'gri/2020/301-2',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/301-2': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/301-2',
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}'
            },
          ],
        },
      ]
    },
  }
};
