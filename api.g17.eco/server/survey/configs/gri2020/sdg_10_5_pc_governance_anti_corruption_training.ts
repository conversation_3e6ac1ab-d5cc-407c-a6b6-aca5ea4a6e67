/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const sdg_10_5_pc_governance_anti_corruption_training: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/10.5/pc-governance-anti-corruption-training',
  compositeUtrCode: 'survey/sdg/10.5/pc-governance-anti-corruption-training',
  fragmentUtrCodes: [
    'gri/2020/205-2/a',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/205-2/a': vc.allValueChain,
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/205-2/a',
        valueListCode: 'corruption_governance_total_number'
      },
      b: {
        code: 'gri/2020/205-2/a',
        valueListCode: 'number_board_members'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '100 * {a}/{b}'
            },
          ],
        },
      ]
    },
  }
};
