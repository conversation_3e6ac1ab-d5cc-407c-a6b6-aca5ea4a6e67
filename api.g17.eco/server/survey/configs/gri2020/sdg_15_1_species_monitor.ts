/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_15_1_species_monitor: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/15.1/species-monitor',
  compositeUtrCode: 'survey/sdg/15.1/species-monitor',
  fragmentUtrCodes: [
    'gri/2020/304-4',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/304-4': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/304-4',
        valueListCode: 'critically_endangered'
      },
      b: {
        code: 'gri/2020/304-4',
        valueListCode: 'endangered'
      },
      c: {
        code: 'gri/2020/304-4',
        valueListCode: 'vulnerable'
      },
      d: {
        code: 'gri/2020/304-4',
        valueListCode: 'near_threatened'
      },
      e: {
        code: 'gri/2020/304-4',
        valueListCode: 'least_concern'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '5*linearDistribution({a},0,1,0,1)',
            },
            {
              type: CalculationType.Formula,
              formula: '10*linearDistribution({b},0,1,0,1)'
            },
            {
              type: CalculationType.Formula,
              formula: '15*linearDistribution({c},0,1,0,1)'
            },
            {
              type: CalculationType.Formula,
              formula: '25*linearDistribution({d},0,1,0,1)'
            },
            {
              type: CalculationType.Formula,
              formula: '45*linearDistribution({e},0,1,0,1)'
            },
          ],
        },
      ]
    },
  }
};
