/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_9_1_pc_buildings_impact_assessment: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/9.1/pc-buildings-impact-assessment',
  compositeUtrCode: 'survey/sdg/9.1/pc-buildings-impact-assessment',
  fragmentUtrCodes: [
    'gri/2020/413-1',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/413-1': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/413-1',
        valueListCode: 'community_engagement'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}'
            },
          ]
        }
      ]
    },
  }
};
