/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const sdbti_scope_1_2: CompositeUtrConfigInterface = {
  code: 'gri/2020/sbti/scope1-2',
  compositeUtrCode: 'sbti/2020/scope1-2',
  fragmentUtrCodes: [
    'gri/2020/305-1/a',
    'gri/2020/305-2/a',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/305-1/a': vc.allValueChain,
    'gri/2020/305-2/a': vc.allValueChain,
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/305-1/a',
      },
      b: {
        code: 'gri/2020/305-2/a',
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: 'ifNA("{a}",0)+ifNA("{b}",0)'
            },
          ],
        }
      ]
    },
  }
};
