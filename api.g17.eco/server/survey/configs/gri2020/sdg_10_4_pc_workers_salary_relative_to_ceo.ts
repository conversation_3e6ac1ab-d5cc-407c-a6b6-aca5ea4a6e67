/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_10_4_pc_workers_salary_relative_to_ceo: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/10.4/pc-workers-salary-relative-to-ceo',
  compositeUtrCode: 'survey/sdg/10.4/pc-workers-salary-relative-to-ceo',
  fragmentUtrCodes: [
    'gri/2020/102-38',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/102-38': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/102-38',
        valueListCode: 'numerator_value'
      },
      b: {
        code: 'gri/2020/102-38',
        valueListCode: 'denominator_value'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '100 * {a}/{b}'
            },
          ],
        },
      ]
    },
  }
};
