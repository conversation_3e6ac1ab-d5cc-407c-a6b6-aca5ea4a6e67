/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_5_2_pc_suppliers_screening_social: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/5.2/pc-suppliers-screening-social',
  compositeUtrCode: 'survey/sdg/5.2/pc-suppliers-screening-social',
  fragmentUtrCodes: [
    'gri/2020/414-1',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/414-1': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/414-1',
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}'
            },
          ]
        },
      ]
    },
  }
};
