/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_3_2_pc_health_care_cover_families: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/3.2/pc-health-care-cover-families',
  compositeUtrCode: 'survey/sdg/3.2/pc-health-care-cover-families',
  fragmentUtrCodes: [
    'gri/2020/403-1/b',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/403-1/b': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/403-1/b',
        valueListCode: 'pc_family_covered'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}'
            },
          ]
        },
      ]
    },
  }
};
