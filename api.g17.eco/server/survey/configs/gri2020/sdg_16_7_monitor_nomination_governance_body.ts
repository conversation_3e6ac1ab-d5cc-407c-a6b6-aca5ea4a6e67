/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_16_7_monitor_nomination_governance_body: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/16.7/monitor-nomination-governance-body',
  compositeUtrCode: 'survey/sdg/16.7/monitor-nomination-governance-body',
  fragmentUtrCodes: [
    'gri/2020/102-24/b',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/102-24/b': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/102-24/b',
        valueListCode: 'stakeholders_involved'
      },
      b: {
        code: 'gri/2020/102-24/b',
        valueListCode: 'diversity_considered'
      },
      c: {
        code: 'gri/2020/102-24/b',
        valueListCode: 'independence_considered'
      },
      d: {
        code: 'gri/2020/102-24/b',
        valueListCode: 'expertise_environmental_considered'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: 'resolveString("{a}","yes",25)+resolveString("{b}","yes",25)+resolveString("{c}","yes",25)+resolveString("{d}","yes",25)'
            },
          ],
        }
      ]
    },
  }
};
