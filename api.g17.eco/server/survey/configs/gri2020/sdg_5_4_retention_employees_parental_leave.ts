/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_5_4_retention_employees_parental_leave: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/5.4/retention-employees-parental-leave',
  compositeUtrCode: 'survey/sdg/5.4/retention-employees-parental-leave',
  fragmentUtrCodes: [
    'gri/2020/401-3/b',
    'gri/2020/401-3/c',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/401-3/b': [],
    'gri/2020/401-3/c': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/401-3/b',
      },
      b: {
        code: 'gri/2020/401-3/c',
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: 'linearDistribution(100*{b}/{a},0,100,0,100)'
            },
          ]
        },
      ]
    },
  }
};
