/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_14_2_pc_labelling_information_on_sourcing_marine: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/14.2/pc-labelling-information-on-sourcing-marine',
  compositeUtrCode: 'survey/sdg/14.2/pc-labelling-information-on-sourcing-marine',
  fragmentUtrCodes: [
    'gri/2020/417-1/a',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/417-1/a': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/417-1/a',
        valueListCode: 'marine_sourcing'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}'
            },
          ],
        },
      ]
    },
  }
};
