/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_14_1_pc_packaging_recyclable: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/14.1/pc-packaging-recyclable',
  compositeUtrCode: 'survey/sdg/14.1/pc-packaging-recyclable',
  fragmentUtrCodes: [
    'gri/2020/301-1',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/301-1': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/301-1',
        valueListCode: 'non_renewable_materials'
      },
      b: {
        code: 'gri/2020/301-1',
        valueListCode: 'renewable_materials'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '100 * {b}/(ifNA("{a}",0)+{b})'
            },
          ],
        },
      ]
    },
  }
};
