/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_16_2_pc_company_risk_child_labour_incidents: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/16.2/pc-company-risk-child-labour-incidents',
  compositeUtrCode: 'survey/sdg/16.2/pc-company-risk-child-labour-incidents',
  fragmentUtrCodes: [
    'gri/2020/408-1/a',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/408-1/a': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/408-1/a',
        valueListCode: 'pc_risk_child_labor'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}'
            },
          ],
        }
      ]
    },
  }
};
