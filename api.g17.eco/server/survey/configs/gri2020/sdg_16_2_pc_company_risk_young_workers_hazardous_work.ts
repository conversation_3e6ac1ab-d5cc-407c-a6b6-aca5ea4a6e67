/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_16_2_pc_company_risk_young_workers_hazardous_work: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/16.2/pc-company-risk-young-workers-hazardous-work',
  compositeUtrCode: 'survey/sdg/16.2/pc-company-risk-young-workers-hazardous-work',
  fragmentUtrCodes: [
    'gri/2020/408-1/a',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/408-1/a': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/408-1/a',
        valueListCode: 'pc_risk_hazardous_work'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}'
            },
          ],
        }
      ]
    },
  }
};
