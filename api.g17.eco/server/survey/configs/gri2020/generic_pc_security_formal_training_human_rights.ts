/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const generic_pc_security_formal_training_human_rights: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/generic/pc-security-formal-training-human-rights',
  compositeUtrCode: 'survey/generic/pc-security-formal-training-human-rights',
  fragmentUtrCodes: [
    'gri/2020/410-1/a'
  ],
  fragmentUtrConfiguration: {
    'gri/2020/410-1/a': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/410-1/a',
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}'
            },
          ]
        }
      ]
    },
  }
};
