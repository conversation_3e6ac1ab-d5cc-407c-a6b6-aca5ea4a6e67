/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const sdg_12_5_pc_operational_waste_sustainably_eliminated: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/12.5/pc-operational-waste-sustainably-eliminated',
  compositeUtrCode: 'survey/sdg/12.5/pc-operational-waste-sustainably-eliminated',
  fragmentUtrCodes: [
    'gri/2020/306-4/c',
    'gri/2020/306-3/a'
  ],
  fragmentUtrConfiguration: {
    'gri/2020/306-4/c': vc.allValueChain,
    'gri/2020/306-3/a': [vc.ProductServices, vc.Operations],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/306-4/c',
        valueListCode: 'reuse',
        getValue: ({ utrv }) => utrv.valueData?.data?.reuse ?? 0
      },
      b: {
        code: 'gri/2020/306-4/c',
        valueListCode: 'recycling',
        getValue: ({ utrv }) => utrv.valueData?.data?.recycling ?? 0
      },
      c: {
        code: 'gri/2020/306-4/c',
        valueListCode: 'other_recovery',
        getValue: ({ utrv }) => utrv.valueData?.data?.other_recovery ?? 0
      },
      d: {
        code: 'gri/2020/306-3/a',
        valueListCode: 'non_hazardous'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '100*(ifNA("{a}",0)+ifNA("{b}",0)+ifNA("{c}",0))/{d}'
            },
          ],
        }
      ]
    },
  }
};
