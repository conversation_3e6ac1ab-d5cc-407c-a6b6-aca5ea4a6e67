/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const sdg_13_1_investment_for_mitigating_climate_change_risks: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/13.1/investment-for-mitigating-climate-change-risks',
  compositeUtrCode: 'survey/sdg/13.1/investment-for-mitigating-climate-change-risks',
  fragmentUtrCodes: [
    'gri/2020/201-2/a',
    'survey/generic/capex-capital-expenditure',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/201-2/a': vc.allValueChain,
    'survey/generic/capex-capital-expenditure': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/201-2/a',
        valueListCode: 'risk_opp_management_cost'
      },
      b: {
        code: 'survey/generic/capex-capital-expenditure',
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '100*{a}/{b}'
            },
          ]
        }
      ]
    },
  }
};
