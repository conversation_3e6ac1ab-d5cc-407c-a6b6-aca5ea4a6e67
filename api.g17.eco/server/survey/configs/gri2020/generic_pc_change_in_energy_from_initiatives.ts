/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const generic_pc_change_in_energy_from_initiatives: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/generic/pc-change-in-energy-from-initiatives',
  compositeUtrCode: 'survey/generic/pc-change-in-energy-from-initiatives',
  fragmentUtrCodes: [
    'gri/2020/302-4/a',
    'gri/2020/302-1/e/baseline',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/302-4/a': vc.allValueChain,
    'gri/2020/302-1/e/baseline': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/302-4/a',
      },
      b: {
        code: 'gri/2020/302-1/e/baseline',
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '100*{a}/{b}'
            },
          ],
        }
      ]
    },
  }
};
