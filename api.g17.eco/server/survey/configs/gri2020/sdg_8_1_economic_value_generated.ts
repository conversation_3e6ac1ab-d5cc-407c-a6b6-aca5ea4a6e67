/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const sdg_8_1_economic_value_generated: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/8.1/economic-value-generated',
  compositeUtrCode: 'survey/sdg/8.1/economic-value-generated',
  fragmentUtrCodes: [
    'gri/2020/201-1/a',
    'survey/gri/2020/201-1/a/baseline'
  ],
  fragmentUtrConfiguration: {
    'gri/2020/201-1/a': [vc.ProductServices, vc.Operations],
    'survey/gri/2020/201-1/a/baseline': [vc.ProductServices, vc.Operations],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/201-1/a',
        valueListCode: 'econ_retained'
      },
      b: {
        code: 'survey/gri/2020/201-1/a/baseline',
        valueListCode: 'econ_retained'
      }
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '(100*({a}-{b})/{b})'
            },
          ]
        }
      ]
    },
  }
};
