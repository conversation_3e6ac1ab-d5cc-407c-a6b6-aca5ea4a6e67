/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_16_7_stakeholder_consulation_governance: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/16.7/stakeholder-consulation-governance',
  compositeUtrCode: 'survey/sdg/16.7/stakeholder-consulation-governance',
  fragmentUtrCodes: [
    'gri/2020/102-29/b',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/102-29/b': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/102-29/b',
        valueListCode: 'economic'
      },
      b: {
        code: 'gri/2020/102-29/b',
        valueListCode: 'environmental'
      },
      c: {
        code: 'gri/2020/102-29/b',
        valueListCode: 'social'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: 'resolveString("{a}","yes",100/3)+resolveString("{b}","yes",100/3)+resolveString("{c}","yes",100/3)'
            },
          ],
        }
      ]
    },
  }
};
