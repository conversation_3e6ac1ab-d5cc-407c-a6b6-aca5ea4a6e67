/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_10_3_pc_employees_temp_permanent_contracts: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/10.3/pc-employees-temp-permanent-contracts',
  compositeUtrCode: 'survey/sdg/10.3/pc-employees-temp-permanent-contracts',
  fragmentUtrCodes: [
    'gri/2020/102-8/a',
    'gri/2020/102-7/a',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/102-8/a': [],
    'gri/2020/102-7/a': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/102-8/a',
        valueListCode: 'employees_contract_permanent'
      },
      b: {
        code: 'gri/2020/102-8/a',
        valueListCode: 'employees_contract_temporary'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '100 * {b} / (resolveNaNr("{a}",0,"not_reported")+{b})'
            },
          ],
        },
      ]
    },
  }
};
