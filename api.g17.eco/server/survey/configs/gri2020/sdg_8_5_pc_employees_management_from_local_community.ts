/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

// This is multipart question that gives a score
export const sdg_8_5_pc_employees_management_from_local_community: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/8.5/pc-employees-management-from-local-community',
  compositeUtrCode: 'survey/sdg/8.5/pc-employees-management-from-local-community',
  fragmentUtrCodes: [
    'gri/2020/202-2',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/202-2': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/202-2'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}'
            },
          ],
        }
      ]
    },
  }
};
