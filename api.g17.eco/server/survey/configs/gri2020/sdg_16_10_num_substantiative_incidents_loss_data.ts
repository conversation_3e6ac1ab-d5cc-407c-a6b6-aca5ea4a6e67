/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_16_10_num_substantiative_incidents_loss_data: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/16.10/num-substantiative-incidents-loss-data',
  compositeUtrCode: 'survey/sdg/16.10/num-substantiative-incidents-loss-data',
  fragmentUtrCodes: [
    'gri/2020/418-1/a',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/418-1/a': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/418-1/a',
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}'
            },
          ],
        }
      ]
    },
  }
};
