/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_8_2_company_transition_assistance_programmes: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/8.2/company-transition-assistance-programmes',
  compositeUtrCode: 'survey/sdg/8.2/company-transition-assistance-programmes',
  fragmentUtrCodes: [
    'gri/2020/404-2/b'
  ],
  fragmentUtrConfiguration: {
    'gri/2020/404-2/b': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/404-2/b',
        valueListCode: 'transition_assistance_pc'
      }
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}'
            },
          ]
        }
      ]
    },
  }
};
