/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const generic_energy_consumption: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/generic/energy-consumption',
  compositeUtrCode: 'survey/generic/energy-consumption',
  fragmentUtrCodes: [
    'gri/2020/302-1/e',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/302-1/e': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/302-1/e'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}'
            },
          ],
        }
      ]
    },
  }
};
