/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_16_10_num_incidents_customer_data: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/16.10/num-incidents-customer-data',
  compositeUtrCode: 'survey/sdg/16.10/num-incidents-customer-data',
  fragmentUtrCodes: [
    'gri/2020/418-1/b',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/418-1/b': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/418-1/b',
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}'
            },
          ],
        }
      ]
    },
  }
};
