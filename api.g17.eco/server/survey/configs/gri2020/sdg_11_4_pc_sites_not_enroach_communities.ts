/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_11_4_pc_sites_not_enroach_communities: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/11.4/pc-sites-not-enroach-communities',
  compositeUtrCode: 'survey/sdg/11.4/pc-sites-not-enroach-communities',
  fragmentUtrCodes: [
    'gri/2020/413-1',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/413-1': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/413-1',
        valueListCode: 'community_engagement'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}'
            },
          ],
        },
      ]
    },
  }
};
