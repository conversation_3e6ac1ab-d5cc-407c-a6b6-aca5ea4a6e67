/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const sdg_4_5_ratio_training_gender: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/4.5/ratio-training-gender',
  compositeUtrCode: 'survey/sdg/4.5/ratio-training-gender',
  fragmentUtrCodes: [
    'gri/2020/404-1/a',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/404-1/a': [vc.Operations, vc.ExternalCsr],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/404-1/a',
        valueListCode: 'female'
      },
      b: {
        code: 'gri/2020/404-1/a',
        valueListCode: 'male'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}/{b}',
            },
          ],
        },
      ],
    },
  }
};
