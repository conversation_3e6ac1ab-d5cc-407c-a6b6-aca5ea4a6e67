/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_3_3_pc_workers_high_risk_diseases: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/3.3/pc-workers-high-risk-diseases',
  compositeUtrCode: 'survey/sdg/3.3/pc-workers-high-risk-diseases',
  fragmentUtrCodes: [
    'gri/2020/403-3',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/403-3': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/403-3',
        valueListCode: 'pc_workers_diseases'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}'
            },
          ]
        },
      ]
    },
  }
};
