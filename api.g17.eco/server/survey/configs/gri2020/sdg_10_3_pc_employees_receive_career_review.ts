/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const sdg_10_3_pc_employees_receive_career_review: CompositeUtrConfigInterface = {
  code: 'gri/2020/survey/sdg/10.3/pc-employees-receive-career-review',
  compositeUtrCode: 'survey/sdg/10.3/pc-employees-receive-career-review',
  fragmentUtrCodes: [
    'gri/2020/404-3',
    'gri/2020/102-7/a',
  ],
  fragmentUtrConfiguration: {
    'gri/2020/404-3': [],
    'gri/2020/102-7/a': [],
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2020/404-3',
        valueListCode: 'male'
      },
      b: {
        code: 'gri/2020/404-3',
        valueListCode: 'female'
      },
      c: {
        code: 'gri/2020/102-7/a',
        valueListCode: 'org_scale_employees_male'
      },
      d: {
        code: 'gri/2020/102-7/a',
        valueListCode: 'org_scale_employees_female'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Sum,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '100 * ( ({c}*{a}/100) + ({d}*{b}/100) ) / ({c}+{d})'
            },
          ],
        },
      ]
    },
  }
};
