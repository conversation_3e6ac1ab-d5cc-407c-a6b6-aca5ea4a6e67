/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const sdg_7_3_energy_consumption: CompositeUtrConfigInterface = {
  code: 'gri/2019/survey/sdg/7.3/energy-consumption',
  ownerSourceName: 'baseline2019',
  compositeUtrCode: 'survey/generic/energy-consumption',
  fragmentUtrCodes: [
    'gri/2019/302-1/e',
  ],
  fragmentUtrConfiguration: {
    'gri/2019/302-1/e': vc.allValueChain,
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2019/302-1/e'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Average,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}'
            },
          ],
        }
      ]
    },
  }
};
