/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const sdg_13_1_r_and_d_technology_spend: CompositeUtrConfigInterface = {
  code: 'gri/2019/survey/sdg/13.1/pc-r-d-technology-spend',
  compositeUtrCode: 'survey/sdg/13.1/pc-r-d-technology-spend',
  ownerSourceName: 'baseline2019',
  fragmentUtrCodes: [
    'gri/2019/302-4/a',
    'gri/2019/survey/generic/capex-lag-one-year',
  ],
  fragmentUtrConfiguration: {
    'gri/2019/303-3/a': vc.allValueChain,
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2019/302-4/a',
      },
      b: {
        code: 'gri/2019/survey/generic/capex-lag-one-year',
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Average,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '100*{a}/({b}*1000000)'
            },
          ]
        }
      ]
    },
  }
};
