/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const sdg_6_4_pc_water_withdrawl_from_scarce: CompositeUtrConfigInterface = {
  code: 'gri/2019/survey/sdg/6.4/pc-water-withdrawl-from-scarce',
  compositeUtrCode: 'survey/sdg/6.4/pc-water-withdrawl-from-scarce',
  ownerSourceName: 'baseline2019',
  fragmentUtrCodes: [
    'gri/2019/303-3/a',
    'gri/2019/303-3/b',
  ],
  fragmentUtrConfiguration: {
    'gri/2019/303-3/a': vc.allValueChain,
    'gri/2019/303-3/b': vc.allValueChain,
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2019/303-3/a',
      },
      b: {
        code: 'gri/2019/303-3/b',
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Average,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '100*{b}/{a}'
            },
          ]
        }
      ]
    },
  }
};
