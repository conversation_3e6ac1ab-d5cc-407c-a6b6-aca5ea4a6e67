/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const generic_pc_women_salary_to_men: CompositeUtrConfigInterface = {
  code: 'gri/2019/survey/sdg/generic/pc-women-salary-to-men',
  ownerSourceName: 'baseline2019',
  compositeUtrCode: 'survey/sdg/generic/pc-women-salary-to-men',
  fragmentUtrCodes: [
    'gri/2019/405-2/a',
  ],
  fragmentUtrConfiguration: {
    'gri/2019/405-2/a': vc.allValueChain,
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2019/405-2/a',
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Average,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}'
            },
          ],
        }
      ]
    },
  }
};
