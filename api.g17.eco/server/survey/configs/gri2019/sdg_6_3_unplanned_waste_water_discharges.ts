/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import { ConditionTypes } from '../../../rules/condition';
import * as vc from '../../../util/valueChain';

export const sdg_6_3_unplanned_waste_water_discharges: CompositeUtrConfigInterface = {
  code: 'gri/2019/survey/sdg/6.3/unplanned-waste-water-discharges',
  ownerSourceName: 'baseline2019',
  compositeUtrCode: 'survey/sdg/6.3/unplanned-waste-water-discharges',
  fragmentUtrCodes: [
    'gri/2019/303-3/a',
    'gri/2019/303-4/a',
    'gri/2019/303-5/a',
  ],
  fragmentUtrConfiguration: {
    'gri/2019/303-3/a': vc.allValueChain,
    'gri/2019/303-4/a': vc.allValueChain,
    'gri/2019/303-5/a': vc.allValueChain,
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2019/303-3/a',
      },
      b: {
        code: 'gri/2019/303-4/a',
      },
      c: {
        code: 'gri/2019/303-5/a',
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Average,
          conditions: [
            {
              type: ConditionTypes.Formula,
              condition: '{b}',
              result: {
                isNotApplicable: true
              },
            },
          ],
          variables: [
            {
              type: CalculationType.Formula,
              formula: '({a}-{c})*1000'
            },
          ],
        },
        {
          type: CalculationType.Average,
          conditions: [
            {
              type: ConditionTypes.Formula,
              condition: '{c}',
              result: {
                isNotApplicable: true
              },
            },
          ],
          variables: [
            {
              type: CalculationType.Formula,
              formula: '({a}-{b})*1000'
            },
          ],
        },
        {
          type: CalculationType.Average,
          conditions: [
            {
              type: ConditionTypes.Formula,
              condition: '{a}',
              result: {
                isNotApplicable: true
              },
            },
          ],
          variables: [
            {
              type: CalculationType.Formula,
              formula: '({a}-{b}-{c})*1000'
            },
          ],
        },
        {
          type: CalculationType.Average,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '({a}-{b}-{c})*1000'
            },
          ],
        }
      ]
    },
  }
};
