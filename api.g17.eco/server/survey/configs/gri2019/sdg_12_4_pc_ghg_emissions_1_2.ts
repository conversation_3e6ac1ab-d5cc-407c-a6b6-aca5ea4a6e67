/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import { ConditionTypes } from '../../../rules/condition';
import * as vc from '../../../util/valueChain';

export const sdg_12_4_pc_ghg_emissions_1_2: CompositeUtrConfigInterface = {
  code: 'gri/2019/survey/sdg/12.4/pc-ghg-emissions-1-2',
  ownerSourceName: 'baseline2019',
  compositeUtrCode: 'survey/sdg/12.4/pc-ghg-emissions-1-2',
  fragmentUtrCodes: [
    'gri/2019/305-1/a',
    'gri/2019/305-2/a',
    'gri/2019/305-3/a',
  ],
  fragmentUtrConfiguration: {
    'gri/2019/305-1/a': vc.allValue<PERSON>hain,
    'gri/2019/305-2/a': vc.all<PERSON><PERSON>ue<PERSON><PERSON><PERSON>,
    'gri/2019/305-3/a': vc.allValueChain,
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2019/305-1/a',
      },
      b: {
        code: 'gri/2019/305-2/a',
      },
      c: {
        code: 'gri/2019/305-3/a',
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Average,
          conditions: [
            {
              type: ConditionTypes.Formula,
              condition: '{c}',
              result: {
                isNotApplicable: true
              },
            },
          ],
          variables: [
            {
              type: CalculationType.Formula,
              formula: '100' // basically a+b/a+b=1
            },
          ],
        },
        {
          type: CalculationType.Average,
          conditions: [
            {
              type: ConditionTypes.Formula,
              condition: '{a}',
              result: {
                isNotApplicable: true
              },
            },
          ],
          variables: [
            {
              type: CalculationType.Formula,
              formula: '100*({b})/({b}+{c})'
            },
          ],
        },
        {
          type: CalculationType.Average,
          conditions: [
            {
              type: ConditionTypes.Formula,
              condition: '{b}',
              result: {
                isNotApplicable: true
              },
            },
          ],
          variables: [
            {
              type: CalculationType.Formula,
              formula: '100*({a})/({a}+{c})'
            },
          ],
        },
        {
          type: CalculationType.Average,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '100*({a}+{b})/({a}+{b}+{c})'
            },
          ],
        }
      ]
    },
  }
};
