/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

 // 14.1/Q1

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const sdg_14_1_pc_water_discharge_recycled: CompositeUtrConfigInterface = {
  code: 'gri/2019/survey/sdg/14.1/pc-water-discharge-recycled',
  compositeUtrCode: 'survey/sdg/14.1/pc-water-discharge-recycled',
  ownerSourceName: 'baseline2019',
  fragmentUtrCodes: [
    'gri/2019/303-4/a',
  ],
  fragmentUtrConfiguration: {
    'gri/2019/303-4/a': vc.allValueChain,
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2019/303-4/a',
        valueListCode: 'third_party_water',
      },
      b: {
        code: 'gri/2019/303-4/a',
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Average,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '(100*{a}/{b})'
            },
          ],
        },
      ]
    },
  }
};
