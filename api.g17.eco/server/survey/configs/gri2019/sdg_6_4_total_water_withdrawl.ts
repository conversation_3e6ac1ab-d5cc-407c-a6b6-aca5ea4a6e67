/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const sdg_6_4_total_water_withdrawl: CompositeUtrConfigInterface = {
  code: 'gri/2019/survey/sdg/6.4/total-water-withdrawl',
  ownerSourceName: 'baseline2019',
  compositeUtrCode: 'survey/sdg/6.4/total-water-withdrawl',
  fragmentUtrCodes: [
    'gri/2019/303-3/a',
  ],
  fragmentUtrConfiguration: {
    'gri/2019/303-3/a': vc.allValueChain,
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2019/303-3/a',
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Average,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '1000*{a}'
            },
          ],
        }
      ]
    },
  }
};
