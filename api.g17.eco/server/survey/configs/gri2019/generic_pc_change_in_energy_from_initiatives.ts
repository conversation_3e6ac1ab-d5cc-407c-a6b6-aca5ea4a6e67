/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const generic_pc_change_in_energy_from_initiatives: CompositeUtrConfigInterface = {
  code: 'gri/2019/survey/generic/pc-change-in-energy-from-initiatives',
  ownerSourceName: 'baseline2019',
  compositeUtrCode: 'survey/generic/pc-change-in-energy-from-initiatives',
  fragmentUtrCodes: [
    'gri/2019/302-4/a',
    'gri/2019/302-1/e/base-year',
  ],
  fragmentUtrConfiguration: {
    'gri/2019/302-4/a': vc.allValueChain,
    'gri/2019/302-1/e/base-year': vc.allValueChain,
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2019/302-4/a',
      },
      b: {
        code: 'gri/2019/302-1/e/base-year',
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Average,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '100*{a}/{b}'
            },
          ],
        }
      ]
    },
  }
};
