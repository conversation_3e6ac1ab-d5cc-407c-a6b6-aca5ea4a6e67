/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const sdg_14_1_total_waste_water_discharged: CompositeUtrConfigInterface = {
  code: 'gri/2019/survey/sdg/14.1/total-waste-water-discharged',
  ownerSourceName: 'baseline2019',
  compositeUtrCode: 'survey/sdg/14.1/total-waste-water-discharged',
  fragmentUtrCodes: [
    'gri/2019/303-4/b',
  ],
  fragmentUtrConfiguration: {
    'gri/2019/303-4/b': vc.allValueChain,
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2019/303-4/b',
        valueListCode: 'other_water'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Average,
          conditions: [
            {
              type: CalculationType.Formula,
              condition: '{a}',
              result: {
                gte: 0,
              },
            },
          ],
          variables: [
            {
              type: CalculationType.Formula,
              formula: '1000*{a}'
            },
          ],
        }
      ]
    },
  }
};
