/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const generic_pc_renewable_fuel_consumption: CompositeUtrConfigInterface = {
  code: 'gri/2019/survey/generic/pc-renewable-fuel-consumption',
  compositeUtrCode: 'survey/generic/pc-renewable-fuel-consumption',
  ownerSourceName: 'baseline2019',
  fragmentUtrCodes: [
    'gri/2019/302-1/a',
    'gri/2019/302-1/b',
  ],
  fragmentUtrConfiguration: {
    'gri/2019/302-1/a':  vc.allValueChain,
    'gri/2019/302-1/b':  vc.allValueChain,
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2019/302-1/a'
      },
      b: {
        code: 'gri/2019/302-1/b',
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Average,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '100*{b}/({a}+{b})'
            },
          ]
        }
      ]
    },
  }
};
