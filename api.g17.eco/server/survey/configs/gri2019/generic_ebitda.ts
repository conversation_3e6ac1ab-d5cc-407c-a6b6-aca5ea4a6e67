/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const generic_ebitda: CompositeUtrConfigInterface = {
  code: 'gri/2019/survey/generic/ebitda',
  ownerSourceName: 'baseline2019',
  compositeUtrCode: 'survey/generic/ebitda',
  fragmentUtrCodes: [
    'gri/2019/survey/generic/ebitda',
  ],
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2019/survey/generic/ebitda',
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Average,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}'
            }
          ]
        }
      ]
    },
  }
};
