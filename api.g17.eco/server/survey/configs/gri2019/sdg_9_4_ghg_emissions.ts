/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import { ConditionTypes } from '../../../rules/condition';
import * as vc from '../../../util/valueChain';

export const sdg_9_4_ghg_emissions: CompositeUtrConfigInterface = {
  code: 'gri/2019/survey/sdg/9.4/ghg-emissions',
  ownerSourceName: 'baseline2019',
  compositeUtrCode: 'survey/generic/ghg-emissions',
  fragmentUtrCodes: [
    'gri/2019/305-1/a',
    'gri/2019/305-2/a',
    'gri/2019/305-3/a',
  ],
  fragmentUtrConfiguration: {
    'gri/2019/305-1/a': vc.allValueChain,
    'gri/2019/305-2/a': vc.allValueChain,
    'gri/2019/305-3/a': vc.allValueChain,
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2019/305-1/a',
      },
      b: {
        code: 'gri/2019/305-2/a',
      },
      c: {
        code: 'gri/2019/305-3/a',
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Average,
          conditions: [
            {
              type: ConditionTypes.Formula,
              condition: '{a}',
              result: {
                isNotApplicable: true
              },
            },
            {
              type: ConditionTypes.Formula,
              condition: '{b}',
              result: {
                isNotApplicable: true
              },
            },
          ],
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{c}'
            },
          ],
        },
        {
          type: CalculationType.Average,
          conditions: [
            {
              type: ConditionTypes.Formula,
              condition: '{a}',
              result: {
                isNotApplicable: true
              },
            },
            {
              type: ConditionTypes.Formula,
              condition: '{c}',
              result: {
                isNotApplicable: true
              },
            },
          ],
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{b}'
            },
          ],
        },
        {
          type: CalculationType.Average,
          conditions: [
            {
              type: ConditionTypes.Formula,
              condition: '{b}',
              result: {
                isNotApplicable: true
              },
            },
            {
              type: ConditionTypes.Formula,
              condition: '{c}',
              result: {
                isNotApplicable: true
              },
            },
          ],
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}'
            },
          ],
        },
        {
          type: CalculationType.Average,
          conditions: [
            {
              type: ConditionTypes.Formula,
              condition: '{a}',
              result: {
                isNotApplicable: true
              },
            },
          ],
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{b}+{c}'
            },
          ],
        },
        {
          type: CalculationType.Average,
          conditions: [
            {
              type: ConditionTypes.Formula,
              condition: '{b}',
              result: {
                isNotApplicable: true
              },
            },
          ],
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}+{c}'
            },
          ],
        },
        {
          type: CalculationType.Average,
          conditions: [
            {
              type: ConditionTypes.Formula,
              condition: '{c}',
              result: {
                isNotApplicable: true
              },
            },
          ],
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}+{b}'
            },
          ],
        },
        {
          type: CalculationType.Average,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}+{b}+{c}'
            },
          ],
        },
      ]
    },
  }
};
