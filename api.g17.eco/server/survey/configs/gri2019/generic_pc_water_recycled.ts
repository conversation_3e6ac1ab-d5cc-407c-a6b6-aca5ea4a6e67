/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import { ConditionTypes } from '../../../rules/condition';
import * as vc from '../../../util/valueChain';

export const generic_pc_water_recycled: CompositeUtrConfigInterface = {
  code: 'gri/2019/survey/generic/pc-water-recycled',
  compositeUtrCode: 'survey/generic/pc-water-recycled',
  ownerSourceName: 'baseline2019',
  fragmentUtrCodes: [
    'gri/2019/303-3/a',
  ],
  fragmentUtrConfiguration: {
    'gri/2019/303-3/a': vc.allValueChain,
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2019/303-3/a',
        valueListCode: 'produced_water',
      },
      b: {
        code: 'gri/2019/303-3/a',
        valueListCode: 'third_party_water',
      },
      c: {
        code: 'gri/2019/303-3/a',
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Average,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '100*{b}/{c}'
            },
          ],
          conditions: [
            {
              type: ConditionTypes.Formula,
              condition: '{a}',
              result: {
                isNotApplicable: true
              },
            },
          ]
        },
        {
          type: CalculationType.Average,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '100*{a}/{c}'
            },
          ],
          conditions: [
            {
              type: ConditionTypes.Formula,
              condition: '{b}',
              result: {
                isNotApplicable: true
              },
            },
          ]
        },
        {
          type: CalculationType.Average,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '100*({a}+{b})/{c}'
            },
          ]
        }
      ]
    },
  }
};
