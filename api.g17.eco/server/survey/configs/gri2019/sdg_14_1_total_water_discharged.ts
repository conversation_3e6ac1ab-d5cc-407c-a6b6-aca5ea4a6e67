/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';
import * as vc from '../../../util/valueChain';

export const sdg_14_1_total_water_discharged: CompositeUtrConfigInterface = {
  code: 'gri/2019/survey/sdg/14.1/normalization/total-water-discharged',
  compositeUtrCode: 'survey/sdg/14.1/normalization/total-water-discharged',
  ownerSourceName: 'baseline2019',
  fragmentUtrCodes: [
    'gri/2019/303-4/a',
  ],
  fragmentUtrConfiguration: {
    'gri/2019/303-4/a': vc.allValueChain
  },
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2019/303-4/a'
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Average,
          conditions: [
            {
              type: CalculationType.Formula,
              condition: '{a}',
              result: {
                gte: 0,
              },
            },
          ],
          variables: [
            {
              type: CalculationType.Formula,
              formula: '1000*{a}'
            },
          ],
        }
      ]
    },
  }
};
