/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from '../../compositeUtrConfigs';
import { CalculationStrategy, CalculationType } from '../../../rules/rule';

export const generic_capex_capital_expenditure: CompositeUtrConfigInterface = {
  code: 'gri/2019/survey/generic/capex-capital-expenditure',
  ownerSourceName: 'baseline2019',
  compositeUtrCode: 'survey/generic/capex-capital-expenditure',
  fragmentUtrCodes: [
    'gri/2019/capex-capital-expenditure',
  ],
  importConfigurationData: {
    variables: {
      a: {
        code: 'gri/2019/capex-capital-expenditure',
      },
    },
    calculation: {
      type: CalculationStrategy.FirstMatch,
      values: [
        {
          type: CalculationType.Average,
          variables: [
            {
              type: CalculationType.Formula,
              formula: '{a}'
            }
          ]
        }
      ]
    },
  }
};
