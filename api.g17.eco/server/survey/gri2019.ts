/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { CompositeUtrConfigInterface } from './compositeUtrConfigs';
import { generic_capex_capital_expenditure } from './configs/gri2019/generic_capex_capital_expenditure';
import { generic_ebitda } from './configs/gri2019/generic_ebitda';
import { generic_pc_change_in_energy_from_initiatives } from './configs/gri2019/generic_pc_change_in_energy_from_initiatives';
import { generic_pc_renewable_fuel_consumption } from './configs/gri2019/generic_pc_renewable_fuel_consumption';
import { generic_pc_water_from_3rd_party } from './configs/gri2019/generic_pc_water_from_3rd_party';
import { generic_pc_water_recycled } from './configs/gri2019/generic_pc_water_recycled';
import { generic_pc_women_salary_to_men } from './configs/gri2019/generic_pc_women_salary_to_men';
import { sdg_6_3_pc_waste_water_treated } from './configs/gri2019/sdg_6_3_pc_waste_water_treated';
import { sdg_6_3_unplanned_waste_water_discharges } from './configs/gri2019/sdg_6_3_unplanned_waste_water_discharges';
import { sdg_6_4_pc_water_withdrawl_from_scarce } from './configs/gri2019/sdg_6_4_pc_water_withdrawl_from_scarce';
import { sdg_6_4_total_water_withdrawl } from './configs/gri2019/sdg_6_4_total_water_withdrawl';
import { sdg_6_4_water_consumption } from './configs/gri2019/sdg_6_4_water_consumption';
import { sdg_7_3_energy_consumption } from './configs/gri2019/sdg_7_3_energy_consumption';
import { sdg_9_4_ghg_emissions } from './configs/gri2019/sdg_9_4_ghg_emissions';
import { sdg_12_4_pc_ghg_emissions_1_2 } from './configs/gri2019/sdg_12_4_pc_ghg_emissions_1_2';
import { sdg_13_1_r_and_d_technology_spend } from './configs/gri2019/sdg_13_1_r_and_d_technology_spend';
import { sdg_14_1_pc_water_discharge_recycled } from './configs/gri2019/sdg_14_1_pc_water_discharge_recycled';
import { sdg_14_1_total_waste_water_discharged } from './configs/gri2019/sdg_14_1_total_waste_water_discharged';
import { sdg_14_1_total_water_discharged } from './configs/gri2019/sdg_14_1_total_water_discharged';
import { Blueprint } from '../repository/BlueprintRepository';
import { Blueprints } from './blueprints';
import * as vc from '../util/valueChain';
import { blueprintDefaultUnitConfig } from '../service/units/unitTypes';
import config from '../config';

const configs = [
  generic_ebitda,
  generic_capex_capital_expenditure,
  generic_pc_change_in_energy_from_initiatives,
  generic_pc_renewable_fuel_consumption,
  generic_pc_water_from_3rd_party,
  generic_pc_water_recycled,
  generic_pc_women_salary_to_men,
  sdg_6_3_unplanned_waste_water_discharges,
  sdg_6_3_pc_waste_water_treated,
  sdg_6_4_pc_water_withdrawl_from_scarce,
  sdg_6_4_total_water_withdrawl,
  sdg_6_4_water_consumption,
  sdg_7_3_energy_consumption,
  sdg_9_4_ghg_emissions,
  sdg_12_4_pc_ghg_emissions_1_2,
  sdg_13_1_r_and_d_technology_spend,
  sdg_14_1_pc_water_discharge_recycled,
  sdg_14_1_total_waste_water_discharged,
  sdg_14_1_total_water_discharged,
];

export const gri2019ConfigCodeMap: { [key: string]: CompositeUtrConfigInterface } = {};
configs.forEach(c => {
  gri2019ConfigCodeMap[c.code] = c;
});

const assetsCDN = config.assets.cdn;

export const gri2019: Blueprint = {
  references: [
    Blueprints.Baseline2019,
  ],
  name: 'GRI 2019',
  code: Blueprints.Gri2019,
  logo: {
    src: assetsCDN + '/images/logos/gri.png',
    alt: 'GRI',
  },
  unitConfig: blueprintDefaultUnitConfig,
  additionalConfigs: [ ], // We are adding configs dynamically at the bottom of this file
  forms: [
    {
      utrGroupConfig: {
        groupName: 'Normalization',
        utrCodes: [
          'gri/2019/capex-capital-expenditure',
          'gri/2019/survey/generic/ebitda',
          'gri/2019/survey/generic/opex',
          'gri/2019/survey/generic/water-intensity',
          'gri/2019/survey/generic/energy-intensity',
          'gri/2019/survey/generic/ghg-intensity',
          'gri/2019/survey/generic/denominator-for-intensity',
          'gri/2019/survey/generic/capex-5-years-average',
          'gri/2019/survey/generic/capex-3-years-cumulative',
          'gri/2019/survey/generic/taxes-paid',
          'gri/2019/survey/generic/tax-rate',
          'gri/2019/survey/generic/capex-lag-one-year',
          'gri/2019/survey/generic/alignment-sdgs-investment',
          'gri/2019/survey/generic/spend-sdgs-initiatives',
          'gri/2019/survey/generic/investment-committed-sdgs'
        ],
        fragmentUtrConfiguration: {
          'gri/2019/survey/generic/spend-sdgs-initiatives': vc.allValueChain,
          'gri/2019/survey/generic/investment-committed-sdgs': vc.allValueChain
        },
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 100: Universal Standards',
        groupData: {
          colour: '#335377',
        },
        utrCodes: []
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 200: Economic Standards',
        groupData: {
          colour: '#72327c',
        },
        utrCodes: []
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 301: Materials',
        groupData: {
          colour: '#17956d',
        },
        utrCodes: []
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 302: Energy',
        groupData: {
          colour: '#17956d',
          link: 'https://www.globalreporting.org/standards/media/1009/gri-302-energy-2016.pdf',
          icon: assetsCDN + '/images/logos/gri.png',
        },
        utrCodes: [
          'gri/2019/302-1/a',
          'gri/2019/302-1/b',
          'gri/2019/302-1/c',
          'gri/2019/302-1/d',
          'gri/2019/302-1/e',
          'gri/2019/302-4/a',
          'gri/2019/302-5/a',
        ],
        fragmentUtrConfiguration: {
          'gri/2019/302-1/a': vc.allValueChain,
          'gri/2019/302-1/b': vc.allValueChain,
          'gri/2019/302-1/c': vc.allValueChain,
          'gri/2019/302-1/d': vc.allValueChain,
          'gri/2019/302-1/e': vc.allValueChain,
          'gri/2019/302-4/a': vc.allValueChain,
          'gri/2019/302-5/a': vc.allValueChain
        },
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 303: Water and Effluents',
        groupData: {
          colour: '#17956d',
          link: 'https://www.globalreporting.org/standards/media/1909/gri-303-water-and-effluents-2018.pdf',
          icon: assetsCDN + '/images/logos/gri.png',
        },
        utrCodes: [
          'gri/2019/303-3/a',
          'gri/2019/303-3/b',
          'gri/2019/303-4/a',
          'gri/2019/303-4/b',
          'gri/2019/303-4/c',
          'gri/2019/303-4/d',
          'gri/2019/303-4/e',
          'gri/2019/303-5/a',
          'gri/2019/303-5/b',
          'gri/2019/303-5/c',
          'gri/2019/303-5/d',
        ],
        fragmentUtrConfiguration: {
          'gri/2019/303-3/a': vc.allValueChain,
          'gri/2019/303-3/b': vc.allValueChain,
          'gri/2019/303-4/a': vc.allValueChain,
          'gri/2019/303-4/b': vc.allValueChain,
          'gri/2019/303-4/c': vc.allValueChain,
          'gri/2019/303-4/d': vc.allValueChain,
          'gri/2019/303-4/e': vc.allValueChain,
          'gri/2019/303-5/a': vc.allValueChain,
          'gri/2019/303-5/b': vc.allValueChain,
          'gri/2019/303-5/c': vc.allValueChain,
          'gri/2019/303-5/d': vc.allValueChain,
        }
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 304: Biodiversity',
        groupData: {
          colour: '#17956d',
        },
        utrCodes: []
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 305: Emissions',
        groupData: {
          colour: '#17956d',
          link: 'https://www.globalreporting.org/standards/media/1012/gri-305-emissions-2016.pdf',
          icon: assetsCDN + '/images/logos/gri.png',
        },
        utrCodes: [
          'gri/2019/305-1/a',
          'gri/2019/305-1/b',
          'gri/2019/305-1/c',
          'gri/2019/305-2/a',
          'gri/2019/305-2/b',
          'gri/2019/305-2/c',
          'gri/2019/305-3/a',
        ],
        fragmentUtrConfiguration: {
          'gri/2019/305-1/a': vc.allValueChain,
          'gri/2019/305-1/b': vc.allValueChain,
          'gri/2019/305-1/c': vc.allValueChain,
          'gri/2019/305-2/a': vc.allValueChain,
          'gri/2019/305-2/b': vc.allValueChain,
          'gri/2019/305-2/c': vc.allValueChain,
          'gri/2019/305-3/a': vc.allValueChain,
        }
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 400: Social Standards',
        groupData: {
          colour: '#fa6a01',
        },
        utrCodes: []
      }
    },
    {
      utrGroupConfig: {
        groupName: 'GRI 405: Diversity and Equal Opportunity',
        groupData: {
          colour: '#fa6a01',
          link: 'https://www.globalreporting.org/standards/media/1020/gri-405-diversity-and-equal-opportunity-2016.pdf',
          icon: assetsCDN + '/images/logos/gri.png',
        },
        utrCodes: [
          'gri/2019/405-2/a',
          'gri/2019/405-2/b',
        ],
        fragmentUtrConfiguration: {
          'gri/2019/405-2/a': vc.allValueChain,
          'gri/2019/405-2/b': vc.allValueChain,
        }
      }
    },
  ],
};

configs.forEach(c => {
  gri2019.additionalConfigs.push({ compositeConfig: c.code });
});
