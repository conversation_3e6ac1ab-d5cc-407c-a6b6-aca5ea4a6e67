/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import type { UniversalTrackerPlain } from '../../../models/universalTracker';
import { NotApplicableTypes, type UniversalTrackerValuePlain } from '../../../models/universalTrackerValue';
import {
  AggregationMode,
  type AggregationState,
  ColumnAggregationCompatibility,
  ColumnType,
  ColumnValueAggregation,
  type TableColumn,
  UtrValueType,
  ValueAggregation,
  ValueAggregationChildrenCompatibility,
  ValueAggregationSiblingsCompatibility,
  type ValueTable,
} from '../../../models/public/universalTrackerType';
import type { RowData, ValueData } from '../../../models/public/universalTrackerValueType';
import type { DisaggregationUniversalTrackerValueFields } from './AggregatedUniversalTracker';
import { normalizeText } from '../../../util/string';
import { tryCalculation } from '../../../rules/calculation/formula';
import type { VariableMap } from '../../../rules/rule';
import { trimValue } from './utils';

/**
 * Type guard to check if a weight value is valid
 */
function isValidWeight(weight: unknown): weight is number {
  return typeof weight === 'number' && !isNaN(weight) && isFinite(weight) && weight >= 0;
}

export type UtrValueFields = Pick<UniversalTrackerValuePlain,
  'value' |
  'valueData' |
  'effectiveDate' |
  'assuranceStatus' |
  'universalTrackerId' |
  'type' |
  'period'
>

export interface AggregatorValueFields extends UtrValueFields {
  utr: UniversalTrackerPlain;
  /** @description - Count of UTRVs added to the aggregation */
  aggregationCount: number;

  /** @description - Count of rows in the table for utrvs */
  tableRowCount: number;

  /** @description - Count of columns in the table for utrvs */
  tableColumnCount: { [code: string]: number | undefined };

  /** @description - Count of columns in the table for utrvs grouped by group key */
  tableGroupColumnCount: { [groupKey: string]: { [code: string]: number | undefined } | undefined };

  /** @description - Count of rows in the table for utrvs grouped by group key */
  tableGroupRowCount: { [groupKey: string]: number | undefined };

  valueData?: ValueData;
  disaggregation: DisaggregationUniversalTrackerValueFields[];

  /** @description - Flexible state storage for different aggregation types */
  aggregationState?: AggregationState;

  /** @description - Current aggregation mode being used */
  aggregationMode: AggregationMode;
}

export type AggregatorFn = (acc: AggregatorValueFields, utrv: DisaggregationUniversalTrackerValueFields) => AggregatorValueFields

interface ColumnAggregatorParams {
  accValue: number | string;
  col: RowData;
  acc: AggregatorValueFields;
  utrv: UtrValueFields;
  averageCounter: number;
  currentRowData: RowData[];
  groupKey: string;
}

type ColumnAggregatorFn = (params: ColumnAggregatorParams) => number | string;

const recalculateAverage = (existing: number, aggregate: number, count: number) => {
  return ((existing * count) + aggregate) / (count + 1);
};


const columnWithSetValue = (col: RowData | undefined): col is RowData => {
  return typeof col === 'object' && col.value !== undefined;
}

const shouldSkipNoValue = (col: RowData, valueAggregation: ColumnValueAggregation | undefined): boolean => {
  // Weighted average columns should process undefined values as 0
  if (valueAggregation === ColumnValueAggregation.ColumnWeightedAverageAggregator) {
    return false; // Don't skip - process undefined values as 0
  }

  // For columns without aggregation (like text columns) or other aggregation types,
  // skip only if column has no set value
  return !columnWithSetValue(col);
}

class Aggregators {
  // @TODO - what to do if NA or NR
  static valueSumAggregator: AggregatorFn = (acc, utrv) => {
    acc.value = (acc.value || 0) + (utrv.value || 0);
    return acc;
  }

  static valueAverageAggregator: AggregatorFn = (acc, utrv) => {
    if (utrv.valueData?.notApplicableType === NotApplicableTypes.NA) {
      return acc;
    }
    acc.value = recalculateAverage(acc.value || 0, utrv.value || 0, acc.aggregationCount);
    return acc;
  }

  static valueCountAggregator: AggregatorFn = (acc, utrv) => {
    if (utrv.valueData?.notApplicableType === NotApplicableTypes.NA) {
      return acc;
    }
    acc.value = (acc.value ?? 0) + 1;
    return acc;
  }

  static valueConcatenateAggregator: AggregatorFn = (acc, utrv) => {
    if (!utrv.valueData?.data || typeof utrv.valueData.data !== 'string') {
      return acc;
    }

    if (!acc.valueData?.data) {
      acc.valueData = {
        data: utrv.valueData.data
      };
      return acc;
    }
    acc.valueData.data = acc.valueData.data.concat(`, ${utrv.valueData.data}`);
    return acc;
  }

  static numericValueListAverageAggregator: AggregatorFn = (acc, utrv) => {
    if (typeof utrv.valueData?.data !== 'object') {
      return acc;
    }

    Object.entries(utrv.valueData.data).forEach(([code, v]) => {
      const value = Number(v || 0);
      if (acc.valueData?.data === undefined) {
        acc.valueData = {
          data: {},
        };
      }

      acc.valueData.data[code] = recalculateAverage(Number(acc.valueData.data[code] || 0), value, acc.aggregationCount);
    });

    const utrvValueListCodes = Object.keys(utrv.valueData?.data);
    // find codes that are missing in the current utrv answer to recalculate average of each code for combined utrv (acc)
    const missingCodes = Object.keys(acc.valueData?.data).filter((k) => utrvValueListCodes.indexOf(k) === -1);
    missingCodes.forEach((code) => {
      if (acc.valueData?.data) {
        acc.valueData.data[code] = recalculateAverage(acc.valueData.data[code], 0, acc.aggregationCount);
      }
    });

    acc.value = recalculateAverage(acc.value || 0, utrv.value || 0, acc.aggregationCount);

    return acc;
  }

  static numericValueListSumAggregator: AggregatorFn = (acc, utrv) => {
    if (typeof utrv.valueData?.data !== 'object') {
      return acc;
    }
    Object.entries(utrv.valueData.data).forEach(([code, v]) => {
      const value = Number(v);
      if (acc.valueData === undefined) {
        acc.valueData = {
          data: {}
        }
      }

      if (acc.valueData.data === undefined) {
        acc.valueData.data = {}
      }

      if (acc.valueData.data[code] === undefined) {
        acc.valueData.data[code] = value;
      } else {
        acc.valueData.data[code] = Number(acc.valueData.data[code]) + value;
      }
    });

    acc.value = (acc.value || 0) + (utrv.value || 0);

    return acc;
  }

  static textCountAggregator: AggregatorFn = (acc, utrv) => {
    return Aggregators.valueListCountAggregator(acc, utrv);
  }

  static valueListCountAggregator: AggregatorFn = (acc, utrv) => {
    if (!utrv.valueData?.data) {
      return acc;
    }

    const values = Array.isArray(utrv.valueData.data) ? utrv.valueData.data : [utrv.valueData.data];
    values.forEach(v => {
      if (typeof v === 'string') {
        if (acc.valueData === undefined) {
          acc.valueData = {
            data: {}
          }
        }
        if (!acc.valueData.data[v]) {
          acc.valueData.data[v] = 0
        }
        acc.valueData.data[v] = Number(acc.valueData.data[v]) + 1;
      }
    })

    return acc;
  }

  static latestAggregator: AggregatorFn = (acc, utrv) => {
    if (!acc.effectiveDate || utrv.effectiveDate >= acc.effectiveDate) {
      acc.effectiveDate = utrv.effectiveDate;
      acc.value = utrv.value;
      if (utrv.valueData) {
        if (acc.valueData === undefined) {
          acc.valueData = {
            data: {}
          }
        }
        acc.valueData.data = utrv.valueData.data;
        acc.valueData.table = utrv.valueData.table;
        acc.valueData.notApplicableType = utrv.valueData.notApplicableType;
      }
    }
    return acc;
  }

  static tableAggregator: AggregatorFn = (acc, utrv) => {
    const tableDefinition = acc.utr.valueValidation?.table
    const tableData = utrv.valueData?.table;
    if (!tableData || !tableDefinition) {
      return acc;
    }

    const accRow: { [key: string]: string | number } = {};
    // Prefill initial values with current reducer accumulated values
    tableDefinition.columns.forEach(({ code }) => {
      const col = acc?.valueData?.table?.[0].find((column) => column.code === code);
      if (col?.value) {
        accRow[code] = col?.value;
      }
    });

    tableData.reduce((rowAcc, row) => {
      tableDefinition.columns.forEach((tableColumn) => {
        const { code } = tableColumn;
        const { valueAggregation , aggregatorFn } = getAggregatorByColumn(tableColumn, acc.aggregationMode);
        if (valueAggregation === ColumnValueAggregation.ColumnPostAggregationCalculation) {
          return; // Skip post-aggregation calculation columns in first pass
        }

        const col = row.find((column) => column.code === code);
        if (!col || shouldSkipNoValue(col, valueAggregation)) {
          return;
        }

        const currentCounter = acc.tableColumnCount[code] ?? 0;

        rowAcc[code] = aggregatorFn({
          accValue: rowAcc[code],
          col,
          acc,
          utrv,
          averageCounter: currentCounter,
          currentRowData: row,
          groupKey: '__default__' // Simple table aggregation uses default group
        });
        acc.tableColumnCount[code] = currentCounter + 1;
      });

      acc.tableRowCount += 1;

      return rowAcc;
    }, accRow);

    // Second pass: recalculate calculated columns using aggregated values
    recalculateCalculatedColumns(tableDefinition, accRow);

    const cols: RowData[] = tableDefinition.columns
      .filter(({ code }) => accRow[code] !== undefined)
      .map(({ code }) => ({
        code: code,
        value: accRow[code]
      }));

    acc.valueData = {
      table: [
        cols
      ]
    }

    return acc;
  }

  // Append new rows, and add source utrvId
  static tableConcatenationAggregator: AggregatorFn = (acc, utrv) => {
    const tableDefinition = acc.utr.valueValidation?.table
    const tableData = utrv.valueData?.table;
    if (!tableData || !tableDefinition) {
      return acc;
    }

    if (!acc.valueData) {
      acc.valueData = {};
    }
    if (!acc.valueData.table) {
      acc.valueData.table = [];
    }

    tableData.forEach(row => acc.valueData?.table?.push([
      {
        code: 'utrvId',
        value: utrv._id
      },
      ...row
    ]));
    return acc;
  }

  // Group rows by a key, and aggregate the values
  static tableRowGroupAggregator: AggregatorFn = (acc, utrv) => {
    const tableDefinition = acc.utr.valueValidation?.table
    const tableData = utrv.valueData?.table;
    const groupColumns = acc.utr.valueValidation?.table?.aggregation?.columns.map(c => c.code);
    if (!tableData || !tableDefinition || !groupColumns) {
      return acc;
    }

    const groupKeyGenerator = (row: RowData[]) => {
      return groupColumns
        .map((c) => {
          const rawValue = row.find((r) => r.code === c)?.value;
          return normalizeText(rawValue, { lowercase: true });
        })
        .join('-');
    };

    const groupedData: { [key: string]: { [code: string]: number | string } } = {};

    // We process this again???
    (acc.valueData?.table || []).forEach((row) => {
      const key = groupKeyGenerator(row);

      if (!groupedData[key]) {
        groupedData[key] = {};
      }

      tableDefinition.columns.forEach((tableColumn) => {
        const { code, type } = tableColumn;
        const { valueAggregation, aggregatorFn } = getAggregatorByColumn(tableColumn, acc.aggregationMode);
        if (valueAggregation === ColumnValueAggregation.ColumnPostAggregationCalculation) {
          return; // Skip post-aggregation calculation columns in first pass
        }

        const col = row.find((column) => column.code === code);
        if (!col || shouldSkipNoValue(col, valueAggregation)) {
          return;
        }

        // Skip weighted average re-processing in first loop - it should have been calculated already
        if (valueAggregation === ColumnValueAggregation.ColumnWeightedAverageAggregator) {
          groupedData[key][code] = groupedData[key][code] ?? col.value;
          return;
        }

        const value = type === ColumnType.Text ? groupedData[key][code] ?? col.value : groupedData[key][code];

        // Averages should have been calculated in the accumulator already,
        // we do not average again in this step, just grouping the data again.
        const defaultAverage = 0;

        groupedData[key][code] = aggregatorFn({
          accValue: value,
          col,
          acc,
          utrv,
          averageCounter: defaultAverage,
          currentRowData: row,
          groupKey: key // Use the actual group key for group isolation
        });
      });
    });

    // New data should increase the counts
    tableData.forEach((row) => {
      const key = groupKeyGenerator(row);

      if (!groupedData[key]) {
        groupedData[key] = {};
      }

      tableDefinition.columns.forEach((tableColumn) => {
        const { code, type } = tableColumn;
        const { valueAggregation, aggregatorFn } = getAggregatorByColumn(tableColumn, acc.aggregationMode);

        if (valueAggregation === ColumnValueAggregation.ColumnPostAggregationCalculation) {
          return; // Skip post-aggregation calculation columns in first pass
        }

        const col = row.find((column) => column.code === code);
        if (!col || shouldSkipNoValue(col, valueAggregation)) {
          return;
        }

        if (!acc.tableGroupColumnCount[key]) {
          acc.tableGroupColumnCount[key] = {};
        }
        const value = type === ColumnType.Text ? groupedData[key][code] ?? col.value : groupedData[key][code];
        const avgCounter = acc.tableGroupColumnCount[key][code] ?? 0;
        groupedData[key][code] = aggregatorFn({
          accValue: value,
          col,
          acc,
          utrv,
          averageCounter: avgCounter,
          currentRowData: row,
          groupKey: key // Use the actual group key for group isolation
        });

        acc.tableGroupColumnCount[key][code] = avgCounter + 1;
      });

      acc.tableGroupRowCount[key] = (acc.tableGroupRowCount[key] ?? 0) + 1;
    });

    // Second pass: recalculate calculated columns for each group
    Object.keys(groupedData).forEach(key => {
      recalculateCalculatedColumns(tableDefinition, groupedData[key]);
    });

    const newTableData: RowData[][] = Object.keys(groupedData).map(key => {
      return tableDefinition.columns.map(({ code }) => ({
        code: code,
        // normalize values of grouped columns by trimming extra spaces (e.g., 'Gasoline  Consumption ' -> 'Gasoline Consumption')
        value: groupColumns.includes(code) ? trimValue(groupedData[key][code]) : groupedData[key][code],
      }));
    });

    acc.valueData = {
      table: newTableData
    };

    return acc;
  }

  static columnSumAggregator: ColumnAggregatorFn = ({ accValue, col }) => {
    if (isNaN(col.value)) {
      return accValue; // Just ignore it, and we can maintain empty values if none of the values exist
    }

    const value = Number(col.value ?? 0);
    if (accValue === undefined) {
      return value;
    }
    return Number(accValue) + value;
  }

  static columnAverageAggregator: ColumnAggregatorFn = ({ accValue, col, averageCounter }) => {
    const value = isNaN(col.value) ? 0 : Number(col.value);
    return recalculateAverage(Number(accValue) || 0, value || 0, averageCounter);
  }

  static columnLatestAggregator: ColumnAggregatorFn = ({ accValue, col, acc: utrvAcc, utrv }) => {
    if (!utrvAcc.effectiveDate || utrv.effectiveDate >= utrvAcc.effectiveDate) {
      utrvAcc.effectiveDate = utrv.effectiveDate;
      return col.value;
    }
    return accValue;
  }

  static columnMaxAggregator: ColumnAggregatorFn = ({ accValue, col }) => {
    if (isNaN(col.value)) {
      return accValue; // Skip invalid values, maintain existing accumulated value
    }

    const value = Number(col.value ?? 0);
    if (accValue === undefined) {
      return value;
    }
    return Math.max(Number(accValue), value);
  }

  static columnPostAggregationCalculation: ColumnAggregatorFn = ({ accValue }) => {
    // For post-aggregation calculations, we skip the first pass aggregation
    // The actual calculation will happen in a second pass after other columns are aggregated
    // Return the accumulated value without processing the current column value
    return accValue;
  }

  /** Weighted average aggregator that calculates the weighted average of the column values, using the weight formula */
  static columnWeightedAverageAggregator: ColumnAggregatorFn = ({ col, acc: utrvAcc, currentRowData, groupKey }) => {
    // Treat undefined, null, or NaN values as 0 for weighted average columns
    const value = (col.value === undefined || col.value === null || isNaN(Number(col.value))) ? 0 : Number(col.value);

    // Initialize aggregation state if not present
    if (!utrvAcc.aggregationState) {
      utrvAcc.aggregationState = {};
    }
    if (!utrvAcc.aggregationState.weighted) {
      utrvAcc.aggregationState.weighted = {};
    }

    // Initialize state for this group and column if not present
    const columnCode = col.code || 'unknown';
    const stateKey = `${groupKey}:${columnCode}`;

    if (!utrvAcc.aggregationState.weighted[stateKey]) {
      utrvAcc.aggregationState.weighted[stateKey] = {
        numerator: 0,
        denominator: 0,
        count: 0
      };
    }

    const state = utrvAcc.aggregationState.weighted[stateKey];

    // Find the weight formula from the column configuration
    const tableColumn = utrvAcc.utr.valueValidation?.table?.columns?.find(c => c.code === columnCode);
    const modeKey = utrvAcc.aggregationMode;
    const weightFormula = tableColumn?.aggregationConfig?.modes?.[modeKey]?.weightFormula;

    let weight = 1; // Default weight
    if (weightFormula && currentRowData) {
      // Build variable map from current row data (passed as parameter)
      const variables: VariableMap = {};
      currentRowData.forEach(rowCol => {
        if (rowCol.code && rowCol.value !== undefined) {
          variables[rowCol.code] = rowCol.value;
        }
      });

      // Calculate weight using the formula
      const calculatedWeight = tryCalculation({ formula: weightFormula, variables });
      if (isValidWeight(calculatedWeight)) {
        weight = calculatedWeight;
      }

    }

    // Update weighted average state
    state.numerator += value * weight;
    state.denominator += weight;
    state.count += 1;

    // Calculate and return the current weighted average
    return state.denominator === 0 ? 0 : state.numerator / state.denominator;
  }

  static columnConcatenateAggregator: ColumnAggregatorFn = ({ accValue, col }) => {
    const concatenate = accValue ? String(accValue).split(', ') : [];
    concatenate.push(col.value);
    return concatenate.join(', ');
  }

  // Not implemented, types does nothing
  static emptyAggregator: AggregatorFn = (acc) => acc
  static columnEmptyAggregator: AggregatorFn = (acc) => acc
}

const isCompatible = (aggregationFn: string, compatibility: { default: ValueAggregation | ColumnValueAggregation, compatible?: (ValueAggregation | ColumnValueAggregation)[] }) => {
  const availableAggregators = Object.keys(Aggregators);
  if (!availableAggregators.includes(aggregationFn)) {
    return false;
  }
  if (!compatibility) {
    return false;
  }
  if (compatibility.default === aggregationFn) {
    return true;
  }
  if (compatibility.compatible?.includes(aggregationFn as ValueAggregation)) {
    return true;
  }
  return false;
}

// @TODO - should probably do this compatbility check at a UTRV level too, as there could be a disconnect with UTR
export function getAggregatorByUniversalTrackerFn(
  utr: Pick<UniversalTrackerPlain, 'valueType' | 'valueAggregation' | 'aggregationConfig'>,
  valueAggregationMode: AggregationMode
): ValueAggregation {
  const isCombinedAggregation = valueAggregationMode === AggregationMode.Combined;
  const valueType = utr.valueType;
  const compatibility = isCombinedAggregation
    ? ValueAggregationSiblingsCompatibility[valueType]
    : ValueAggregationChildrenCompatibility[valueType];

  // First check if there's a mode-specific override in aggregationConfig
  const modeKey = isCombinedAggregation ? AggregationMode.Combined : AggregationMode.Children;
  const modeSpecificAggregation = utr.aggregationConfig?.modes?.[modeKey]?.valueAggregation;
  if (modeSpecificAggregation && isCompatible(modeSpecificAggregation, compatibility)) {
    return modeSpecificAggregation;
  }

  // Fall back to valueAggregation if no mode-specific config or if it's incompatible
  if (utr.valueAggregation && isCompatible(utr.valueAggregation, compatibility)) {
    return utr.valueAggregation;
  }

  // Finally fall back to system default
  return compatibility.default;
}

export const isCompatibleAggregationOverride = (params: {
  valueType: UtrValueType;
  mode: AggregationMode;
  aggregation: ValueAggregation;
}): boolean => {
  const { valueType, mode, aggregation } = params;

  const compatibility =
    mode === AggregationMode.Combined ? ValueAggregationSiblingsCompatibility : ValueAggregationChildrenCompatibility;

  const typeConfig = compatibility[valueType];
  return typeConfig ? Boolean(typeConfig.compatible?.includes(aggregation)) : false;
};

export const isCompatibleColumnsAggregationOverride = (columns: TableColumn[]): boolean => {
  return columns.every((column) => {
    const columnType = column.type as ColumnType;
    const compatibility = ColumnAggregationCompatibility[columnType];

    if (!column.aggregationConfig?.modes) {
      return true;
    }

    // Helper function to check if aggregation is compatible
    const isAggregationCompatible = (aggregation: ColumnValueAggregation): boolean => {
      return aggregation === compatibility.default || compatibility.compatible?.includes(aggregation) || false;
    };

    // Check Combined mode aggregation
    const combinedAggregation = column.aggregationConfig.modes[AggregationMode.Combined]?.valueAggregation;
    if (combinedAggregation !== undefined && !isAggregationCompatible(combinedAggregation)) {
      return false;
    }

    // Check Children mode aggregation
    const childrenAggregation = column.aggregationConfig.modes[AggregationMode.Children]?.valueAggregation;
    if (childrenAggregation !== undefined && !isAggregationCompatible(childrenAggregation)) {
      return false;
    }

    return true;
  });
};

export function getAggregatorByUniversalTracker(
  utr: Pick<UniversalTrackerPlain, 'valueType' | 'valueAggregation' | 'aggregationConfig'>,
  valueAggregationMode: AggregationMode
): AggregatorFn {
  const fnName = getAggregatorByUniversalTrackerFn(utr, valueAggregationMode);
  return Aggregators[fnName as keyof Aggregators];
}

/**
 * Recalculate calculated columns using aggregated values
 * Extracts variables from formulas and evaluates calculations
 */
function recalculateCalculatedColumns(
  tableDefinition: ValueTable,
  aggregatedData: { [key: string]: string | number | undefined }
): void {
  tableDefinition.columns.forEach(({ code, valueAggregation, calculation }) => {
    if (valueAggregation !== ColumnValueAggregation.ColumnPostAggregationCalculation || !calculation?.formula) {
      return; // Skip columns that don't have explicit post-aggregation calculation
    }

    // Extract variables from formula using existing pattern
    const pattern = /\{(.*?)\}/g;
    const variables: VariableMap = {};

    // Build variables map from aggregated values
    let match;
    while ((match = pattern.exec(calculation.formula)) !== null) {
      const varName = match[1];
      // Get aggregated value from aggregatedData
      variables[varName] = aggregatedData[varName] ?? 0;
    }

    // Use existing tryCalculation to evaluate formula
    const calculatedValue = tryCalculation({ formula: calculation.formula, variables }) as number | string;

    // Store the calculated value
    aggregatedData[code] = calculatedValue;
  });
}

interface AggregatorResult {
  valueAggregation: ColumnValueAggregation;
  aggregatorFn: ColumnAggregatorFn;
}

function getAggregatorByColumn(
  tableColumn: Pick<TableColumn, 'aggregationConfig' | 'type' | 'valueAggregation'>,
  aggregationMode: AggregationMode
): AggregatorResult {
  const { type, valueAggregation } = tableColumn;
  const compatibility = ColumnAggregationCompatibility[type as ColumnType];

  // Check for mode-specific aggregation configuration first
  if (tableColumn?.aggregationConfig?.modes) {
    const modeConfig = tableColumn.aggregationConfig.modes[aggregationMode];
    if (modeConfig?.valueAggregation && isCompatible(modeConfig.valueAggregation, compatibility)) {
      return {
        valueAggregation: modeConfig.valueAggregation,
        aggregatorFn: Aggregators[modeConfig.valueAggregation as keyof Aggregators]
      };
    }
  }

  // Fall back to column-level valueAggregation
  if (valueAggregation && isCompatible(valueAggregation, compatibility)) {
    return {
      valueAggregation,
      aggregatorFn: Aggregators[valueAggregation as keyof Aggregators]
    };
  }

  // Finally fall back to system default
  const defaultValueAggregation = compatibility.default;
  return {
    valueAggregation: defaultValueAggregation,
    aggregatorFn: Aggregators[defaultValueAggregation as keyof Aggregators]
  };
}

// TODO: Should we add more check for new aggregation types? valueConcatenateAggregator, numericValueListAverageAggregator
export function getUtrValueTypeByAggregator(aggregatorFn: ValueAggregation, originalType = UtrValueType.Number) {
  switch (aggregatorFn) {
    case ValueAggregation.ValueSumAggregator:
    case ValueAggregation.ValueCountAggregator:
    case ValueAggregation.ValueAverageAggregator:
      return UtrValueType.Number;
    case ValueAggregation.ValueListCountAggregator:
    case ValueAggregation.TextCountAggregator:
    case ValueAggregation.NumericValueListSumAggregator:
      return UtrValueType.NumericValueList;
    case ValueAggregation.TableColumnAggregator:
    case ValueAggregation.TableConcatenationAggregator:
    case ValueAggregation.TableRowGroupAggregator:
      return UtrValueType.Table;
    case ValueAggregation.EmptyAggregator:
      return UtrValueType.Number;
    case ValueAggregation.LatestAggregator:
    default:
      return originalType;
  }
}
