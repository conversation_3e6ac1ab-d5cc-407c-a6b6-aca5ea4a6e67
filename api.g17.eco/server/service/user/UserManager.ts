/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import User, { type UserCreateData, type UserModel, type UserPlain, type UserUpdateData, } from '../../models/user';
import { type ObjectId } from 'bson';
import {
  type ActivationRequiredEmail,
  createUserEmailService,
  type UserActivateData,
  type UserEmailService
} from './UserEmailService';
import { wwgLogger } from '../wwgLogger';
import { createUserEventService, type UserEventService } from '../event/UserEventService';
import { USER } from '../event/Events';
import { createOnboardingRepository, type OnboardingRepository } from '../../repository/OnboardingRepository';
import { OnboardingStatus } from '../../models/onboarding';
import { dashboardPermissions } from '../../models/commonProperties';
import UserError from '../../error/UserError';
import UserLockManager from './UserLockManager';
import { filterValidRoles, type UserPermissions, UserRoles } from './userPermissions';
import { type Jwt } from '@okta/jwt-verifier';
import BadRequestError from '../../error/BadRequestError';
import { type CreatePassword, getOktaManager, type OktaManager } from './OktaManager';
import { UserInitiativeRepository } from '../../repository/UserInitiativeRepository';
import config from '../../config';
import Initiative from '../../models/initiative';
import UserToken, { UserTokenTypes } from '../../models/userToken';
import { getUserPermissionService, type UserPermissionUpdateModel } from './UserPermissionService';

const defaultAppGroupIds = [config.authentication.appGroup];
const staffAppGroupIds = config.authentication.appStaffGroup
  .split(',')
  .map(id => id.trim())
  .filter(Boolean);

enum PasswordChangeReason {
  PasswordReset = 'password_reset',
  UpdateProfile = 'update_profile',
  UpdateUserByAdmin = 'update_user_by_admin',
}

enum LockReason {
  FailLoginAttempts = 'failed_login_attempts',
  AdminLock = 'admin_lock',
  AdminUnlock = 'admin_unlock',
  PasswordResetUnlock = 'password_reset_unlock',
}

interface ChangePasswordEventData {
  oldPassword?: string;
  newPassword: string;
  isStaff?: boolean,
  reason: PasswordChangeReason;
}

interface ChangePasswordOkta extends ChangePasswordEventData {
  oldPassword: string;
}

interface UserChangeByAdmin extends Partial<UserPlain> {
  password?: string | null;
  oldPassword?: string;
}

const isChangePasswordWithOld = (data: ChangePasswordEventData): data is ChangePasswordOkta => Boolean(data.oldPassword);

export class UserManager {

  constructor(
    private userEmailService: UserEmailService,
    private userEventService: UserEventService,
    private userRepo: typeof UserInitiativeRepository,
    private onboardingRepo: OnboardingRepository,
    private oktaClient: OktaManager,
    private userPermissionService: ReturnType<typeof getUserPermissionService>,
  ) {
  }

  public async activateUser(activateData: Omit<UserActivateData, 'user'> & { userId: string | ObjectId }) {
    const user = await this.userRepo.findUserById(activateData.userId);
    if (!user) {
      throw new Error('User not found');
    }

    if (user.active) {
      throw new Error('User is already active');
    }

    await this.activate(user);

    await this.sendOnboardingActivatedEmail({ ...activateData, user });

    return { message: 'User is now active' };
  }

  public async migrateOktaUser(user: UserModel, password: CreatePassword, groupIds?: string[]) {
    try {
      const oktaUser = await this.createOktaUser(user, password, true, groupIds);
      user.oktaUserId = oktaUser.id;
    } catch (error) {
      if (error.errorCode === 'E0000001') {
        // Assume that user already exists, so we need to fetch and update
        // If the getUserByEmail fails, an error with errorCode=E0000007 will be thrown
        try {
          const oktaUser = await this.oktaClient.getUserByEmail(user.email);
          if (groupIds) {
            for (let i = 0; i < groupIds.length; i++) {
              await oktaUser.addToGroup(groupIds[i]);
            }
          }
          user.oktaUserId = oktaUser.id;
        } catch (error2) {
          throw AggregateError([error, error2]);
        }
      } else {
        throw error;
      }
    }
    await user.save();
    return user;
  }

  private async createOktaUser(
    user: Pick<UserPlain, 'email' | 'firstName' | 'surname'>,
    password: CreatePassword,
    activate = false,
    groupIds?: string[],
  ) {
    return this.oktaClient.createUser({
      email: user.email,
      firstName: user.firstName,
      lastName: user.surname,
      activate,
      groupIds: groupIds,
      credentials: { password }
    }).catch(e => {
      if (this.oktaClient.isOktaError(e)) {
        throw new UserError(this.oktaClient.getOktaErrorMessage(e), { cause: e })
      }
      throw e;
    });
  }

  public async createUser(userData: UserCreateData) {
    const password = { value: userData.password };

    wwgLogger.info(`Creating new user ${userData.email}`);
    const oktaActive = userData.oktaActive ?? true; // Active by default

    const oktaUser = await this.createOktaUser(userData, password, oktaActive, defaultAppGroupIds)
    const provider = oktaUser.credentials.provider;
    return this.createUserWithoutPassword({
      ...userData,
      active: userData.active ?? true,
      oktaUserId: oktaUser.id,
      authenticationProvider: {
        type: provider.type.toString(),
        name: provider.name,
      }
    });
  }

  public async updateUserByAdmin(user: UserModel, data: UserChangeByAdmin) {
    const password = data.password;
    data.password = null;
    const initiativeIds: ObjectId[] = [];

    if (Array.isArray(data?.permissions)) {
      const permissionMap = this.getUserPermissionMap(user);

      data.permissions = data.permissions.map((initiativePermissions) => {
        if (Array.isArray(initiativePermissions?.permissions)) {
          // Filter out non valid permissions
          initiativePermissions.permissions = initiativePermissions
            .permissions.filter((p) => dashboardPermissions.enum.includes(p));
        }

        const { initiativeId, permissions } = initiativePermissions;

        if (permissions?.length === 0) {
          return undefined;
        }

        const isAddingOwnerRole = this.isAddingOwnerRole(permissionMap, initiativePermissions);

        if (isAddingOwnerRole) {
          initiativeIds.push(initiativeId);
        }

          return initiativePermissions;
        })
        .filter(Boolean) as UserPermissions[];
    }

    if (initiativeIds.length) {
      await this.checkConflictingInitiativeOwners(initiativeIds, user)
    }

    user.set(data);

    if (password && data.oldPassword) {
      await this.changePassword(user, {
        isStaff: true,
        newPassword: password,
        oldPassword: data.oldPassword,
        reason: PasswordChangeReason.UpdateUserByAdmin,
      });
    }
    return user.save();
  }
  public async isPasswordExpired(user: UserModel) {
    if (!user.oktaUserId) {
      throw new BadRequestError(`user (${user._id}) doesn't have an okta user id`);
    }
    return await this.oktaClient.isPasswordExpired(user.oktaUserId)
  }
  private async changePassword(user: UserModel, data: ChangePasswordEventData) {
    if (user.authenticationProvider?.type === 'FEDERATION') {
      throw new BadRequestError('Cannot change password for federated SSO user')
    }

    const eventData = {
      isStaff: data.isStaff,
      reason: data.reason,
    };

    if (!user.oktaUserId) {
      throw new BadRequestError(`Trying to change password of a user (${user._id}) that doesn't have an okta user id`);
    }

    if (!isChangePasswordWithOld(data)) {
      throw new BadRequestError('Current user password does not match')
    }

    try {
      if (await this.oktaClient.isPasswordExpired(user.oktaUserId)) {
        throw new BadRequestError(`Password set to expired for (${user._id} ${user.oktaUserId}), user needs to reset`);
      }
      await this.oktaClient.changeUserPassword(user.oktaUserId, data)
      this.userEventService.addEvent(user, USER.events.passwordChanged, eventData);
      return;
    } catch (e) {
      const possibleAuthException = [400, 401, 403].includes(e.status)
      if (e instanceof BadRequestError === false || !possibleAuthException) {
        throw e
      }
      await UserLockManager.incrementLockAttempts(user);
      this.userEventService.addEvent(user, USER.events.loginFail);
      if (UserLockManager.isLocked(user)) {
        await this.oktaClient.expireUserPassword(user.oktaUserId)
        UserLockManager.unlock(user)
      }
      throw e
    }
  }

  private getUserPermissionMap(user: UserModel) {
    const permissionMap = new Map<string, UserRoles[]>();
    user.permissions.forEach((initiativePermissions) => {
      const { initiativeId, permissions } = initiativePermissions;
      permissionMap.set(initiativeId.toString(), permissions);
    });
    return permissionMap;
  }

  private isAddingOwnerRole(
    existingPermissions: Map<string, UserRoles[]>,
    initiativePermissions: UserPermissions<string | ObjectId>
  ) {
    const { initiativeId, permissions } = initiativePermissions;
    if (!permissions.includes(UserRoles.Owner)) {
      return false;
    }

    return !existingPermissions.get(initiativeId.toString())?.includes(UserRoles.Owner);
  }

  private async checkConflictingInitiativeOwners(initiativeIds: ObjectId[], user: UserModel) {
    const owners = await UserInitiativeRepository.findUsersByRole(initiativeIds, UserRoles.Owner, user);
    if (owners.length) {
      const ownerInitiativeIds = new Set(
        owners.map((user) => user.permissions.map((p) => p.initiativeId.toString())).flat()
      );
      const conflictingInitiativeIds = initiativeIds.filter((id) => ownerInitiativeIds.has(id.toString()));

      const initiatives = await Initiative.find({ _id: { $in: conflictingInitiativeIds } }, { name: 1 })
        .lean()
        .exec();
      const initiativeNames = initiatives.map((initiative) => initiative.name).join(', ');

      throw new UserError(`Owner already exists for [${initiativeNames}]`, {
        userId: user._id.toString(),
        initiativeIds: conflictingInitiativeIds.map(String),
        debugMessage: '[Staff] Tried to update the user with permissions that included existing owner',
      });
    }
  }

  public async updateProfile(user: UserModel, data: UserUpdateData) {
    user.set({
      title: data.title,
      firstName: data.firstName,
      surname: data.surname,
      jobTitle: data.jobTitle,
      telephoneNumber: data.telephoneNumber
    });

    if (data.password && data.oldPassword) {
      await this.changePassword(user, {
        newPassword: data.password,
        oldPassword: data.oldPassword,
        reason: PasswordChangeReason.UpdateProfile,
      });
    }

    return user.save();
  }

  public async removeInitiativePermissions(user: UserPermissionUpdateModel, initiativeId: string) {
    if (!user.permissions) {
      return;
    }

    user.permissions = user.permissions.filter(p => String(p.initiativeId) !== initiativeId);
    return user.save();
  }

  public async updateInitiativePermissions(user: UserPermissionUpdateModel, initiativeId: string, newRoles: UserRoles[], replace = true) {
    // Ensure
    if (newRoles.includes(UserRoles.Owner)) {
      await this.enforceSingleOwnerPolicy(user, initiativeId);
    }

    if (user.permissions) {
      const uniqueInitiativeIds = [...new Set(user.permissions.map(p => String(p.initiativeId)))];
      user.permissions = uniqueInitiativeIds.map(id => {
        return user.permissions.find(p => String(p.initiativeId) === id) as UserPermissions;
      });
    }

    const initiativePermissions = user.permissions?.find(p => String(p.initiativeId) === initiativeId);
    if (initiativePermissions) {
      if (replace) {
        // Replace roles
        initiativePermissions.permissions = filterValidRoles(newRoles);
      } else {
        // Merge existing roles
        initiativePermissions.permissions = [...new Set([...filterValidRoles(newRoles), ...initiativePermissions.permissions])];
      }

      return user.save();
    }

    return this.userPermissionService.addAndUpdate({
      user,
      initiativeId,
      permissions: newRoles,
    });
  }

  private async enforceSingleOwnerPolicy(user: Pick<UserModel, '_id'>, initiativeId: string) {
    const owners = await User.find(
      {
        _id: { $ne: user._id },
        permissions: {
          $elemMatch: { initiativeId, permissions: UserRoles.Owner }
        }
      },
      { _id: 1, permissions: 1 })
      .exec();

    if (owners.length === 0) {
      return;
    }

    await Promise.all(
      owners.map(owner => {
        const currentPermissions = owner.permissions.find(p => String(p.initiativeId) === initiativeId);
        if (!currentPermissions) {
          return;
        }
        const newRoles = currentPermissions.permissions.filter(p => p !== UserRoles.Owner);
        return this.updateInitiativePermissions(owner, initiativeId, newRoles);
      })
    );
  }

  public async sendActivationRequiredEmail(activationData: ActivationRequiredEmail) {
    this.userEventService.addEvent(activationData.user, USER.events.activationEmailSent, {
      userTokenId: activationData.userToken._id
    });
    return this.userEmailService.sendActivationRequiredEmail(activationData).catch(wwgLogger.error);
  }

  public async activate(user: UserModel) {
    user.active = true;
    await user.save();
    return this.userEventService.addEvent(user, USER.events.activated);
  }

  /**
   * @TODO All of these flows should not be used anymore, due to OKTA
   *
   * Allow to activate a user from admin (staff) platform.
   *
   * User have not yet onboarded, trigger activation email with onboarding link
   * otherwise send activation confirmation email
   */
  private async sendOnboardingActivatedEmail(activateData: UserActivateData) {
    const registrationData = activateData.user.registrationData;
    if (registrationData && registrationData.onboardingId) {
      const [ob] = await this.onboardingRepo.find({ _id: registrationData.onboardingId });
      if (ob && [
        OnboardingStatus.Pending,
        OnboardingStatus.NotStarted,
      ].includes(ob.status)) {
        return this.userEmailService.sendOnboardingActivatedEmail(ob, activateData).catch(wwgLogger.error);
      }
    }

    return this.userEmailService.sendActivatedEmail(activateData).catch(wwgLogger.error);
  }

  public async createUserWithoutPassword(data: Partial<UserPlain>) {
    if (data._id) {
      throw new Error('Not allowed to specify _id on create');
    }
    const model = new User({ ...data });
    return model.save();
  }

  public async provisionExternalUser(jwt: Jwt): Promise<UserModel> {
    const { sub, uid: oktaUserId } = jwt.claims;
    if (typeof oktaUserId !== 'string') {
      throw new BadRequestError('Missing required claim external user id')
    }

    const existingUser = await User.findOne({ $or: [{ email: sub }, { oktaUserId }] }).exec();
    wwgLogger.info('Start provision external user', { claims: jwt.claims, userId: existingUser?._id });

    if (existingUser) {
      if (existingUser.oktaUserId === oktaUserId) {
        throw new BadRequestError('External id already exist within the system')
      }

      // Provision by email
      existingUser.oktaUserId = oktaUserId;
      return this.provisionExistingUser(existingUser);
    }

    const user = await this.createUserWithoutPassword({
      email: sub,
      active: true,
      oktaUserId: oktaUserId,
      isStaff: false,
    });

    const ids = await this.provisionInitiativePermissions(user);
    this.addProvisioningEvent(user, ids);
    return user;
  }

  public async provisionExistingUser(existingUser: UserModel) {
    const ids = await this.provisionInitiativePermissions(existingUser);
    this.addProvisioningEvent(existingUser, ids);
    return existingUser.save()
  }

  private addProvisioningEvent(user: UserModel, ids: ObjectId[]) {
    const message = `Provisioning user using ${user.email}`;
    const metadata = {
      initiativeIds: ids,
      oktaUserId: user.oktaUserId,
      email: user.email,
      userId: user._id,
      isStaff: user.isStaff,
      isNew: user.isNew,
    };

    wwgLogger.info(message, metadata);
    this.userEventService.addEvent(user, USER.events.provisioned, {
      ...metadata,
      message,
    })
  }

  public async triggerFailedLoginAttempt(user: UserModel, isStaff: boolean) {
    await UserLockManager.incrementLockAttempts(user);
    this.userEventService.addEvent(user, USER.events.loginFail, { isStaff });

    if (UserLockManager.isLocked(user)) {
      this.userEventService.addEvent(user, USER.events.locked, { reason: LockReason.FailLoginAttempts });
    }
  }

  public async adminLock(user: UserModel) {
    await UserLockManager.lock(user);
    this.userEventService.addEvent(user, USER.events.locked, { reason: LockReason.AdminLock });
    return user;
  }

  public async adminUnlock(user: UserModel) {
    await UserLockManager.unlock(user);
    this.userEventService.addEvent(user, USER.events.unlocked, { reason: LockReason.AdminUnlock });
    return user;
  }

  private async provisionInitiativePermissions(user: UserModel): Promise<ObjectId[]> {

    const uid = user.oktaUserId;
    if (!uid) {
      return [];
    }
    const oktaUser = await this.oktaClient.getUser(uid);
    const provider = oktaUser.credentials.provider;

    user.authenticationProvider = {
      type: provider.type.toString(),
      name: provider.name,
    }

    // Provision fields from okta
    const { title, lastName, firstName, profileUrl } = oktaUser.profile;
    user.title = user.title || title;
    user.firstName = user.firstName || firstName;
    user.surname = user.surname || lastName;
    user.profile = user.profile || profileUrl;

    const oktaGroups = await this.oktaClient.fromCollection(oktaUser.listGroups());

    const oktaGroupIds = oktaGroups.map(g => g.id);

    // Check if user is staff
    const isStaff = staffAppGroupIds.some(id => oktaGroupIds.includes(id));
    if (user.isStaff !== isStaff) {
      wwgLogger.info('Updating user isStaff flag', {
        oktaGroupIds,
        staffAppGroupIds,
        userId: user._id,
        oktaUserId: user.oktaUserId,
        isStaff: isStaff,
        previousIsStaff: user.isStaff
      })
      user.isStaff = isStaff
    }

    const matchingIds = await this.userRepo.getInitiativesByOktaGroupIds(oktaGroupIds)

    const matchingInitiativeIds = matchingIds.map(ob => ob._id);
    wwgLogger.info(`Found matching ${matchingIds.length} initiatives`, {
      oktaGroupIds,
      matchingIds: matchingInitiativeIds,
      userId: user._id,
      oktaUserId: user.oktaUserId,
      isStaff: user.isStaff,
    });

    await this.userPermissionService.addMultipleAndSave({
      user,
      permissions: matchingInitiativeIds.map((_id) => ({ initiativeId: _id, permissions: [UserRoles.Viewer] }))
    })

    return matchingInitiativeIds;
  }

  public async hasExternalIdp(email: string) {
    return this.oktaClient.hasExternalIdp(email).catch(err => {
      wwgLogger.error(err);
      return false;
    });
  }

  public async getExternalIdpIds(email: string): Promise<string[]> {
    return this.oktaClient.getExternalIdp(email).catch(err => {
      wwgLogger.error(err);
      return [] as string[];
    });
  }

  public async getIdp(idpId: string) {
    return this.oktaClient.getIdp(idpId)
  }

  public async getOktaUser({ oktaUserId }: { oktaUserId: string }) {
    return this.oktaClient.getUser(oktaUserId)
  }

  public async updateLastLogin(user: UserModel, loginTimestampInSeconds: number): Promise<UserModel> {
    const lastLogin = new Date(loginTimestampInSeconds * 1000);
    if (user.lastLogin && user.lastLogin >= lastLogin) {
      return user; // Nothing to do
    }
    user.lastLogin = lastLogin;

    wwgLogger.info('Updating user last login date', {
      userId: user._id,
      currentLastLogin: user.lastLogin,
      lastLogin,
    })

    this.userEventService.addEvent(user, USER.events.refreshToken, { isStaff: user.isStaff });
    return user.save().catch(e => {
      wwgLogger.error(e);
      return user;
    });
  }

  public async getMultipleAssociatedInitiativesString(permissions: UserPermissions<ObjectId>[]) {
    const initiativeIds = permissions.map((permission) => permission.initiativeId);
    const associatedInitiatives = await Initiative.find(
      {
        _id: {
          $in: initiativeIds,
        },
      },
      { _id: 1, name: 1, code: 1 }
    )
      .lean()
      .exec();
    return associatedInitiatives
      .map((initiative) => `(${initiative._id}/${initiative.name}/${initiative.code})`)
      .join();
  }

  private getMinutesDifference(inputTime: Date) {
    return Math.floor((Date.now() - inputTime.getTime()) / 1000 / 60);
  }

  public async getActivationTokenAge(userId: ObjectId) {
    const existedToken = await UserToken.findOne({ userId, type: UserTokenTypes.UserActivation })
      .sort({
        created: -1,
      })
      .lean()
      .exec();
    if (!existedToken) {
      return undefined;
    }
    return this.getMinutesDifference(existedToken.created);
  }
}

export const createUserManager = (oktaManager?: ReturnType<typeof getOktaManager>) => new UserManager(
  createUserEmailService(),
  createUserEventService(),
  UserInitiativeRepository,
  createOnboardingRepository(),
  oktaManager ?? getOktaManager(),
  getUserPermissionService(),
);

let instance: UserManager;
export const getUserManager = () => {
  if (!instance) {
    instance = createUserManager();
  }
  return instance;
}
