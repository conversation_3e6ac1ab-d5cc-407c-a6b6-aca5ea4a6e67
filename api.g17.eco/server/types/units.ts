/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

/**
 * Shared unit types to avoid circular dependencies
 */
export enum SupportedMeasureUnits {
  time = "time",
  area = "area",
  mass = "mass",
  volume = "volume",
  energy = "energy",
  currency = "currency",
  co2Emissions = "co2Emissions",
  partsPer = "partsPer",
  numberScale = 'numberScale',
  length = 'length',
}

export enum NumberScale {
  Single = 'single',
  Hundreds = 'hundreds',
  Thousands = 'thousands',
  Millions = 'millions',
  Billions = 'billions',
}

export type Unit = string;

export const validUnitTypes = Object.values(SupportedMeasureUnits);

export interface UnitConfig {
  [SupportedMeasureUnits.time]: Unit;
  [SupportedMeasureUnits.area]: Unit;
  [SupportedMeasureUnits.mass]: Unit;
  [SupportedMeasureUnits.volume]: Unit;
  [SupportedMeasureUnits.energy]: Unit;
  [SupportedMeasureUnits.currency]: Unit;
  [SupportedMeasureUnits.co2Emissions]: Unit | undefined;
  [SupportedMeasureUnits.partsPer]: Unit | undefined;
  [SupportedMeasureUnits.numberScale]: NumberScale | undefined;
  [SupportedMeasureUnits.length]: Unit | undefined;
}