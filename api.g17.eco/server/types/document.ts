import { DocumentPlain } from '../models/document';

export enum DocumentMediaType {
  Image = 'image',
  Video = 'video',
  File = 'file',
}

export interface DocumentByType {
  [DocumentMediaType.Image]: InsightDocument[];
  [DocumentMediaType.Video]: InsightDocument[];
  [DocumentMediaType.File]: InsightDocument[];
}

export type InsightDocument = Pick<
  DocumentPlain,
  '_id' | 'title' | 'description' | 'ownerId' | 'url' | 'size' | 'created' | 'metadata' | 'type' | 'public' | 'path'
>;
