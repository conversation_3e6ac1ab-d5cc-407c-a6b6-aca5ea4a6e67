import { Types } from 'mongoose';
import { UtrValueType } from '../models/public/universalTrackerType';
import { UniversalTrackerPlain } from '../models/universalTracker';
import type { Tags } from '../types/universalTrackerValue';
import { UniversalTrackerValuePlain } from '../models/universalTrackerValue';
import { RatingType, WFN_CODE_PREFIX } from '../types/wfn';
import { createArrayOfNumbers } from './array';

const METRIC_OPTIONS_LENGTH_MAPPING: Record<string, number> = {
  // NutritionEducation
  '1-1': 3,
  '1-2': 3,
  '1-3': 3,
  '1-4': 2,
  '1-5': 3,
  '1-6': 1,
  '1-7': 3,
  '1-8': 2,
  // NutritionRelatedHealthChecksAndFollowUp
  '2-1': 3,
  '2-2': 2,
  '2-3': 3,
  '2-4': 3,
  '2-5': 2,
  '2-6': 2,
  '2-7': 1,
  '2-8': 3,
  '2-9': 2,
  // HealthyFoodAtWork
  '3-1': 3,
  '3-2': 2,
  '3-3': 2,
  '3-4': 2,
  '3-5': 3,
  '3-6': 3,
  '3-7': 3,
  '3-8': 2,
  '3-9': 1,
  '3-10': 3,
  '3-11': 2,
  // BreastfeedingSupport
  '4-1': 3,
  '4-2': 3,
  '4-3': 4,
  '4-4': 2,
  '4-5': 2,
  '4-6': 3,
  '4-7': 2,
  '4-8': 3,
  '4-9': 2,
  '4-10': 1,
  '4-11': 3,
  '4-12': 2,
};

const SPECIAL_CASE = {
  '2-1/option2': 1.5,
  '2-1/option3': 2,
  '3-1/option3': 4,
  '3-6/option3': 4,
  '3-7/option3': 5,
};

const getUtrCode = (metric: string) => `${WFN_CODE_PREFIX}${metric}`;
const getOptionCode = (metric: string, option: number | string) => `${getUtrCode(metric)}/option${option}`;

// wfn/2024/1-1/option1 = 1, wfn/2024/1-1/option2 = 2, wfn/2024/1-1/option3 = 3
const getOptionNumberMapping = () => {
  const result: Record<string, number> = {};
  for (const [metric, optionsLength] of Object.entries(METRIC_OPTIONS_LENGTH_MAPPING)) {
    createArrayOfNumbers(1, optionsLength).forEach((i) => {
      result[`${getOptionCode(metric, i)}`] = i;
    });
  }

  for (const [option, value] of Object.entries(SPECIAL_CASE)) {
    result[`${WFN_CODE_PREFIX}${option}`] = value;
  }

  return result;
};

export const VALUE_LIST_OPTION_VALUE_MAPPING: Record<string, number> = getOptionNumberMapping();

type UtrMin = Pick<UniversalTrackerPlain<Types.ObjectId, Tags>, 'code' | 'valueType' | 'typeTags'>;

type UtrvsMin = Pick<UniversalTrackerValuePlain, 'compositeData' | 'valueData'>;

export type UtrsData<UtrType = UniversalTrackerPlain<Types.ObjectId, Tags>, UtrvType = UniversalTrackerValuePlain> = {
  utr: UtrType;
  utrvs: UtrvType[];
};

const getCombinedScore = <UtrType extends UtrMin, UtrvsType extends UtrvsMin>({
  utrsData,
  surveyId,
  metrics,
}: {
  utrsData: UtrsData<UtrType, UtrvsType>[];
  surveyId: Types.ObjectId;
  metrics: string[];
}) => {
  const utrCodes = metrics.map((metric) => getUtrCode(metric));

  return utrsData.reduce((combinedScore, { utr, utrvs }) => {
    if (!utrCodes.includes(utr.code)) {
      return combinedScore;
    }

    const utrv = utrvs.find((utrv) => surveyId.equals(utrv.compositeData?.surveyId ?? ''));

    // if utrv?.valueData?.data is mapped to number then use it or get it from mapping.
    combinedScore +=
      typeof utrv?.valueData?.data === 'number'
        ? utrv.valueData.data
        : VALUE_LIST_OPTION_VALUE_MAPPING[utrv?.valueData?.data] ?? 0;
    return combinedScore;
  }, 0);
};

export const mapValueListToNumber = <UtrType extends UtrMin, UtrvsType extends UtrvsMin>(
  utrsData: UtrsData<UtrType, UtrvsType>[]
) => {
  return utrsData.map(({ utr, utrvs }) => {
    return {
      utr,
      utrvs: utrvs.map((utrv) => {
        const option = utrv.valueData?.data;
        if (
          typeof option === 'string' &&
          utr.valueType === UtrValueType.ValueList &&
          utrv.valueData &&
          VALUE_LIST_OPTION_VALUE_MAPPING[option] !== undefined
        ) {
          utrv.valueData.data = VALUE_LIST_OPTION_VALUE_MAPPING[option];

          const surveyId = utrv.compositeData?.surveyId;
          if (surveyId && option === getOptionCode('3-7', 3)) {
            // This option is 5 points ONLY when scores >2 points combined for metrics 3-4, 3-5 and 3-6
            const combinedScore = getCombinedScore({ utrsData, surveyId, metrics: ['3-4', '3-5', '3-6'] });
            utrv.valueData.data = combinedScore > 2 ? 5 : 3;
          }
        }
        return utrv;
      }),
    };
  });
};

export const getRating = (rating: number): RatingType => {
  if (rating >= 76) {
    return 'Gold';
  }

  if (rating >= 51) {
    return 'Silver';
  }

  if (rating >= 26) {
    return 'Bronze';
  }

  return 'Beginner';
};

export const getScore = (value: string | number | undefined) => {
  if (typeof value === 'string') {
    return 0;
  }
  return value;
};
