/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

// eslint-disable-next-line no-restricted-imports
import dayjs, { ConfigType, ManipulateType, OpUnitType } from 'dayjs';
import utc from 'dayjs/plugin/utc';
import relativeTime from 'dayjs/plugin/relativeTime';
import duration from 'dayjs/plugin/duration';
import customParseFormat from 'dayjs/plugin/customParseFormat';
dayjs.extend(relativeTime)
dayjs.extend(utc);
dayjs.extend(duration);
dayjs.extend(customParseFormat);

export enum DateFormat {
  DefaultDashes = 'YYYY-MM-DD',
  Sortable = 'YYYY-MM-DD HH:mm:ss',
  SortableSlash = 'DD/MM/YYYY HH:mm:ss',
  Slash = 'DD/MM/YYYY',
  Humanize = 'humanize',
  YearMonth = 'YYYY MMMM',
  YearMonthShort = 'YYYY MMM',
  FileName = 'YYYY-MM-DD_HH-mm-ss',
  DayMonthYear = 'DD MMMM YYYY',
  MonthDayYear = 'MMM DD, YYYY',
  FullMonthName = 'MMMM',
  Year = 'YYYY',
  MonthYear = 'MMMM YYYY',
  DayMonth = 'DD MMM'
}

type DateString = string | Date;
export interface DateRange {
  startDate?: string;
  endDate?: string;
}

export const customDateFormat = (date: Date | string | undefined, format = DateFormat.DefaultDashes, utc = true) => {
  if (!date) {
    return '';
  }
  const day = utc ? dayjs.utc(date) : dayjs(date);
  switch (format) {
    case DateFormat.Humanize:
      return day.fromNow();
    default:
      return day.format(format);
  }
};


export const getBackwardWeekDates = (endDate: Date, startDate: Date) => {

  // end Date must be later
  if (endDate <= startDate) {
    return [];
  }

  const arr = [];
  const date = new Date(endDate);
  for (; date >= startDate; date.setDate(date.getDate() - 7)) {
    arr.push(new Date(date));
  }
  return arr;
};

export const isBefore = (value: Date | string, date?: Date | string) => {
  return dayjs(date).isBefore(value);
};

export const isAfter = (value: Date | string, date?: Date | string) => {
  return dayjs(date).isAfter(value);
};

export const getDiffAgo = (date: ConfigType) => {
  const diffMonths = dayjs().diff(date, 'months');
  return diffMonths ? `${diffMonths}mo ago` : `${dayjs().diff(date, 'days')}d ago`;
};

export const hasChange = (date: string | Date | undefined, comparedDate: string | Date | undefined) => {
  if (typeof date === 'object' && typeof comparedDate === 'object') {
    return date.toString() !== comparedDate.toString();
  }
  return date !== comparedDate;
};

const MONTHS = {
  Jan: 'January',
  Feb: 'February',
  Mar: 'March',
  Apr: 'April',
  May: 'May',
  Jun: 'June',
  Jul: 'July',
  Aug: 'August',
  Sep: 'September',
  Oct: 'October',
  Nov: 'November',
  Dec: 'December',
};

export const getMonthFromAbbreviation = (abbreviation: string): string | undefined =>
  MONTHS[abbreviation as keyof typeof MONTHS];

export const getCurrentDateStr = (format = DateFormat.DefaultDashes) => dayjs().format(format);

export const getCurrentDate = () => {
  return new Date();
};

export const getFinancialMonths = () => {
  const months = Object.values(MONTHS).map((month) => month.toLowerCase());
  months.unshift('');
  return months;
};

// If now Feb, return [january, december, november, october]
export const getLastNumberOfMonths = (numberOfMonths = 4) => {
  const months = [];
  for (let number = 1; number <= numberOfMonths; number++) {
    months.push(dayjs().utc().subtract(number, 'months').format(DateFormat.FullMonthName).toLowerCase());
  }
  return months;
};

export const subtractDate = (date: string | Date, value: number, unit: ManipulateType) => {
  return dayjs.utc(date).subtract(value, unit).toDate();
};

export const addDate = (date: string | Date | number, value: number, unit: ManipulateType) => {
  return dayjs.utc(date).add(value, unit).toDate();
};

export const getUTCEndOf = (unit: OpUnitType, date?: DateString) => {
  return dayjs.utc(date).endOf(unit).toDate();
};

export const setUTCEndOf = ({
  year,
  month,
  day = 1,
  unit = 'month',
}: {
  year: number;
  month: number; // 0 indexed
  day?: number;
  unit?: OpUnitType;
}) => {
  return dayjs.utc(Date.UTC(year, month, day)).endOf(unit).toDate();
};

export const getYear = (date?: DateString) => {
  return dayjs.utc(date).year();
};

export const toTimestamp = (date: Date | string) => dayjs.utc(date, undefined, false).unix();
export const timestampToISOString = (date: number) => dayjs.unix(date).toISOString();

/**
 * Notification sent on: (*Deadline is org financial end date plus 4 months)
 *
 * Deadline minus 3 months
 * Deadline minus 1 month
 * Deadline minus 7 days
 */
export const getFinancialEndDateDeadline = (deadline: Date) => {
  const today = dayjs().utc();
  if (dayjs(deadline).utc().subtract(7, 'days').isSame(today, 'day')) {
    return 'seven days';
  }
  if (dayjs(deadline).utc().subtract(1, 'months').isSame(today, 'day')) {
    return 'one month';
  }
  if (dayjs(deadline).utc().subtract(3, 'months').isSame(today, 'day')) {
    return 'three months';
  }
};

type LikeDate = string | Date;
export const isSame = (firstDate: LikeDate, secondDate: LikeDate, unit: ManipulateType) => {
  return dayjs(firstDate).isSame(secondDate, unit);
};

export const getDuration = (diff: number, unit?: ManipulateType) => {
  return dayjs.duration(diff, unit ?? 'milliseconds');
};

export const isBeforeNow = (value: ConfigType) => dayjs(value).isBefore();

export const getDiff = (date1: number, date2: ConfigType) => {
  return dayjs.unix(date1).diff(date2, 'days');
};

export const getDiffInUnit = (date1: ConfigType, date2: ConfigType, unit: ManipulateType) => {
  return dayjs(date1).diff(date2, unit);
};

export const getFutureDate = (duration: number, unit: ManipulateType = 'day') => dayjs().add(duration, unit).toDate();

export const getPastDate = (duration: number, unit: ManipulateType = 'day') =>
  dayjs().subtract(duration, unit).toDate();

export const projectDate = ({
  field,
  startDate,
  endDate,
}: {
  field: 'effectiveDate' | 'created' | 'completedDate';
  startDate: string | Date | undefined;
  endDate: string | Date | undefined;
}) => {
  if (!startDate && !endDate) {
    return undefined;
  }
  return {
    [field]: {
      ...(startDate && { $gte: new Date(startDate) }),
      ...(endDate && { $lte: new Date(endDate) }),
    },
  };
};

export const getStartOfMonth = (date: Date | string) => {
  return dayjs(date).startOf('month').startOf('day').toDate();
};

export const getStartOfDay = (date?: Date | string) => {
  return dayjs(date).startOf('day').toDate();
};

export const getEndOfDay = (date?: Date | string) => {
  return dayjs(date).endOf('day').toDate();
};

export const getEndOfMonth = (date: Date | string) => {
  return dayjs(date).endOf('month').endOf('day').toDate();
};

export enum TimePeriod {
  Hourly = 'hourly',
  Daily = 'daily',
  Weekly = 'weekly',
  Monthly = 'monthly',
  Yearly = 'yearly',
}

export const TimePeriodUnitMapping = {
  [TimePeriod.Hourly]: 'hour',
  [TimePeriod.Daily]: 'day',
  [TimePeriod.Weekly]: 'week',
  [TimePeriod.Monthly]: 'month',
  [TimePeriod.Yearly]: 'year',
} satisfies Record<TimePeriod, OpUnitType>;

export const getTimeRangeByPeriod = (period: TimePeriod) => {
  return {
    startTime: dayjs().utc().startOf(TimePeriodUnitMapping[period]).toDate(),
    endTime: dayjs().utc().endOf(TimePeriodUnitMapping[period]).toDate(),
  };
};

export const revertFormattedToDate = (date: string, format: DateFormat | string): Date => {
  return dayjs(date, format).toDate();
};

export const generateDateMatch = (date: string | Date) => {
  return {
    $gte: dayjs(date).startOf('month').toDate(),
    $lte: dayjs(date).endOf('month').toDate(),
  };
};

export const toDate = (date: string | Date) => {
  return dayjs(date).toDate();
};

export const sortDateFnAsc = (dateA: LikeDate, dateB: LikeDate) => (dateA < dateB ? 1 : dateA > dateB ? -1 : 0);

export const oneDayInSeconds = 3600 * 24;

export const getUnixSeconds = (timeInSeconds: number) => dayjs().unix() + timeInSeconds;
