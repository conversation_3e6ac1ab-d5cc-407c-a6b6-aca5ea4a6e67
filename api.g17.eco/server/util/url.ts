/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import config from "../config";
import { URL } from 'url';
import { wwgLogger } from '../service/wwgLogger';

export const getSubdomain = (url?: string): string | undefined => {
  if (config.appSubdomain) {
    return config.appSubdomain;
  }

  if (!url) {
    return;
  }

  try {
    const urlParts = new URL(url).hostname.split('.')
    return urlParts.slice(0, -2).join('.')
  } catch (e) {
    wwgLogger.error(e);
    return;
  }
}

export const VALID_DOMAIN_REGEX =
  /^(http:\/\/localhost(:\d+)?(\/.*)?|https:\/\/([\w-]+\.)*?(g17\.eco|worldwidegeneration\.co)(:\d+)?(\/.*)?)$/;