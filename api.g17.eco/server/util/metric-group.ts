import { ObjectId } from 'bson';
import { MetricGroupPlain, type MetricGroupWithSubgroups } from '../models/metricGroup';

export const getTagTextByUtrId = (
  utrTagMap: Map<string, string[]> | undefined,
  utrId: ObjectId | string | undefined
) => {
  if (!utrTagMap || !utrId) {
    return '';
  }
  const id = String(utrId);
  const tags = utrTagMap.get(id);
  if (!tags) {
    return '';
  }

  return tags.join(', ');
};

export const buildHierarchy = (metricGroups: MetricGroupPlain[]) => {
  const idToNodeMap = new Map<string, MetricGroupWithSubgroups>(metricGroups.map((item) => [item._id.toString(), item]));
  const result: MetricGroupWithSubgroups[] = [];

  metricGroups.forEach((item) => {
    const parentId = item.parentId;
    if (parentId) {
      const parent = idToNodeMap.get(String(parentId));
      if (parent) {
        if (!parent.subgroups) {
          parent.subgroups = [];
        }
        parent.subgroups.push(item);
      }
    } else {
      // If no parentId, it's a root node
      result.push(item);
    }
  });

  return result;
};
