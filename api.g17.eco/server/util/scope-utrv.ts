import { KeysEnum } from "../models/public/projectionUtils";
import { UniversalTrackerValuePlain } from "../models/universalTrackerValue";
import { ScopeFiltersUtr } from "../service/survey/scope/filterScope";

export type ScopeUtrvProjection = Pick<UniversalTrackerValuePlain, '_id' | 'universalTrackerId' | 'universalTracker'>;
export const scopeUtrvProjection: KeysEnum<ScopeUtrvProjection> = {
  _id: 1,
  universalTrackerId: 1,
  universalTracker: 1,
};
export interface ScopeUtrv extends Pick<ScopeUtrvProjection, '_id' | 'universalTrackerId'> {
  universalTracker: ScopeFiltersUtr;
}

export interface ToFetchUtrScopeUtrv extends Pick<ScopeUtrvProjection, '_id' | 'universalTrackerId'> {
  universalTracker?: ScopeFiltersUtr;
}
