import { InitiativeUtrMin } from '../models/initiativeUniversalTracker';
import { ColumnType, TableColumn, UtrValueType, ValueValidation } from '../models/public/universalTrackerType';
import { UniversalTrackerPlain } from '../models/universalTracker';
import { ObjectId } from 'bson';

export const mergeUtrOverrides = ({
  utr,
  initiativeUtr,
}: {
  utr: Pick<UniversalTrackerPlain, '_id' | 'valueType' | 'valueValidation'>;
  initiativeUtr: InitiativeUtrMin | undefined;
}) => {
  if (!initiativeUtr) {
    return {};
  }
  const overrides: Pick<UniversalTrackerPlain, 'unitInput' | 'numberScaleInput' | 'valueValidation'> = {};
  const { numberScaleInput, unitInput } = initiativeUtr;
  if (unitInput) {
    overrides['unitInput'] = unitInput;
  }
  if (numberScaleInput) {
    overrides['numberScaleInput'] = numberScaleInput;
  }
  const mergedValueValidation = mergeUtrValueValidation({ utr, initiativeUtr });
  if (mergedValueValidation) {
    overrides['valueValidation'] = mergedValueValidation;
  }
  return overrides;
}

export const mergeUtrValueValidation = ({
  utr,
  initiativeUtr,
}: {
  utr: Pick<UniversalTrackerPlain, '_id' | 'valueType' | 'valueValidation'>;
  initiativeUtr: InitiativeUtrMin | undefined;
}) => {
  if (!initiativeUtr) {
    return utr.valueValidation;
  }

  const { valueValidation } = initiativeUtr;

  if (!valueValidation) {
    return utr.valueValidation;
  }

  if (utr.valueType !== UtrValueType.Table) {
    if (valueValidation.decimal === undefined) {
      return utr.valueValidation;
    }
    return { ...utr.valueValidation, decimal: valueValidation.decimal };
  }

  // Deal with table
  const table = utr.valueValidation?.table;
  const overrideColumns = valueValidation.table?.columns;
  if (!table?.columns?.length || !overrideColumns?.length) {
    return utr.valueValidation; // nothing we can do
  }

  return {
    ...utr.valueValidation,
    table: {
      ...table,
      columns: table.columns.map((col) => {
        const overriddenColumn = overrideColumns.find(c => c.code === col.code);
        const { validation, unitInput, numberScaleInput } = overriddenColumn ?? {};
        const newCol = { ...col };
        if (unitInput) {
          newCol.unitInput = unitInput;
        }
        if (numberScaleInput) {
          newCol.numberScaleInput = numberScaleInput;
        }

        if (validation?.decimal !== undefined) {
          newCol.validation = { ...col.validation, decimal: validation?.decimal };
        }
        return newCol;
      }),
    }
  };
};

export const hasMinAndMax = (
  valueValidation: UniversalTrackerPlain['valueValidation']
): valueValidation is { min: number; max: number } => {
  return typeof valueValidation?.min === 'number' && typeof valueValidation?.max === 'number';
};


export const isSimpleNumericColumnType = (column: Pick<TableColumn, 'type' | 'calculation'>) => {
  return [ColumnType.Number, ColumnType.Percentage].includes(column.type as ColumnType) && !column.calculation?.formula;
};

export const isPercentageColumn = (column: Pick<TableColumn, 'type' | 'validation'>) => {
  const { type, validation } = column;

  if (type === UtrValueType.Percentage) {
    return true;
  }
  return type === UtrValueType.Number && validation?.max === 100 && validation?.min === 0;
};

export const isSingleRowTable = <T extends string | ObjectId = string>(utr: Pick<UniversalTrackerPlain<T>, 'valueType' | 'valueValidation'>) => {
  return utr.valueType === UtrValueType.Table && utr.valueValidation?.table?.validation?.maxRows === 1;
};

export const isTextTableColumnType = (type: ColumnType | string | undefined) => {
  return type === ColumnType.Text;
};

export const hasTextColumnType = (valueValidation: ValueValidation | undefined) => {
  const columns = valueValidation?.table?.columns;
  return columns ? columns.some((column) => isTextTableColumnType(column?.type)) : false;
};

export function hasTextValue<T extends string | ObjectId = string>(
  utr: Pick<UniversalTrackerPlain<T>, 'valueType' | 'valueValidation'>,
) {
  switch (utr.valueType) {
    case UtrValueType.Text:
    case UtrValueType.TextValueList:
      return true;
    case UtrValueType.Table:
      return isSingleRowTable(utr) && hasTextColumnType(utr.valueValidation);
    default:
      return false;
  }
}
