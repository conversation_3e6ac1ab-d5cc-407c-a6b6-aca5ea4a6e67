import { ObjectId } from 'bson';
import { RootInitiativeData } from "../repository/InitiativeRepository";

interface Item {
  _id: ObjectId;
  parentId?: ObjectId;
  children?: Item[];
}

function getChildren(items: Item[], parentId: ObjectId): Item[] {
  return items
    .filter((item) => item.parentId && item.parentId.equals(parentId))
    .map((child) => ({ ...child, children: getChildren(items, child._id) }));
}

export function arrayToTree<T extends Item = Item>(items: T[], rootId: ObjectId): T[] {
  const roots = items.filter((item) => item._id.equals(rootId));

  return roots.map((root) => ({ ...root, children: getChildren(items, root._id) }));
}

export function flattenTree(items: Item[]): Item[] {
  let children: Item[] = [];
  items.forEach((item) => {
    if (item.children?.length) {
      children = [...children, ...item.children];
    }
  });

  return items.concat(children.length ? flattenTree(children) : children);
}

export interface InitiativeWithParent {
  _id: ObjectId;
  name: string;
  parentId?: ObjectId;
  parentNames: string[];
}

export interface TreeNodeType extends InitiativeWithParent {
  id: string;
  parentIdString?: string;
  parent?: TreeNodeType;
}

export type InitiativeParent = Pick<InitiativeWithParent, '_id' | 'name' | 'parentId' >;

export interface InitiativeWithCombinedParents {
  _id: ObjectId;
  name: string;
  parentId?: ObjectId;
  parents: InitiativeParent[];
}

export const createWithParents = (initiative: RootInitiativeData, parentMap: Map<string, InitiativeParent>) => {
  const currentInitiative: InitiativeWithCombinedParents = {
    _id: initiative._id,
    name: initiative.name,
    parentId: initiative.parentId,
    parents: [] as InitiativeParent[],
  }

  let parent = initiative.parentId ? parentMap.get(initiative.parentId.toString()) : undefined;
  while (parent) {
    currentInitiative.parents.push({
      _id: parent._id,
      name: parent.name,
      parentId: parent.parentId,
    });
    parent = parent.parentId ? parentMap.get(parent.parentId.toString()) : undefined;
  }
  return currentInitiative;
}
