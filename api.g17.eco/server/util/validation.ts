import { ErrorMapCtx, ZodError, ZodErrorMap, ZodIssueCode, ZodIssueOptionalMessage, ZodObject, ZodRawShape } from 'zod';
import BadRequestError from '../error/BadRequestError';

/**
 * Generates a custom error map for ZodError objects.
 *
 * @param {ZodError} error - The ZodError object to generate the error map for.
 * @param {object} ctx - The context object containing the default error message.
 * @return {object} The error map object with a custom message or the default error message.
 * 
 * More details: https://zod.dev/ERROR_HANDLING?id=a-working-example
 */
const customErrorMap: ZodErrorMap = (
  error: ZodIssueOptionalMessage,
  ctx: ErrorMapCtx
): {
  message: string;
} => {
  /*
  This is where you override the various error codes
  */
  switch (error.code) {
    case ZodIssueCode.invalid_type:
      return { message: `${error.path.join(', ')} is not ${error.expected}, received ${error.received}` };
    default:
      // fall back to default message!
      return { message: ctx.defaultError };
  }
};

export function mustValidate<T extends ZodRawShape>(data: any, schema: ZodObject<T>) {
  try {
    return schema.parse(data, { errorMap: customErrorMap });
  } catch (err) {
    throw new BadRequestError(
      err instanceof ZodError ? err.errors.map((error) => error.message).join(', ') : 'Invalid data'
    );
  }
}
