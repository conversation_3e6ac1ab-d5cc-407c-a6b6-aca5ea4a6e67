/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

// Load env variables first
const result = require('dotenv').config({ path: process.env.G17_API_ENV_FILE });
if (process.env.G17_API_ENV_FILE && result.error) {
  throw result.error;
}

import { initTracing, setupErrorMiddleware, shouldHandleError } from './telemetry/tracing';
initTracing();

import { connectDb } from './service/db/Db';
import express from 'express';
import cors from 'cors';
import config from './config';
import bodyParser from 'body-parser';
import compression from 'compression';
import { sendErrorResponse } from './error/handleError';
import { wwgLogger } from './service/wwgLogger';
import { type UserModel } from './models/user';

import { type InitiativePlain } from './models/initiative';
const nocache = require('nocache');
import { fileUploadErrorMiddleware } from './http/FileUpload';
import { HttpErrorMessages } from './error/ErrorMessages';
import { appEventSetup } from './service/event/AppEventSetup';
import { type OrganizationPlain } from './models/organization';
import { type PublicAuth } from './routes/public/AuthTypes';
import { redisCache } from './service/cache';
import { extractVersion } from './util/string';

const morgan = require('morgan');
const app = express();

// Setup events
const emitter = appEventSetup();
wwgLogger.info('AppEventEmitter setup completed', {
  eventNames: emitter.eventNames(),
});

app.set('trust proxy', true);

// Restore Express v4 query parser behavior
app.set('query parser', 'extended');

app.use(morgan(':date[clf] :method :url :status :response-time ms - :res[content-length]'));
app.use(compression()); // compress all responses
app.use(nocache()); // prevents IE11 from caching api requests

app.use(
  bodyParser.json({
    limit: '50mb',
    // We need the raw body to verify webhook signatures.
    // Let's compute it only when hitting the Stripe webhook endpoint.
    verify: function (req: any, res, buf) {
      if (req.originalUrl?.startsWith('/api/inbound/stripe/webhook')) {
        req.rawBody = buf.toString();
      }
    },
  })
);

// parse application/x-www-form-urlencoded
// for easier testing with Postman or plain HTML forms
app.use(bodyParser.urlencoded({
  limit: '50mb',
  extended: true
}));

// Backward compatibility with express 4.
app.use((req, res, next) => {
  if (
    !req.headers['content-type']?.startsWith('multipart/form-data') &&
    ['POST', 'PUT', 'PATCH', 'DELETE'].includes(req.method) &&
    req.body === undefined
  ) {
    wwgLogger.warn(`Empty body for ${req.method} ${req.originalUrl}`, {
      headers: { 'content-type': req.headers['content-type'] },
    });
    req.body = {};
  }
  next();
});

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Express {
    interface Response {
      FromModel: Function;
      /** @deprecated Use next(err) instead */
      Exception: (e: Error | string) => void;
      /** @deprecated Use Exception instead */
      Invalid: Function;
      Success: Function;
      NotPermitted: (msg?: string) => void;
    }

    interface User extends UserModel {
      email: string;
    }

    interface Request {
      initiative?: InitiativePlain;
      organization?: OrganizationPlain;
      user?: User;
      tokenData?: any;
      rawBody?: string;

      auth?: PublicAuth
    }
  }
}

app.response.FromModel = function (data: any) {
  this.json({ success: !!data, data: data, message: (data ? undefined : 'No results') });
};
app.response.Invalid = function (message: string = '') {
  this.json({ success: false, message: message });
};
app.response.Success = function (message: string = '') {
  this.json({ success: true, message: message });
};
app.response.NotPermitted = function (message?: string) {
  this.status(403);
  this.json({ success: false, message: message || HttpErrorMessages.PermissionDenied });
};

app.disable('x-powered-by');
app.use(
  cors({
    exposedHeaders: ['Content-Disposition'],
  })
);

// detect mismatch between server version and client version
app.use((req, res, next) => {
  if (!config.isProduction) {
    return next();
  }

  const clientReleaseName = req.header('X-Client-Version');
  if (!clientReleaseName) {
    return next();
  }

  const clientVersion = extractVersion(clientReleaseName);
  const serverVersion = extractVersion(config.release.release);

  if (clientVersion !== serverVersion) {
    wwgLogger.warn(`Client version ${clientVersion} does not match server version ${serverVersion}`);
  }

  next();
});

app.use('/api', require('./routes'));
app.use('/v1', require('./routes/public/v1'));

// Last handler, after all routes did not match, must be 404
app.use((req, res) => {
  res.status(404).json({ success: false, message: 'Not Found' })
})


// Database connection
const connected = connectDb();

// Redis cache
redisCache.connect().catch((e: Error) => {
  wwgLogger.error(e);
})

// The error handler must be before any other error middleware and after all controllers
setupErrorMiddleware(app, { shouldHandleError });

app.use(fileUploadErrorMiddleware)

// error handler, no stack traces leaked to user
app.use((err: Error, req: express.Request, res: express.Response) => {
  sendErrorResponse(err, res, req);
});


app.listen(Number(config.port), config.hostname, () => {
  connected.then(conn => {
    console.log(`DB Connection to ${conn.connection?.host ?? '-'}:${conn.connection?.port ?? '-'} is ready`);
    if (typeof process.send === 'function') {
      process.send('ready');
    }
  });
  const modeMsg = `Running in ${process.env.NODE_ENV || 'development'}, appEnv: ${config.appEnv}`;
  return console.log(`Listening on ${config.hostname} port ${config.port}. ${modeMsg}`);
});
