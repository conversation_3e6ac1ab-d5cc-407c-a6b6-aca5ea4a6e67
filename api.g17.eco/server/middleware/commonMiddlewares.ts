/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { type RequestHandler } from 'express';
import { type UserModel } from '../models/user';
import UniversalTrackerValue from '../models/universalTrackerValue';
import { InitiativeRepository } from '../repository/InitiativeRepository';
import { ObjectId } from 'bson';
import PermissionDeniedError from '../error/PermissionDeniedError';
import { UserInitiativeRepository } from '../repository/UserInitiativeRepository';
import { UtrvPermissions, type UtrvPermissionUtrv } from '../service/utr/UtrvPermissions';
import { RequesterType } from '../models/dataShare';
import { DataShareRepository } from '../repository/DataShareRepository';
import { InitiativePermissions } from '../service/initiative/InitiativePermissions';
import { DataSharePermissions } from '../service/share/DataSharePermissions';
import type { KeysEnum } from '../models/public/projectionUtils';

export const bodyParamIdCheck: RequestHandler = (req, res, next) => {
  if (!req.body._id || req.params.id !== req.body._id) {
    return res.Invalid('POST url id (' + req.params.id + ') does not match POST body _id (' + req.body._id + ')');
  }
  next();
};

export const populateInitiative: RequestHandler = (req, res, next) => {
  if (!req.user) {
    return next(new PermissionDeniedError())
  }
  UserInitiativeRepository.getUserInitiative(req.user, req.params.initiativeId)
    .then((initiativeDoc) => {
      if (!initiativeDoc) {
        return next(new PermissionDeniedError())
      }
      req.initiative = initiativeDoc;
      next();
    }).catch((e: Error) => next(e));
};

export const populatePortfolioDataShareInitiative: RequestHandler = (req, res, next) => {
  if (!req.user) {
    return next(new PermissionDeniedError())
  }

  const { portfolioId, initiativeId } = req.params;

  DataSharePermissions.canAccessInitiative(RequesterType.Portfolio, portfolioId, initiativeId)
    .then(hasAccess => {
      if (!hasAccess) {
        throw new PermissionDeniedError();
      }
    })
    .then(() => {
      return DataShareRepository.getInitiative(RequesterType.Portfolio, portfolioId, initiativeId)
    })
    .then((initiativeDoc) => {
      if (!initiativeDoc) {
        return next(new PermissionDeniedError())
      }
      req.initiative = initiativeDoc;
      next();
    }).catch((e: Error) => next(e));
}

export const checkPublicPermissions: RequestHandler = async (req, res, next) => {
  if (!req.user) {
    return next(new PermissionDeniedError())
  }

  const initiativeId = req.params.initiativeId;
  if (!initiativeId || !ObjectId.isValid(initiativeId)) {
    return next(new PermissionDeniedError(`PublicPermissions: InitiativeId is not valid: '${initiativeId}'`))
  }

  if (!await InitiativePermissions.canAccess(req.user, initiativeId)) {
    if (!await DataSharePermissions.canUserAccessInitiative(req.user, initiativeId)) {
      return next(new PermissionDeniedError());
    }
  }

  InitiativeRepository.getInitiativeById(initiativeId).then((initiativeDoc) => {
    if (!initiativeDoc) {
      return next(new PermissionDeniedError())
    }
    req.initiative = initiativeDoc;
    next();
  }).catch((e: Error) => next(e));
};

export const populateInitiativeByBody: RequestHandler = (req, res, next) => {
  if (!req.user) {
    res.NotPermitted()
    return;
  }
  const user: UserModel = req.user;
  UserInitiativeRepository.getUserInitiative(user, req.body.initiativeId)
    .then((initiativeDoc) => {
      if (!initiativeDoc) {
        res.NotPermitted();
        return;
      }
      req.initiative = initiativeDoc;
      next();
    }).catch((e: Error) => next(e));
};

export const checkUniversalTrackerPermissions: RequestHandler = (req, res, next) => {
  if (!req.user) {
    res.NotPermitted()
    return;
  }
  const initiativeId = new ObjectId(req.params.initiativeId);
  const user: UserModel = req.user;
  UserInitiativeRepository.getUserInitiative(req.user, initiativeId)
    .then(async (initiativeDoc) => {
      if (initiativeDoc) {
        // All good and permitted
        next();
        return;
      }

      const universalTrackerId = new ObjectId(req.params.universalTrackerId);
      const utrv = await UniversalTrackerValue.findOne({ initiativeId, universalTrackerId }, {
        _id: 1,
        initiativeId: 1,
        universalTrackerId: 1,
        stakeholders: 1,
        compositeData: 1,
      } satisfies KeysEnum<UtrvPermissionUtrv>)
        .lean<UtrvPermissionUtrv>()
        .exec();

      if (utrv && (await UtrvPermissions.canAccess(utrv, user))) {
        // All good and permitted
        return next();
      }

      // Can access from assurance? Need to Figure out faster
      if (await UtrvPermissions.hasAssurerAccessToUtrData(user, initiativeId, universalTrackerId)) {
        return next();
      }

      return res.NotPermitted();

    }).catch((e: Error) => next(e));
};

export const checkStakeholderType: RequestHandler = (req, res, next) => {
  const type: string = req.params.type || req.params.role;
  if (!['stakeholder', 'verifier', 'escalation'].includes(type)) {
    return res.Invalid('Permitted Stakeholder Types: stakeholder | verifier | escalation');
  }
  next();
};
