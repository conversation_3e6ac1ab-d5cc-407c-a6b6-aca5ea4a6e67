/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import express from 'express';
import { getBluePrintContribution } from '../service/survey/BlueprintContribution';
import { type Blueprints } from '../survey/blueprints';

const router = express.Router();
const bc = getBluePrintContribution();

router.route('/:code')
  .get(async (req, res) => {
    const model = await bc.getContributions(req.params.code as Blueprints);
    res.FromModel(model);
  });

router.route('/:code/questions')
  .get(async (req, res) => {
    const model = await bc.getQuestions(req.params.code as Blueprints);
    res.FromModel(model);
  });

module.exports = router;
