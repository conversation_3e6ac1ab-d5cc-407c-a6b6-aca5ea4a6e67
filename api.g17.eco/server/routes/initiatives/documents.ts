/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import { DocumentOwnerType } from '../../models/document';
import { getDocumentService } from '../../service/file/DocumentService';
import FileUpload from '../../http/FileUpload';
import UserError from '../../error/UserError';
import { type AuthRouter } from '../../http/AuthRouter';
import { ContextMiddleware } from '../../middleware/audit/contextMiddleware';
import { mustValidate } from '../../util/validation';
import {
  uploadDocumentSchema,
  bulkUploadDocumentSchema,
  getDocumentsSchema,
  updateDisplayDocumentsSchema,
} from '../validation-schemas/documents';
import { canAccessInitiative, canManageInitiative, isInitiativeExists } from '../../middleware/initiativeMiddlewares';
import { getResultsFromPromiseAllSettled } from '../../util/promise';
import { wwgLogger } from '../../service/wwgLogger';
import { getInitiativeSettingsService } from '../../service/initiative/InitiativeSettingsService';
import { AssuranceRepository } from '../../repository/AssuranceRepository';

const router = express.Router({ mergeParams: true }) as AuthRouter;
router.use(ContextMiddleware);

const documentService = getDocumentService();
const initiativeSettingsService = getInitiativeSettingsService();

router
  .route('/')
  .get(canAccessInitiative, async (req, res) => {
    const initiativeId = req.params.initiativeId;
    const { startDate, endDate, cursor, limit, mediaTypes, ownerSubType, searchText } = mustValidate(
      req.query,
      getDocumentsSchema
    );
    const documents = await documentService.handleGetDocumentsWithUrl(initiativeId, {
      startDate,
      endDate,
      cursor,
      limit,
      mediaTypes,
      ownerSubType,
      searchText,
    });
    return res.FromModel(documents);
  })
  .post(isInitiativeExists, canManageInitiative, FileUpload.any(), async (req, res) => {
    const data = mustValidate(req.body, uploadDocumentSchema);
    const initiative = res.locals.initiative;
    const files: any = req.files;
    if (!Array.isArray(files) || files.length === 0) {
      throw new UserError('There are no file to be uploaded');
    }
    const result = await Promise.allSettled(
      files.map((file) =>
        documentService.handleDocumentUpload(
          { ...data, ownerId: initiative._id, ownerType: DocumentOwnerType.Initiative },
          file,
          req.user
        )
      )
    );

    const { fulfilled, rejected } = getResultsFromPromiseAllSettled(result);
    if (rejected.length > 0) {
      wwgLogger.error(`Cannot upload documents for initiative ${initiative._id}`, rejected);
    }

    return res.FromModel({ fulfilled, rejected });
  })
  .patch(isInitiativeExists, canManageInitiative, async (req, res) => {
    const data = mustValidate(req.body, bulkUploadDocumentSchema);
    const documentIds = data.documentIds;
    const result = await documentService.handleBulkDocumentEdit(documentIds, {
      ...data,
      ownerType: DocumentOwnerType.Initiative,
    });

    return res.FromModel(result);
  });

router
  .route('/insight-display')
  .get(canAccessInitiative, async (req, res) => {
    const initiativeId = res.locals.initiativeId;
    const displayDocuments = await initiativeSettingsService.getDisplayDocumentsMap(initiativeId);
    // [GU-6229] Assurance docs are not part of displayDocuments setting for now
    const { documents: displayAssuranceDocuments } = await AssuranceRepository.getAssuranceDocuments(initiativeId);

    return res.FromModel({
      ...displayDocuments,
      assurance: displayAssuranceDocuments,
    });
  })
  .patch(isInitiativeExists, canManageInitiative, async (req, res) => {
    const initiativeId = res.locals.initiativeId;
    const documentChanges = mustValidate(req.body, updateDisplayDocumentsSchema);
    const result = await initiativeSettingsService.handleUpdateDisplayDocuments(initiativeId, documentChanges);
    return res.FromModel(result);
  });

router
  .route('/:documentId')
  .patch(isInitiativeExists, canManageInitiative, async (req, res) => {
    const data = mustValidate(req.body, uploadDocumentSchema);
    const documentId = req.params.documentId;
    const result = await documentService.handleDocumentEdit(documentId, {
      ...data,
      ownerType: DocumentOwnerType.Initiative,
    });

    return res.FromModel(result);
  })
  .delete(isInitiativeExists, canManageInitiative, async (req, res) => {
    const documentId = req.params.documentId;
    const initiative = res.locals.initiative; // Parsed by middleware
    const result = await documentService.handleDeleteDocument({ documentId, initiative });

    return res.FromModel(result);
  });

module.exports = router;
