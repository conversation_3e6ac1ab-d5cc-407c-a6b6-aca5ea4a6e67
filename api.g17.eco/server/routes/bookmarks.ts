import express from 'express';
import { type AuthRouter } from '../http/AuthRouter';
import { ObjectId } from 'bson';
import { BookmarkRepository } from '../repository/BookmarkRepository';

const router = express.Router() as AuthRouter;

router.route('/universal-tracker-value/survey/:surveyId').get(async (req, res) => {
  const userId = req.user._id;
  const surveyId = new ObjectId(req.params.surveyId);
  return res.FromModel(await BookmarkRepository.findUtrvBookmarksBySurvey(userId, surveyId));
});

router
  .route('/universal-tracker-value')
  .post(async (req, res) => {
    const userId = req.user._id;
    const utrvId = new ObjectId(req.body.utrvId);
    const surveyId = new ObjectId(req.body.surveyId);
    return res.FromModel(await BookmarkRepository.createUtrvBookmark(userId, utrvId, surveyId));
  })
  .delete(async (req, res) => {
    const userId = req.user._id;
    const utrvId = new ObjectId(req.body.utrvId);
    const surveyId = new ObjectId(req.body.surveyId);
    return res.FromModel(await BookmarkRepository.deleteUtrvBookmark(userId, utrvId, surveyId));
  });

module.exports = router;
