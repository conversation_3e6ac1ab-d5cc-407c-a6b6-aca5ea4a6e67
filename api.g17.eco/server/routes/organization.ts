/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import express from 'express';
import { frameworks, standards } from '@g17eco/core';
import { canManageInitiative } from '../middleware/initiativeMiddlewares';
import Initiative, { InitiativeTypes } from '../models/initiative';
import { InitiativeRepository } from '../repository/InitiativeRepository';
import { getSurveyScopeService } from '../service/organization/SurveyScopeService';
import { OrganizationRepository } from '../repository/OrganizationRepository';
import { type OrganizationPermissionsMin } from '../models/organization';
import { type AuthRouter } from '../http/AuthRouter';
import { mustValidate } from '../util/validation';
import { userOnboardingSchema, userPermissionUpdateSchema } from './validation-schemas/assurance-tracker';
import { getOrganizationManager } from '../service/assurance/OrganizationManager';
import { canManageOrganization } from '../middleware/organizationMiddlewares';
import { wwgLogger } from '../service/wwgLogger';
import UserError from '../error/UserError';
import User from '../models/user';

const router = express.Router() as AuthRouter;
const organizationManager = getOrganizationManager();

router.route('/')
  .get(async (req, res) => {
    const result = await OrganizationRepository.getOrganizationByTypes(req.query.types as string[], true);
    res.FromModel(result);
  });

router.route('/:organizationId/users/onboard')
  .post(canManageOrganization, async (req, res, next) => {
    try {
      const organization = res.locals.organization as OrganizationPermissionsMin;
      const { emails, permissions } = mustValidate(req.body, userOnboardingSchema);
      // Onboarding emails may contain current user email, and we don't allow modifying it here
      // Need to exclude the current user from the list of onboarding emails
      const eligibleEmails = emails.filter(email => req.user.email !== email);

      if (emails.includes(req.user.email)) {
        wwgLogger.error(new UserError('Input emails contain current user email', {
          organizationId: organization._id.toString(),
          userId: req.user._id.toString(),
          emails
        }));
      }

      const result = await organizationManager.onboardEmails({
        organization,
        permissions,
        emails: eligibleEmails,
        delegator: req.user,
      });

      return res.FromModel(result);
    } catch (error) {
      next(error);
    }
  });

router.route('/:organizationId/users/:userId/permissions')
  .patch(canManageOrganization, async (req, res, next) => {
    try {
      const organization = res.locals.organization as OrganizationPermissionsMin;
      const userId = req.params.userId;
      const { permissions } = mustValidate(req.body, userPermissionUpdateSchema);
      const user = req.user._id.equals(userId)
        ? req.user
        : await User.findOne({ _id: userId, organizationId: organization._id }).orFail().exec();

      const organizationPermissions = await organizationManager.updateOrganizationPermissions({
        organization,
        overridePermissions: permissions,
        user,
      });

      return res.FromModel(organizationPermissions);
    } catch (error) {
      next(error);
    }
  });

// TODO: Move this to initiative routes
router.route('/:initiativeId/metric-groups').get(canManageInitiative, async (req, res, next) => {
  try {
    const initiativeId = res.locals.initiativeId as string;
    const initiative = await Initiative.findById(initiativeId, { code: 1, type: 1 }).orFail().exec();
    // portfolio
    if (initiative.type === InitiativeTypes.Group) {
      const metrics = await InitiativeRepository.getInitiativeKpiGroupsSingle(initiative);
      return res.FromModel(metrics);
    }
    // getFullTree
    const metrics = await InitiativeRepository.getOrganizationKpiGroups(initiativeId);
    res.FromModel(metrics);
  } catch (e) {
    return next(e);
  }
});

// TODO: Move this to initiative routes
router.route('/:initiativeId/scopes/used').get(canManageInitiative, async (req, res, next) => {
  try {
    const initiativeId = res.locals.initiativeId as string;
    const initiative = await Initiative.findById(initiativeId, { code: 1, type: 1 }).orFail().lean().exec();
    // portfolio
    if (initiative.type === InitiativeTypes.Group) {
      return res.FromModel({
        standards: Object.keys(standards).reduce((acc, s) => {
          acc[s] = (standards[s].subgroups ?? []).map((sub) => sub.code);
          return acc;
        }, {} as { [key: string]: string[] }),
        frameworks: Object.keys(frameworks).reduce((acc, f) => {
          acc[f] = (frameworks[f].subgroups ?? []).map((sub) => sub.code);
          return acc;
        }, {} as { [key: string]: string[] }),
      });
    }

    const usedScopes = await getSurveyScopeService().getUsedScopes(initiativeId);
    res.FromModel(usedScopes);
  }
  catch (e) {
    return next(e);
  }
});

module.exports = router;
