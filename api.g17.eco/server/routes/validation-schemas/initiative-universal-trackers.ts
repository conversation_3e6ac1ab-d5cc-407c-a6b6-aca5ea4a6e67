import { SupportedMeasureUnits } from '../../service/units/unitTypes';
import { z } from 'zod';
import { idSchema, refineIdSchema } from './common';
import { UTRV_CONFIG_CODES, UtrvConfigValue } from '../../models/initiativeUniversalTracker';
import {
  ColumnValueAggregation,
  ValueAggregation,
  type Variation,
  VariationDataSource,
  VariationType,
} from '../../models/public/universalTrackerType';
import { isNumeric } from '../../util/number';

const unitConfigSchema = z
  .object({
    unitLocked: z.boolean().optional(),
    numberScaleLocked: z.boolean().optional(),
  })
  .catchall(z.string().optional()) // Allow any SupportedMeasureUnits as optional strings
  .refine(
    (data) => {
      // Validate that only SupportedMeasureUnits keys are added as strings or are undefined
      return Object.keys(data).every(
        (key) =>
          key === 'unitLocked' ||
          key === 'numberScaleLocked' ||
          SupportedMeasureUnits[key as keyof typeof SupportedMeasureUnits] !== undefined
      );
    },
    {
      message: 'Invalid key in unitConfig. Only SupportedMeasureUnits or lock properties are allowed.',
    }
  );

const variationSchema = z
  .object({
    isEnforced: z.boolean(),
    value: z.number().optional(),
    dataSource: z.nativeEnum(VariationDataSource).optional(),
    confirmationRequired: z.boolean().optional(),
    type: z.nativeEnum(VariationType).optional(),
  })
  .superRefine((data, ctx) => {
    if (!data.isEnforced) {
      return;
    }

    if (data.value === undefined) {
      ctx.addIssue({
        code: z.ZodIssueCode.invalid_type,
        expected: 'number',
        received: 'undefined',
        message: 'value is required',
      });
    }

    if (!data.type || data.type === VariationType.Percentage) {
      if (isNumeric(data.value) && (data.value < 0 || data.value > 100)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'value must be between 0 and 100',
        });
      }

      if (data.dataSource === undefined) {
        ctx.addIssue({
          code: z.ZodIssueCode.invalid_type,
          expected: 'string',
          received: 'undefined',
          message: 'dataSource is required',
        });
      }
    }

    if (data.confirmationRequired === undefined) {
      ctx.addIssue({
        code: z.ZodIssueCode.invalid_type,
        expected: 'boolean',
        received: 'undefined',
        message: 'confirmationRequired is required',
      });
    }
  })
  .transform((data) => {
    const { isEnforced, dataSource, confirmationRequired } = data;
    if (!isEnforced) {
      return null;
    }
    const value = isNumeric(data.value) ? data.value : 0;
    const variation = { min: value, max: value, dataSource, confirmationRequired, type: VariationType.Percentage };
    // need to cast here as TypeScript can't infer these properties are defined even after superRefine() checks.
    return [variation] as Variation[];
  });

export const inputOverrideSchema = z.object({
  decimal: z.record(z.string(), z.union([z.number().min(0).max(5), z.null()])).optional(),
  unitConfig: z.record(z.string(), unitConfigSchema).optional(),
  variation: z.record(z.string(), variationSchema).optional(),
  utrIds: z.array(idSchema),
});

export const utrvConfigOverrideSchema = z.object({
  utrvConfig: z.record(z.enum(UTRV_CONFIG_CODES), z.nativeEnum(UtrvConfigValue)),
  utrIds: z.array(idSchema),
});

export const aggregationOverrideSchema = z.object({
  aggregationConfig: z
    .object({
      modes: z
        .object({
          children: z
            .object({
              valueAggregation: z.union([z.nativeEnum(ValueAggregation), z.literal('default')]),
            })
            .optional(),
          combined: z
            .object({
              valueAggregation: z.union([z.nativeEnum(ValueAggregation), z.literal('default')]),
            })
            .optional(),
        })
        // Normalize 'default' to undefined
        .transform((data) => ({
          children:
            !data.children || data.children.valueAggregation === 'default'
              ? undefined
              : {
                  valueAggregation: data.children.valueAggregation,
                },
          combined:
            !data.combined || data.combined.valueAggregation === 'default'
              ? undefined
              : {
                  valueAggregation: data.combined.valueAggregation,
                },
        })),
    })
    .optional(),
  valueValidation: z
    .object({
      table: z
        .object({
          columns: z.array(
            z.object({
              code: z.string(),
              type: z.string(),
              name: z.string(),
              aggregationConfig: z
                .object({
                  modes: z.object({
                    children: z
                      .object({
                        valueAggregation: z.union([z.nativeEnum(ColumnValueAggregation), z.literal('default')]),
                      })
                      .optional(),
                    combined: z
                      .object({
                        valueAggregation: z.union([z.nativeEnum(ColumnValueAggregation), z.literal('default')]),
                      })
                      .optional(),
                  }),
                })
                .optional(),
            })
          ),
        })
        .optional(),
    })
    .optional()
    // Normalize 'default' to undefined
    // This is needed as FE sends 'default' when no value is selected, but we store undefined in the DB
    .transform((data) => ({
      table: data?.table
        ? {
            columns: data.table.columns.map((column) => ({
              ...column,
              aggregationConfig: column.aggregationConfig
                ? {
                    modes: {
                      children:
                        !column.aggregationConfig.modes.children?.valueAggregation ||
                        column.aggregationConfig.modes.children.valueAggregation === 'default'
                          ? undefined
                          : { valueAggregation: column.aggregationConfig.modes.children.valueAggregation },
                      combined:
                        !column.aggregationConfig.modes.combined?.valueAggregation ||
                        column.aggregationConfig.modes.combined.valueAggregation === 'default'
                          ? undefined
                          : { valueAggregation: column.aggregationConfig.modes.combined.valueAggregation },
                    },
                  }
                : undefined,
            })),
          }
        : undefined,
    })),
  utrId: refineIdSchema('utrId'),
});
