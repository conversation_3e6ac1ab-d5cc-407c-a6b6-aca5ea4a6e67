/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import { getAssuranceManager } from '../service/assurance/AssuranceManager';
import FileUpload from '../http/FileUpload';
import { AssuranceRepository } from '../repository/AssuranceRepository';
import Organization from '../models/organization';
import { createAssuranceQuestionManager } from '../service/assurance/AssuranceQuestionManager';
import { AssurancePermissions } from '../service/assurance/AssurancePortfolioPermissions';
import { SurveyPermissions } from '../service/survey/SurveyPermissions';
import { type AuthRouter } from '../http/AuthRouter';
import { UserInitiativeRepository } from '../repository/UserInitiativeRepository';
import { SurveyRepository } from '../repository/SurveyRepository';
import PermissionDeniedError from '../error/PermissionDeniedError';
import { type AssurancePortfolioPlain, AssurancePortfolioStatus } from '../service/assurance/model/AssurancePortfolio';
import UserError from '../error/UserError';
import { ContextMiddleware } from '../middleware/audit/contextMiddleware';
import { ObjectId } from 'bson';
import { UserRepository } from '../repository/UserRepository';
import { canManageAssurancePortfolio, canViewAssurancePortfolio } from '../middleware/assurancePortfolioMiddlewares';
import { getOrganizationOnboardingRepository } from '../repository/OrganizationOnboardingRepository';

const assuranceManager = getAssuranceManager();
const assuranceQuestionManager = createAssuranceQuestionManager();
const organizationOnboardingRepo = getOrganizationOnboardingRepository();

const router = express.Router() as AuthRouter;

router.route('/portfolio')
  .post(ContextMiddleware, FileUpload.any(), async (req, res) => {
    const initiative = await UserInitiativeRepository.getUserInitiative(req.user, req.body.initiativeId);
    if (!initiative) {
      return res.NotPermitted();
    }
    const assurance = await assuranceManager.createPortfolio(req.body, req.user, req.files);
    res.FromModel(assurance);
  });

router.route('/portfolio/survey/:surveyId')
  .get(async (req, res) => {
    const survey = await SurveyRepository.mustFindById(req.params.surveyId);
    const hasPermissions = await SurveyPermissions.canManage(survey, req.user);
    const assurances = await AssuranceRepository.getAssuranceExpandedExtraByInitiativeId(survey.initiativeId, survey._id, req.user._id, hasPermissions);
    res.FromModel({ survey, assurances });
  });

router.route('/portfolio/:id/download')
  .get(async (req, res) => {
    const portfolio = await AssurancePermissions.getPortfolio(req.params.id, req.user);
    const result = await assuranceManager.download(portfolio, req.user);
    res.FromModel(result);
  });

router.route('/portfolio/:id/questions')
  .get(async (req, res) => {
    const portfolio = await AssurancePermissions.getPortfolio(req.params.id, req.user);
    const result = await assuranceQuestionManager.getSelection(portfolio);
    res.FromModel(result);
  })
  .post(async (req, res) => {
    const portfolio = await AssurancePermissions.getPortfolio(req.params.id, req.user);
    const result = await assuranceManager.updateQuestions(portfolio, req.body.questions, req.user);
    res.FromModel(result);
  });

router.route('/portfolio/:id/questions/delegate')
  .post(async (req, res) => {
    const portfolio = await AssurancePermissions.getPortfolio(req.params.id, req.user);
    const result = await assuranceQuestionManager.getSelectedDelegators(portfolio, req.body, req.user);
    res.FromModel(result);
  })
  .patch(canManageAssurancePortfolio, async (req, res) => {
    const portfolio = res.locals.assurancePortfolio as AssurancePortfolioPlain;
    const result = await assuranceQuestionManager.updateQuestionPermissions(portfolio, req.body, req.user);
    res.FromModel(result);
  });

router.route('/portfolio/:id/questions/:questionId/download')
  .get(canViewAssurancePortfolio, async (req, res) => {
    const portfolio = await AssurancePermissions.getPortfolio(req.params.id, req.user);
    const result = await assuranceQuestionManager.download(portfolio, req.params.questionId, req.user);
    res.FromModel(result);
  });

router.route('/portfolio/:id/download/multiple')
  .post(async (req, res) => {
    const portfolio = await AssurancePermissions.getPortfolio(req.params.id, req.user);
    const result = await assuranceManager.download(portfolio, req.user, req.body.questionIds);
    res.FromModel(result);
  });


router.route('/portfolio/:id/ready')
  .patch(FileUpload.any(), ContextMiddleware, async (req, res) => {
    const portfolio = await AssurancePermissions.getPortfolioExpanded(req.params.id, req.user);
    if (portfolio.status === AssurancePortfolioStatus.Created) {
      const result = await assuranceManager.setReadyForAssurer(portfolio);
      res.FromModel(result);
    } else {
      throw new UserError('This assurance portfolio cannot be sent to assurer because it is already in progress.');
    }
  });

router.route('/portfolio/:id/documents')
  .delete(async (req, res) => {
    const portfolio = await AssurancePermissions.getPortfolio(req.params.id, req.user);
    const result = await assuranceManager.removeDocuments(portfolio, req.user, req.body.documentIds);
    res.FromModel(result);
  });


router.route('/portfolio/:id')
  .get(async (req, res, next) => {
    const portfolio = await AssuranceRepository.getAssuranceExpandedExtraById(req.params.id, req.user._id);
    if (!portfolio) {
      return next(new PermissionDeniedError());
    }
    const hasAccess = await AssurancePermissions.hasAccess(portfolio, req.user);
    if (hasAccess) {
      res.FromModel(portfolio);
    } else {
      next(new PermissionDeniedError());
    }
  })
  .patch(FileUpload.any(), async (req, res) => {
    const files = Array.isArray(req.files) ? req.files : [];
    const portfolio = await AssurancePermissions.getPortfolio(req.params.id, req.user);
    const result = await assuranceManager.update(portfolio, req.user, req.body, files);
    res.FromModel(result);
  });

router.route('/portfolio/:id/search')
  .get(async (req, res) => {
    const data = await UserRepository.searchByAssurancePortfolioId(req.query.s as string, req.params.id);
    const anonymizedData = await UserRepository.anonymize(data);
    res.FromModel(anonymizedData);
  });

router.route('/assurers')
  .get(async (req, res) => {
    const result = await Organization.find({ partnerTypes: 'assurer' }, { _id: 1, name: 1 });
    res.FromModel(result);
  });

router.route('/organization/:organizationId/users')
  .get(async (req, res, next) => {
    try {
      const organizationId = new ObjectId(req.params.organizationId);
      const filter = req.user.organizationId
        ? { _id: req.user.organizationId }
        : { 'permissions.userId': req.user._id };
      const organization = await Organization.findOne(filter, { _id: 1, permissions: 1 }).exec();

      if (!organization || !organization._id.equals(organizationId)) {
        return next(new PermissionDeniedError());
      }
      const organizationUsers = await organizationOnboardingRepo.getOrganizationUsers(organizationId);
      res.FromModel(organizationUsers)
    } catch (error) {
      next(error);
    }
  });

module.exports = router;
