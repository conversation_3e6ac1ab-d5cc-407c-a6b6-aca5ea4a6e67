/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import express from 'express';
import Onboarding, { OnboardingStatus } from '../../models/onboarding';
import User, { type UserModel } from '../../models/user';
import { getOnboardingManager, type JoinOnboardingData } from '../../service/onboarding/OnboardingManager';
import { createUserManager } from '../../service/user/UserManager';
import Survey from '../../models/survey';
import Initiative, { type InitiativePlain } from '../../models/initiative';
import { wwgLogger } from '../../service/wwgLogger';
import FileUpload from '../../http/FileUpload';
import { saveProfile } from '../../service/file/profile';
import UserError from '../../error/UserError';
import { UserErrorMessages } from '../../error/ErrorMessages';
import UserToken from '../../models/userToken';
import BadRequestError from '../../error/BadRequestError';
import { InitiativeRepository } from '../../repository/InitiativeRepository';
import {
  getRegistrationService,
  type SelfOnboardingData,
  type SelfOnboardingUserData
} from '../../service/onboarding/RegistrationService';
import { getRootInitiativeService } from '../../service/organization/RootInitiativeService';
import { getAppConfigService, validOnboardingPaths } from '../../service/app/AppConfigService';
import { authenticationRequired } from '../../service/authService';
import { UserActive } from '../../middleware/userMiddlewares';
import ContextError from '../../error/ContextError';
import { ProductCodes } from '../../models/customer';
import { getOnboardingAdminService } from '../../service/onboarding/OnboardingAdminService';
import { getInitiativeOnboardingRepository } from '../../repository/InitiativeOnboardingRepository';


const obManager = getOnboardingManager();
const userManager = createUserManager();
const router = express.Router();
const rootInitiativeService = getRootInitiativeService();


router.route('/onboarding/token/:token')
  .get((req, res, next) => {
    Onboarding.findOne({ 'token': req.params.token }).exec()
      .then(async (model) => {

        if (!model) {
          return next(new BadRequestError('Token is not valid.'));
        }

        const isRejected = [OnboardingStatus.Rejected, OnboardingStatus.Deleted].includes(model.status);
        if (isRejected) {
          return res.FromModel({ status: model.status });
        }

        const initiative = await Initiative.findById(model.initiativeId, {
          name: 1,
          permissionGroup: 1,
          appConfigCode: 1,
        }).lean().exec();

        const service = getAppConfigService();
        const appConfig = initiative?.appConfigCode ? await service.getByCode(initiative.appConfigCode) : undefined;

        if (model.status === OnboardingStatus.Complete) {
          const { status, surveyConfig, surveyStakeholders, assuranceStakeholders, type } = model;
          return res.FromModel({
            status,
            surveyConfig,
            surveyStakeholders,
            assuranceStakeholders,
            permissionGroup: initiative?.permissionGroup,
            appConfig,
            type,
          });
        }

        let user;
        if (!model.user.complete) {
          // Check if user has been created since last request
          user = await User.findOne({ email: model.user.email }).exec();
          if (user) {
            model.user.userId = user._id;
            model.user.complete = true;
            await model.save();
          }
        }

        if (model.surveyConfig && !model.surveyConfig.complete) {
          // Check if survey has been created since last request
          const survey = await Survey.findOne({ code: model.surveyConfig.code }).exec();
          if (survey && user) {
            if (survey.isStakeholder(user._id)) {
              model.surveyConfig.surveyId = survey._id;
              model.surveyConfig.complete = true;
              await model.save();
            }
          }
        }

        const externalIdp = await userManager.hasExternalIdp(model.user.email);
        const name = initiative ? initiative.name : '';

        return res.FromModel({
          ...model.toObject(),
          initiativeName: name,
          permissionGroup: initiative?.permissionGroup,
          appConfig,
          externalIdp,
        });
      })
      .catch((e: Error) => next(e));
  });

router.route('/onboarding/user/token/:token')
  .post(FileUpload.single('profile'), (req, res, next) => {
    Onboarding.findOne({ 'token': req.params.token }).exec()
      .then(async (model) => {
        if (!model) {
          return res.Invalid('Token is not valid');
        }
        if (model.user.userId) {
          return res.FromModel({ isNewUser: false, userId: model.user.userId });
        }
        const { user, isNewUser } = await obManager.createOnboardingUser(model, req.body);
        if (isNewUser && req.file) {
          await saveProfile(user._id, 'user', [req.file]).catch(wwgLogger.error);
        }

        if (isNewUser && 'initiativeId' in model && model.initiativeId) {
          // Execute post user created work
          await getOnboardingAdminService().acceptNewUser(user, model.initiativeId).catch(wwgLogger.error);
        }

        res.FromModel({ isNewUser, userId: user._id });
      })
      .catch((e: Error) => next(e));
  });

router.route('/onboarding/user/token/:token/reject')
  .post((req, res, next) => {
    Onboarding.findOne({ 'token': req.params.token }).exec()
      .then(async (model) => {

        if (!model) {
          return res.Invalid('Token is not valid');
        }

        const { success } = await obManager.rejectOnboarding(model);
        res.FromModel(success);
      })
      .catch((e: Error) => next(e));
  });

// ESGENOME ONLY
router.route('/onboarding/user/sgx-esgenome/register{/:domain}')
  .post(async (req, res, next) => {
    try {
      if (await User.exists({ email: req.body.email })) {
        return next(new BadRequestError(UserErrorMessages.EmailExists));
      }

      const appConfigService = getAppConfigService();

      const domain = req.params.domain || req.header('origin');
      const domainConfig = await rootInitiativeService.getDomainConfig({ domain });
      const appConfig = await appConfigService.mustGetByOnboardingPath('sgx-esgenome');

      const survey = await getRegistrationService().processSGXRegistration({
        data: req.body as SelfOnboardingData,
        domainConfig,
        appConfig,
        domain,
      });

      res.FromModel({ survey });
    } catch (e) {
      wwgLogger.error(e)
      next(e instanceof UserError ? e : new UserError(UserErrorMessages.FailToCreate))
    }
  });

validOnboardingPaths.forEach(onboardingPath => {
  
// Company tracker light and whitelabel version registration
router.route(`/onboarding/${onboardingPath}/config`)
  .get(async (req, res, next) => {
    try {
      const service = getAppConfigService();
      const appConfig = await service.mustGetByOnboardingPath(onboardingPath)
      res.FromModel({ appConfig })
    } catch (e) {
      next(e)
    }
  })

router.route(`/onboarding/${onboardingPath}/register-user{/:domain}`)
  .post(async (req, res, next) => {
    try {
      if (await User.exists({ email: req.body.email })) {
        return next(new BadRequestError(UserErrorMessages.EmailExists));
      }

      if (typeof req.body.email === 'string' && await userManager.hasExternalIdp(req.body.email)) {
        const emailDomain = req.body.email.split('@').slice(1).join();
        wwgLogger.error(new ContextError(`Blocking User Registration due to SSO email domain ${emailDomain}`, {
          emailDomain,
          onboardingPath,
        }));
        return next(new BadRequestError(UserErrorMessages.RegistrationMustUseSSO));
      }

      const appConfig = await getAppConfigService().mustGetByOnboardingPath(onboardingPath);
      const data = req.body as SelfOnboardingUserData;
      const registrationService = getRegistrationService()
      const newUser = await registrationService.createUser(data, appConfig)

      const domain = req.params.domain || req.header('origin');
      const domainConfig = await rootInitiativeService.getDomainConfig({ domain });
      await registrationService.sendActivation({
        newUser,
        domain,
        appConfig,
        domainConfig
      })

      res.FromModel(newUser);
    } catch (e) {
      next(e instanceof UserError ? e : new UserError(UserErrorMessages.FailToCreate, { cause: e }))
    }
  });

router.route(`/onboarding/${onboardingPath}/register-company{/:domain}`)
  .post(authenticationRequired, UserActive, async (req, res, next) => {
    try {
      const appConfig = await getAppConfigService().mustGetByOnboardingPath(onboardingPath);
      const data = req.body as SelfOnboardingData;
      const user = req.user as UserModel;

      const domain = req.params.domain || req.header('origin');

      if (appConfig.productCode === ProductCodes.MaterialityTracker) {
        const initiative = await getRegistrationService().processMATRegistration({
          newUser: user,
          domain: domain,
          appConfig: appConfig,
          data,
        });

        // MAT does not create any survey, however, successful responses must have same format

        return res.FromModel({ survey: { initiativeId: initiative._id.toString() } });
      }

      const { survey } = await getRegistrationService().processRegistration({
        newUser: user,
        domain: domain,
        appConfig: appConfig,
        data,
      })

      res.FromModel({ survey });
    } catch (e) {
      wwgLogger.error(e)
      next(e instanceof UserError ? e : new UserError(UserErrorMessages.FailToCreate))
    }
  });

router.route(`/onboarding/${onboardingPath}/find-existing`)
  .get(authenticationRequired, UserActive, async (req, res, next) => {
    try {
      const user = req.user;
      if (!user) {
        return res.Invalid();
      }
      res.FromModel(
        await InitiativeRepository.findByEmailDomain(user)
      );
    } catch (e) {
      wwgLogger.error(e)
      next(e instanceof UserError ? e : new UserError(UserErrorMessages.NotFound))
    }
  });

router.route(`/onboarding/${onboardingPath}/invitations`)
  .get(authenticationRequired, UserActive, async (req, res, next) => {
    try {
      const user = req.user;
      if (!user) {
        return res.Invalid();
      }
      const onboardingRepository = getInitiativeOnboardingRepository();
      const onboardings = await onboardingRepository.findExistingByUser(user.email, user._id);
      const initiatives: Pick<InitiativePlain, '_id' | 'name'>[] = await Initiative.find({
        _id: {
          $in: onboardings.map(onboarding => onboarding.initiativeId)
        }
      }, { _id: 1, name: 1 }).lean().exec();

      res.FromModel(initiatives);
    } catch (e) {
      wwgLogger.error(e)
      next(e instanceof UserError ? e : new UserError(UserErrorMessages.NotFound))
    }
  });

router.route(`/onboarding/${onboardingPath}/join`)
  .post(authenticationRequired, UserActive, async (req, res, next) => {
    const body = req.body as JoinOnboardingData;
    const user = req.user;
    if (!user) {
      return res.Invalid();
    }
    try {
      const MAX_JOINS = 3;
      const initiativeIds = body.initiativeIds ?? [];

      const joinData: JoinOnboardingData = {
        ...body,
        email: user.email,
        firstName: user.firstName,
        lastName: user.surname
      }

      const onboardingManager = getOnboardingManager();
      await Promise.all(
        initiativeIds.slice(0, MAX_JOINS).map(initiativeId => (
          onboardingManager.joinRequestByUser(initiativeId, user, joinData, {
            domain: req.header('origin'),
          })
        ))
      );

      res.Success();
    } catch (e) {
      // @TODO - What to do if one request fails but one succeeds?
      next(e instanceof UserError ? e : new UserError(UserErrorMessages.FailToCreate))
    }
  });

router.route(`/onboarding/user/${onboardingPath}/join`)
  .post(async (req, res, next) => {
    const joinData = req.body as JoinOnboardingData;
    try {
      const MAX_JOINS = 3;
      const initiativeIds = joinData.initiativeIds ?? [];

      const onboardingManager = getOnboardingManager();

      await Promise.all(
        initiativeIds.slice(0, MAX_JOINS).map(initiativeId => (
          onboardingManager.joinRequestByOnboardingData(initiativeId, joinData, {
            domain: req.header('origin'),
          })
        ))
      );

      res.Success();
    } catch (e) {
      // @TODO - What to do if one request fails but one succeeds?
      next(e instanceof UserError ? e : new UserError(UserErrorMessages.FailToCreate))
    }
  });

})

router.route('/onboarding/user/:userId/:token/activate')
  .post(async (req, res, next) => {
    if (!req.params.token) {
      return res.Invalid('Invalid request');
    }

    try {
      const userToken = await UserToken.findOne({ token: req.params.token }).exec();
      if (!userToken) {
        return next(new UserError(UserErrorMessages.ActivationTokenNotValid));
      }
      if (String(userToken.userId) !== req.params.userId) {
        return next(new UserError(UserErrorMessages.ActivationTokenNotValid));
      }

      const user = await User.findOne({ _id: userToken.userId }).exec()
      if (!user) {
        return next(new UserError(UserErrorMessages.ActivationTokenNotValid));
      }

      await userManager.activate(user);
      await userToken.deleteOne();
      return res.FromModel({ userId: user._id });
    }
    catch (e) {
      next(e);
    }
  });

module.exports = router;

