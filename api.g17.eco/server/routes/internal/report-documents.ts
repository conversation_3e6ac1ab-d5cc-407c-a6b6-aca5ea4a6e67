import express from 'express';
import { getReportDocumentManager } from '../../service/report-document/ReportDocumentManager';

const router = express.Router();
const reportDocumentManager = getReportDocumentManager();

router.route('/:reportId/sync').post(async (req, res, next) => {
  const results = await reportDocumentManager.synchronizeDocumentReport(req.params.reportId);
  res.FromModel(results);
});

module.exports = router;
