/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import UniversalTracker from '../../models/universalTracker';
import UniversalTrackerSearchService from '../../service/utr/UniversalTrackerSearchService';

const router = express.Router();

router.route('/').get(async (req, res) => {
  const model = await UniversalTracker.find().sort({ name: 'asc' }).lean().exec();
  res.FromModel(model);
});

router.route('/search').post(async (req, res) => {
  const results = await UniversalTrackerSearchService.searchByRequest(req.body);
  res.FromModel(results);
});

module.exports = router;
