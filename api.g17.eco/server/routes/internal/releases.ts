/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import { getInternalReleaseWorkflow } from '../../service/internal-release/InternalReleaseWorkflow';
import { ReleaseAction, ReleaseApprovalStatus, type ReleaseWorkflowCreate } from '../../service/internal-release/types';
import ContextError from '../../error/ContextError';
import { getBackgroundJobService } from '../../service/background-process/BackgroundJobService';
import { wwgLogger } from '../../service/wwgLogger';
import { type AuthRouter } from '../../http/AuthRouter';

const router = express.Router() as AuthRouter;

router.route('/jobs').post(async (req, res) => {
  const body = req.body as Partial<ReleaseWorkflowCreate>;

  if (
    !body.release ||
    !body.action ||
    !body.returnUrl ||
    ![ReleaseAction.Preview, ReleaseAction.Plan].includes(body.action)
  ) {
    throw new ContextError('Invalid internal release background job', body);
  }

  const { release, storage, action, returnUrl } = body;
  const requiredProperties = Object.keys(release) as (keyof ReleaseWorkflowCreate['release'])[];
  const missingProperties = requiredProperties.filter((p) => !release[p]);
  if (missingProperties.length > 0) {
    throw new ContextError('Missing required properties for internal release background job', {
      release: body.release,
    });
  }

  const workflow = getInternalReleaseWorkflow();
  const create: ReleaseWorkflowCreate = {
    release: {
      _id: release._id,
      code: release.code,
      path: release.path,
      scope: release.scope,
    },
    storage,
    action,
    returnUrl,
  };

  const job = await workflow.createJob(create, req.user);
  const bgJobService = getBackgroundJobService();
  bgJobService.runFromJob(job).catch((e) => wwgLogger.error(e));

  res.FromModel(job);
});

router.route('/jobs/:jobId').patch(async (req, res) => {
  const body = req.body as Partial<{
    action: 'approval';
    status: ReleaseApprovalStatus;
  }>;

  if (
    !body.action ||
    !body.status ||
    [ReleaseApprovalStatus.Approved, ReleaseApprovalStatus.Rejected].includes(body.status) === false
  ) {
    throw new ContextError('Invalid internal release background job', {
      action: body.action,
      updateStatus: body.status,
    });
  }

  const { action, status } = body;
  const workflow = getInternalReleaseWorkflow();
  const job = await workflow.findById(req.params.jobId);
  if (!job) {
    throw new ContextError('Invalid internal release background job', {
      jobId: req.params.jobId,
    });
  }

  if (action === 'approval') {
    await workflow.updateApproval(job, status, req.user);
    const bgJobService = getBackgroundJobService();
    bgJobService.runFromJob(job).catch((e) => wwgLogger.error(e));
  }
  res.FromModel(job);
});

module.exports = router;
