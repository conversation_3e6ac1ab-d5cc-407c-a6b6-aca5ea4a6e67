/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import { addFileUrl, getStorage } from '../../service/storage/fileStorage';
import Document from '../../models/document';
import multer = require('multer');
import deleteConfirm from '../../middleware/deleteConfirm';
import { getDocumentService } from '../../service/file/DocumentService';

const diskStorage = multer.diskStorage({
  destination: (req, file, cb) => cb(null, '/tmp/'),
  filename: (req, file, cb) => cb(null, file.fieldname + '-' + Date.now())
});

const router = express.Router();
const documents = getStorage();
const upload = multer({ storage: diskStorage });

const handlePost = async (req: any, res: any) => {

  const files = req.files;
  const data = req.body;

  if (!Array.isArray(files)) {
    return res.json({ success: false, message: 'No files uploaded' });
  }

  const file = files.shift();
  const result = await getDocumentService().handleDocumentUpload(data, file, req.user);
  return res.FromModel(result)
};

const handlePatch = async (req: any, res: any) => {
  await Document.findByIdAndUpdate(req.params.id, { $set: req.body });
  const message = 'Successfully updated document with _id=' + req.params.id;
  res.Success(message);
};

const handleGet = async (req: any, res: any) => {
  let model = await Document.findById(req.params.id).orFail().lean().exec();

  const metadata = model.metadata;
  if (!metadata) {
    throw new Error(`Document ${model._id} is missing metadata`)
  }
  model = addFileUrl(model);
  const path = `${model._id}.${metadata.extension}`;
  const signedUrl = await documents.getDownloadUrl(path, metadata.name);

  model.url = signedUrl.pop();
  res.FromModel(model);
};

async function getDocumentsByOwnerId(req: any, res: any) {
  const data = await Document.find({ ownerId: req.params.id }).lean().exec();
  data.map(addFileUrl);
  return res.FromModel(data);
}

router.route('/')
  .post(upload.any(), handlePost);

router.route('/:id')
  .get(handleGet)
  .patch(handlePatch)
  .delete(deleteConfirm, async (req, res) => {
    await Document.findByIdAndDelete(req.params.id).exec();
    res.Success('Object Deleted');
  });

router.route('/owner/:id')
  .get(getDocumentsByOwnerId);

module.exports = router;
module.exports.addFileUrl = addFileUrl;
module.exports.handleGet = handleGet;
