/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import UniversalTrackerGroup from '../../models/universalTrackerGroup';
import deleteConfirm from '../../middleware/deleteConfirm';
const router = express.Router();

router.route('/')
    .get(async function (req, res) {
        const model = await UniversalTrackerGroup.find().exec();
        res.FromModel(model);
    })
    .post(async function (req, res) {
        if (req.body._id) {
            return res.Invalid('Not allowed to specify _id on create');
        }
        const model = new UniversalTrackerGroup(req.body);
        await model.save();
        res.Success();
    });

router.route('/:id')
    .get(async function (req, res) {
        const model = await UniversalTrackerGroup.findById(req.params.id).exec();
        res.FromModel(model);
    })
    .delete(deleteConfirm, async function (req, res) {
        await UniversalTrackerGroup.findByIdAndDelete(req.params.id).exec();
        res.Success('Object Deleted');
    })
    .put(async function (req, res) {
        if (!req.body._id || req.params.id !== req.body._id) {
            return res.Invalid('POST url id (' + req.params.id + ') does not match POST body _id (' + req.body._id + ')');
        }

        const obj = await UniversalTrackerGroup.findById(req.params.id).orFail().exec();
        obj.set(req.body);
        await obj.save();
        res.Success('Successfully updated document with _id=' + req.params.id);
    });

module.exports = router;
