/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { buildCRUD, createSort } from './baseCrud';
import Sponsorship, { type ExtendedSponsorshipModel, type SponsorshipModel } from '../../models/sponsorship';
import express from 'express';
import { type AuthRouter } from '../../http/AuthRouter';
import { RequesterCode } from '../../service/organization/domainConfig';
import { ProductCodes } from '../../models/customer';
import { getSponsorshipMigration } from '../../service/referral/SponsorshipMigration';
import { getSponsorshipManager } from '../../service/referral/SponsorshipManager';

const router = express.Router() as AuthRouter;
router.route('/').get(async (req, res) => {
  const sort = createSort(req.query);
  const model = await Sponsorship.find({}, undefined, { sort })
    .populate([
      { path: 'initiative', select: 'name' },
      { path: 'sponsorshipConfig', select: 'title' },
    ])
    .exec();
  res.FromModel(model);
});

// Migrate legacy sponsorships to new sponsorship workflow
router.route('/migrate').post(async (req, res) => {
  const sponsorshipMigration = getSponsorshipMigration();

  const {
    referralCode = 'SGX4SGX',
    sponsorCode = RequesterCode.SGX,
    productCode = ProductCodes.SGXESGenome,
    limit = 50,
    initiativeIds,
  } = req.body;

  const data = await sponsorshipMigration.process({
    referralCode,
    sponsorCode,
    productCode,
    initiativeIds,
    limit,
  });
  return res.FromModel(data);
});

router.route('/:sponsorshipId/renew').post(async (req, res) => {
  const sponsorshipId = req.params.sponsorshipId;
  const sponsorshipManager = getSponsorshipManager();
  const sponsorship = await Sponsorship.findById(sponsorshipId)
    .populate<ExtendedSponsorshipModel>('sponsorshipConfig')
    .orFail()
    .exec();

  if (sponsorshipManager.shouldRenew(sponsorship) && !req.body.forceRenew) {
    return res.FromModel({
      message: 'Sponsorship is not due for renewal. Use { forceRenew: true } to renew anyway',
      sponsorship: sponsorship.toObject(),
    });
  }

  const periodEndDate = sponsorship.periodEndDate;

  const updatedModel = await sponsorshipManager.renew(sponsorship);
  res.FromModel({
    message: 'Sponsorship renewed successfully',
    sponsorship: updatedModel,
    periodEndDate: {
      before: periodEndDate,
      after: updatedModel.periodEndDate,
    },
  });
});

module.exports = buildCRUD<SponsorshipModel>(Sponsorship, router);
