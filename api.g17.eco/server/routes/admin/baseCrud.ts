/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import { type FilterQuery, type HydratedDocument, type Model } from 'mongoose';
import deleteConfirm from '../../middleware/deleteConfirm';
import { bodyParamIdCheck } from '../../middleware/commonMiddlewares';
import { toArray } from '../../http/query';
import { type AuthRouter } from '../../http/AuthRouter';
import { type ParsedQs } from 'qs';

export const createSort = (query: ParsedQs): Record<string, -1 | 1 > | undefined => {
  const { sortDirection, sortBy } = query;
  const direction = sortDirection === 'asc' ? 1 : -1;
  const sortArray = toArray(sortBy);

  return sortArray.length > 0 ?
    Object.fromEntries(sortArray.map(by => [by, direction])) :
    undefined;
};

export const buildCRUD = <T>(staticModel: Model<any>, router = express.Router() as AuthRouter) => {
  router.route('/')
    .get((req, res, next) => {
      const sort = createSort(req.query);

      const match: FilterQuery<Model<any>> = {};
      if (typeof req.query.start === 'string') {
        match['created'] = { $gte: new Date(req.query.start) };
      }
      if (typeof req.query.end === 'string') {
        if (!match['created']) {
          match['created'] = {};
        }
        match['created'].$lte = new Date(req.query.end);
      }

      staticModel.find(match, undefined, { sort }).exec()
        .then((model: T[]) => res.FromModel(model))
        .catch((e: Error) => next(e));
    })
    .post((req, res, next) => {
      if (req.body._id) {
        return res.Invalid('Not allowed to specify _id on create');
      }
      const model = new staticModel(req.body);
      model.save()
        .then((m: HydratedDocument<any>) => res.FromModel(m))
        .catch(next);
    });

  router.route('/code/:code')
    .get(async (req, res) => {
      const model = await staticModel.findOne({ 'code': req.params.code }).exec();
      res.FromModel(model);
    });

  router.route('/:id')
    .get(async (req, res) => {
      const model = await staticModel.findById(req.params.id).exec();
      res.FromModel(model);
    })
    .delete(deleteConfirm, async (req, res) => {
      await staticModel.findByIdAndDelete(req.params.id).exec();
      res.Success('Object Deleted');
    })
    .put(bodyParamIdCheck, async (req, res) => {
      const obj = await staticModel.findById(req.params.id).exec();
      obj.set(req.body);
      await obj.save();
      res.Success('Successfully updated document with _id=' + req.params.id);
    });
  return router;
};
