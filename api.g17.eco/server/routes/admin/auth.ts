/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */
import express from 'express';
import { verifyAuthHeader } from '../../service/authService';
import BadRequestError from '../../error/BadRequestError';
import { getUserProvisioning } from '../../service/user/UserProvisioning';

const router = express.Router();

router.post('/provision', async (req, res) => {
  const jwt = await verifyAuthHeader(req.headers.authorization);
  const subject = jwt.claims.sub;
  if (req.body.email !== subject) {
    throw new BadRequestError('User email do not match authenticated email')
  }
  const userProvisioning = getUserProvisioning();
  const newUser = await userProvisioning.provisionExternalUser(jwt)
  res.FromModel(newUser.getSafeUser())
});

module.exports = router;
