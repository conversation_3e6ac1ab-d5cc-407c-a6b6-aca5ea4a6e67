/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express, { type Response } from 'express';
import FileUpload from '../../http/FileUpload';
import { saveProfile } from '../../service/file/profile';
import BadRequestError from '../../error/BadRequestError';

const router = express.Router();

router.route('/type/:type/:id')
  .post(FileUpload.any(), async (req: any, res: Response, next) => {

    const files = req.files;
    if (!Array.isArray(files)) {
      return next(new BadRequestError('No files uploaded'));
    }

    const type = req.params.type;
    const id = req.params.id;

    const successMessage = await saveProfile(id, type, files);
    res.Success(successMessage);
  });

module.exports = router;
