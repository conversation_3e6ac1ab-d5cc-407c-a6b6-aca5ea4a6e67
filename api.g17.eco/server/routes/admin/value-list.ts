/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import ValueList from '../../models/valueList';
import deleteConfirm from '../../middleware/deleteConfirm';
import { bodyParamIdCheck } from '../../middleware/commonMiddlewares';
const router = express.Router();

router.route('/')
  .get(async (req, res) => {
    const model = await ValueList.find().exec();
    res.FromModel(model);
  })
  .post(async (req, res) => {
    if (req.body._id) {
      return res.Invalid('Not allowed to specify _id on create');
    }
    const model = new ValueList(req.body);
    await model.save();
    res.Success();
  });

router.route('/codes').post((req, res, next) => {
  if (!req.body?.length) {
    return res.FromModel([]);
  }
  ValueList.find({ code: { $in: req.body } }, '_id code')
    .exec()
    .then((model) => res.FromModel(model))
    .catch((e: Error) => next(e));
});

router.route('/code/:code')
    .get(async (req, res) => {
        const model = await ValueList.findOne({ 'code': req.params.code }).exec();
        res.FromModel(model);
    });

router.route('/:id')
    .get(async (req, res) => {
        const model = await ValueList.findById(req.params.id).exec();
        res.FromModel(model);
    })
    .delete(deleteConfirm, async function (req, res) {
        await ValueList.findByIdAndDelete(req.params.id).exec();
        res.Success('Object Deleted');
    })
    .put(bodyParamIdCheck, async (req, res) => {
        const obj = await ValueList.findById(req.params.id).orFail().exec();
        obj.set(req.body);
        await obj.save();
        res.Success('Successfully updated document with _id=' + req.params.id);
    });


module.exports = router;
