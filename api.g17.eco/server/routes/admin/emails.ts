/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import EmailTransaction from '../../service/email/model/EmailTransaction';
import Sequelize, { type FindOptions } from 'sequelize';
import User, { type UserModel } from '../../models/user';
import { ObjectId } from 'bson';
import moment from 'moment';
import Onboarding from '../../models/onboarding';

const router = express.Router();

router.route('/limit/:limit/offset/:offset')
  .get((req, res, next) => {
    const options: FindOptions = {
      order: [['createdAt', 'DESC']]
    };
    if (req.params.limit) {
      options.limit = Number(req.params.limit);
    }

    EmailTransaction.findAll(options)
      .then(async (models) => {

        const onboardingIds: ObjectId[] = [];
        const userIds: ObjectId[] = [];

        models.forEach((u: EmailTransaction) => {
          if (u.userId) {
            userIds.push(new ObjectId(u.userId))
          } else if (u.data?.onboardingId) {
            onboardingIds.push(new ObjectId(u.data.onboardingId))
          }
        });

        const onboardings = await Onboarding.find({ _id: { $in: onboardingIds }}, { user: 1 }).exec();
        const users = await User.find({ _id: { $in: userIds } }).exec();

        return res.FromModel(models.map((m: EmailTransaction) => {
          const transaction = m.toJSON();
          const user = users.find((u: UserModel) => m.userId === u._id.toString());

          if (user) {
            transaction.user  = user.getComplete();
          } else {
            const obId = m.data?.onboardingId;
            transaction.user  = obId ? onboardings.find(ob => String(ob._id) === obId)?.user : undefined;
          }
          return transaction;
        }));
      })
      .catch((e: Error) => next(e));
  });

router.route('/summary')
  .get((req, res, next) => {
    const createdAt = moment().utc().subtract('1', 'week').toDate();
    EmailTransaction.findAll({
      where : {
        createdAt: {
          [Sequelize.Op.gt]: createdAt
        }
      },
      attributes: [
        'type',
        [Sequelize.fn('COUNT', Sequelize.col('type')), 'count']
      ],
      group: ['type'],
    }).then((models: any) => res.FromModel(models))
      .catch((e: Error) => next(e));
  });

module.exports = router;
