/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import deleteConfirm from '../../middleware/deleteConfirm';
import ReportingFramework from '../../models/reportingFramework';
import { bodyParamIdCheck } from '../../middleware/commonMiddlewares';
import { ReportingFrameworkManager } from '../../service/reporting/ReportingFrameworkManager';
const router = express.Router();

router.route('/')
  .get(async (req, res) => {
    const model = await ReportingFramework.find().exec();
    res.FromModel(model);
  });

router.route('/import')
  .post(async (req, res) => {
    if (req.body._id) {
      return res.Invalid('Not allowed to specify _id on create');
    }

    await ReportingFrameworkManager.create(req.body);
    res.Success();
  });

router.route('/code/:code')
  .get(async function (req, res) {
    const model = await ReportingFramework.findOne({ 'code': req.params.code }).exec();
    res.FromModel(model);
  });

router.route('/:id')
  .get(async (req, res) => {
    const model = await ReportingFramework.findById(req.params.id).exec();
    res.FromModel(model);
  })
  .delete(deleteConfirm, async (req, res) => {
    await ReportingFramework.findByIdAndDelete(req.params.id).exec();
    res.Success('Object Deleted');
  })
  .put(bodyParamIdCheck, async (req, res) => {
    const o = await ReportingFrameworkManager.update(req.params.id, req.body);
    res.FromModel(o);
  });

module.exports = router;
