/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import { createAssuranceManager } from '../../service/assurance/AssuranceManager';
import { AssuranceRepository } from '../../repository/AssuranceRepository';
import { type AuthRouter } from '../../http/AuthRouter';
import { AssuranceOrganizationPermissions } from '../../service/assurance/AssuranceOrganizationPermissions';
import { mustValidate } from '../../util/validation';
import { z } from 'zod';

const router = express.Router() as AuthRouter;
const assuranceManager = createAssuranceManager();


router.route('/portfolio/:id')
  .delete((req, res, next) => {
    AssuranceRepository.findPortfolioById(req.params.id, false)
      .then((portfolio) => {
        if (!portfolio) {
          throw new Error('Portfolio not found')
        }
        return portfolio;
      })
      .then(portfolio => assuranceManager.deletePortfolio(portfolio, req.user))
      .then((result) => res.FromModel(result))
      .catch((err: Error) => next(err));
  });

router.route('/migrate/legacy-users-permissions').post((req, res, next) => {
  const { userIds } = mustValidate(
    req.body,
    z.object({
      userIds: z.string().array().optional(),
    })
  );
  AssuranceOrganizationPermissions.migrateLegacyUsers(userIds)
    .then((result) => res.FromModel(result))
    .catch((e) => next(e));
});

module.exports = router;
