/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import UniversalTrackerSchedule from '../../models/universalTrackerSchedule';
import moment from 'moment';
import { UniversalTrackerScheduleRepository } from '../../repository/UniversalTrackerScheduleRepository';
import type { AuthRouter } from '../../http/AuthRouter';
const router = express.Router() as AuthRouter;

router.route('/')
  .get(async (req, res) => {
    const model = await UniversalTrackerSchedule.find().exec();
    res.FromModel(model);
  })
  .post(async (req, res) => {
    if (req.body._id) {
      return res.Invalid('Not allowed to specify _id on create');
    }
    // Assign userId
    const user = req.user;
    req.body.userId = user._id;
    const model = new UniversalTrackerSchedule(req.body);
    await model.save();
    res.Success();
  });

router.route('/code/:code')
  .get(async function (req, res) {
    const model = await UniversalTrackerSchedule.findOne({ 'code': req.params.code }).exec();
    res.FromModel(model);
  });

router.route('/:id')
  .get(async (req, res) => {
    const model = !req.query.extended ?
      await UniversalTrackerScheduleRepository.findByIdExtended(req.params.id)
      : await UniversalTrackerSchedule.findById(req.params.id).lean().exec();

    res.FromModel(model);
  })
  .put(async (req, res) => {
    if (!req.body._id || req.params.id !== req.body._id) {
      return res.Invalid('POST url id (' + req.params.id + ') does not match POST body _id (' + req.body._id + ')');
    }

    const user = req.user;
    const obj = await UniversalTrackerSchedule.findById(req.params.id).orFail().exec();
    obj.set(req.body);
    if (!obj.userId) {
      obj.userId = user._id;
    }

    if (obj.isModified('cronSchedule')) {
      obj.nextRunDate = moment().endOf('day').toDate();
    }

    await obj.save();
    res.Success('Successfully updated document with _id=' + req.params.id);
  });

module.exports = router;
