/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import LedgerUniversalTrackerValue from '../../models/ledgerUniversalTrackerValue';

const router = express.Router();
router.route('/').get(async (req, res) => {
  const model = await LedgerUniversalTrackerValue.find().sort({ lastUpdatedDate: 'asc' }).lean().exec();
  res.FromModel(model);
});

module.exports = router;
