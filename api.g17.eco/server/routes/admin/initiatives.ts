/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import express from 'express';
import Initiative, { InitiativeTags, InitiativeTypes, TagGroups } from '../../models/initiative';
import Document from '../../models/document';
import deleteConfirm from '../../middleware/deleteConfirm';
import { bodyParamIdCheck } from '../../middleware/commonMiddlewares';
import { InitiativeManager } from '../../service/initiative/InitiativeManager';
import { ObjectId } from 'bson';
import { getOktaManager } from '../../service/user/OktaManager';
import { AdminAuditLogs } from '../../middleware/audit/adminAudit';
import { valueChainCategories } from '../../util/valueChain';
import { saveProfile } from '../../service/file/profile';
import { wwgLogger } from '../../service/wwgLogger';
import { downloadFile } from '../../service/file/file';
import { generatedUUID } from '../../service/crypto/token';
import path from 'path';
import * as fs from 'fs';
import { toBoolean } from '../../http/query';
import User, { userMinFields } from '../../models/user';
import UserError from '../../error/UserError';
import { getAppConfigProvider } from '../../service/app/AppConfigProvider';
import { type AppConfig } from '../../service/app/AppConfig';
import { checkIsSuperAdmin } from '../../middleware/userMiddlewares';
import { getUserManager } from '../../service/user/UserManager';
import { getUserPermissionService, type UserPermissionUpdateModel } from '../../service/user/UserPermissionService';
import { UserRoles } from '../../service/user/userPermissions';
import { FileParserType } from '../../service/survey/transfer/parserTypes';
import { getExportImportManager } from '../../service/organization/ExportImportManager';
import { setCsvFileHeaders } from '../../http/FileDownload';
import FileUpload from '../../http/FileUpload';
import ContextError from '../../error/ContextError';
import { requireStaffScopes } from '../../middleware/staffRoleMiddlewares';
import { StaffScope } from '../../models/staffRole';
import { mustValidate } from '../../util/validation';
import { z } from 'zod';
import { getArchivedInitiativeManager } from '../../service/initiative/ArchivedInitiativeManager';
import { type AuthRouter } from '../../http/AuthRouter';
import { getAuditLogger } from '../../service/audit/AuditLogger';
import { InitiativeAudit } from '../../service/audit/events/Initiative';
import { LEVEL } from '../../service/event/Events';
import { getUpgradeAppService, type RootInitiativeWithCustomer } from '../../service/organization/UpgradeAppService';
import { getInitiativeAuditLogsSchema } from '../validation-schemas/initiatives';
import config from '../../config';
import { getCloneOrganizationWorkflow } from '../../service/organization/clone/CloneWorkflow';
import { getBackgroundJobService } from '../../service/background-process/BackgroundJobService';
import { isRootLevel } from '../../middleware/initiativeMiddlewares';
import { getInitiativeSettingsService } from '../../service/initiative/InitiativeSettingsService';
import { FeatureTag } from '@g17eco/core';
import DOMPurify from 'isomorphic-dompurify';

const documents = require('./documents');
const router = express.Router({ mergeParams: true }) as AuthRouter;
router.use(AdminAuditLogs({ service: 'initiative' }));

const auditLogger = getAuditLogger();
const archivedInitiativeManager = getArchivedInitiativeManager();

router.route('/')
  .get((req, res, next) => {

    const { ids } = req.query as { ids?: string[] };
    const cond = Array.isArray(ids) ?
      { _id: { $in: ids.map(id => new ObjectId(id)) } }
      : {};

    Initiative.find(cond).sort({ name: 'asc' }).lean().exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => next(e));
  })
  .post(function (req, res, next) {
    const user = req.user;
    if (!user) {
      res.NotPermitted();
      return;
    }

    if (req.body._id) {
      return res.Invalid('Not allowed to specify _id on create');
    }

    InitiativeManager.createFromRequest(req.body)
      .then(async (model) => {
        const initiativeId = model._id;
        await getUserPermissionService().addAndUpdate({
          user,
          initiativeId,
          permissions: [UserRoles.Owner],
          context: { actor: req.user },
        });
        res.FromModel(model);
      })
      .catch((e: Error) => next(e));
  });

router.route('/query')
  .post((req, res, next) => {
    const { ids } = req.body as { ids?: string[] };
    const cond = Array.isArray(ids) ?
      { _id: { $in: ids.map(id => new ObjectId(id)) } }
      : {};
    Initiative.find(cond).sort({ name: 'asc' }).lean().exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => next(e));
  })

router.route('/okta/groups')
  .get((req, res, next) => {
    getOktaManager().getAssignedGroups()
      .then((data) => res.FromModel(data))
      .catch((e: Error) => next(e));
  })

router.route('/app-configs')
  .get((req, res, next) => {
    getAppConfigProvider().getAll()
      .then((data) => {
        return res.FromModel(data.reduce((acc, config) => {
          if (!acc[config.productCode]) {
            return { ...acc, [config.productCode]: [config] }
          }
          acc[config.productCode].push(config)
          return acc
        }, {} as Record<string, AppConfig[]>));
      })
      .catch((e: Error) => next(e));
  })

router.route('/users')
  .get(checkIsSuperAdmin, async (req, res, next) => {

    try {
      const { initiativeIds, isStaff } = req.query;

      // Support single initiative as well
      if (!Array.isArray(initiativeIds) || initiativeIds.length === 0) {
        return next(new UserError('Must provide at least one initiativeIds value'))
      }

      const objectIds = initiativeIds.map(id => new ObjectId(id as string));

      const $match: Record<string, unknown> = { 'permissions.initiativeId': { $in: objectIds } };
      if (isStaff !== undefined) {
        $match.isStaff = toBoolean(isStaff)
      }

      const users = await User.find($match,
        {
          ...userMinFields,
          email: 1,
          created: 1,
          lastLogin: 1,
          isStaff: 1,
          permissions: 1,
          telephoneNumber: 1,
        }
      ).lean().exec();

      const initiatives = await Initiative.find(
        { _id: { $in: objectIds } },
        { _id: 1, code: 1, name: 1, permissions: 1, profile: 1, }
      ).lean().exec();

      return res.FromModel(users.map(u => {
        const ids = u.permissions.map(p => p.initiativeId.toHexString());

        return {
          ...u,
          permissions: undefined,
          initiatives: initiatives.filter(({ _id }) => ids.includes(String(_id))),
        }
      }));
    } catch (e) {
      next(e)
    }
  })

router.route('/code/:code/import-ratings')
  .patch((req, res, next) => {
    InitiativeManager.importRatings(req.params.code, req.body.ratings)
      .then((initiative) => res.FromModel(initiative))
      .catch((e: Error) => next(e));
  });

router.route('/code/:code')
  .get(function (req, res, next) {
    Initiative.findOne({ 'code': req.params.code }).lean().exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => next(e));
  });

router.route('/orphan')
  .get((req, res, next) => {
    Initiative.find({ parentId: { $exists: false } }).sort({ name: 'asc' }).lean().exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => next(e));
  });

router.route('/tags')
  .get((req, res) => res.FromModel(TagGroups));

router.route('/portfolios')
  .get(function (req, res, next) {
    Initiative.find({ type: InitiativeTypes.Group }).lean().exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => next(e));
  });

router.route('/companies')
  .get(function (req, res, next) {
    Initiative.find({ tags: InitiativeTags.Organization }).lean().exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => next(e));
  });

router.route('/archive')
  .put(requireStaffScopes([StaffScope.AdminRead, StaffScope.AdminWrite]), async (req, res, next) => {
    try {
      // Allowing a list of initiatives, but for now will handle one initiative per request
      const { initiativeIds } = mustValidate(
        req.body,
        z.object({ initiativeIds: z.string().array().min(1).max(1) })
      );

      const eligibleOrganizations = await archivedInitiativeManager.getEligibleOrganizations({
        initiativeIds,
        isArchive: true,
      });
      if (eligibleOrganizations.length <= 0) {
        return res.FromModel({ initiativeIds: [] });
      }

      for (const eligibleOrganization of eligibleOrganizations) {
        const archivedIds = await archivedInitiativeManager.archiveCompany({
          archivedId: eligibleOrganization._id,
          user: req.user
        });
        auditLogger
          .fromRequest(req, {
            initiativeId: eligibleOrganization._id,
            auditEvent: InitiativeAudit.archivedInitiative,
            severity: LEVEL.WARNING,
            targets: archivedIds.map((id) => auditLogger.initiativeTarget({ _id: id })),
          })
          .catch(wwgLogger.error);
      }
      return res.FromModel({ initiativeIds: eligibleOrganizations.map(initiative => initiative._id) });
    } catch (error) {
      next(error);
    }
  });

router.route('/reactivate')
  .put(requireStaffScopes([StaffScope.AdminRead, StaffScope.AdminWrite]), async (req, res, next) => {
    try {
      const { initiativeIds } = mustValidate(
        req.body,
        z.object({ initiativeIds: z.string().array().min(1).max(1) })
      );

      const eligibleOrganizations = await archivedInitiativeManager.getEligibleOrganizations({ initiativeIds, isArchive: false });
      if (eligibleOrganizations.length <= 0) {
        return res.FromModel({ initiativeIds: [] });
      }

      for (const eligibleOrganization of eligibleOrganizations) {
        const reactivatedIds = await archivedInitiativeManager.reactivate({ archivedInitiativeId: eligibleOrganization._id });
        auditLogger
          .fromRequest(req, {
            initiativeId: eligibleOrganization._id,
            auditEvent: InitiativeAudit.reactivatedInitiative,
            severity: LEVEL.WARNING,
            targets: reactivatedIds.map((id) => auditLogger.initiativeTarget({ _id: id })),
          })
          .catch(wwgLogger.error);
      }
      return res.FromModel({ initiativeIds: eligibleOrganizations.map(initiative => initiative._id) });
    } catch (error) {
      next(error);
    }
  });

router.route('/companies/nodes')
  .get(function (req, res, next) {
    Initiative.find({ tags: { $nin: valueChainCategories } }).lean().exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => next(e));
  });

router.route('/:id/children')
  .get((req, res, next) => {
    Initiative.find({ parentId: req.params.id }).sort({ name: 'asc' }).lean().exec()
      .then((model) => res.FromModel(model))
      .catch((e: Error) => next(e));
  });

router.route('/:id/features').get(async (req, res) => {
  const initiativeId = req.params.id;
  const results = await InitiativeManager.getDefaultAndConfigFeatures(initiativeId, req.header('origin'));
  res.FromModel(results);
});


router.route('/:id/upgrade')
  .post(requireStaffScopes([StaffScope.AdminWrite]), (req, res, next) => {
    Initiative.findOne({
      _id: req.params.id,
      tags: InitiativeTags.Organization,
      customer: { $exists: true },
    })
      .orFail(new UserError('Upgrade can only be applied at organization level.'))
      .lean<RootInitiativeWithCustomer>()
      .exec()
      .then(async (rootInitiative) => {

        const { productCode, appConfigCode, couponId } = mustValidate(req.body, z.object({
          productCode: z.string(),
          appConfigCode: z.string().optional(),
          couponId: z.string().optional(),
        }));

        const result = await getUpgradeAppService().upgrade({
          rootInitiative,
          newProductCode: productCode,
          appConfigCode,
          couponId,
        })
        res.FromModel(result)

      })
      .catch(next);
  })

router.route('/:id')
  .delete(deleteConfirm, function (req, res, next) {
    Initiative.findByIdAndDelete(req.params.id).exec()
      .then(() => res.Success('Object Deleted'))
      .catch((e: Error) => next(e));
  })
  .put(bodyParamIdCheck, function (req, res, next) {
    return InitiativeManager.adminUpdate(req.params.id, req.body)
      .then((initiative) => {

        const msg = 'Successfully updated document with _id=' + req.params.id;
        const fileUrl = req.body.logoUrl;

        if (typeof fileUrl === 'string' && fileUrl.toLowerCase().startsWith('http')) {
          const destination = `/tmp/${generatedUUID()}${path.extname(fileUrl) ?? ''}`

          const now = Date.now()
          return downloadFile(fileUrl, destination).then(({ contentLength, contentType }) => {
            const file = {
              mimetype: contentType,
              originalname: path.basename(fileUrl),
              path: destination,
              size: Number(contentLength),
            } as Express.Multer.File
            return saveProfile(initiative._id, 'initiative', [file])
          })
            .then((msg) => wwgLogger.info(msg, { duration: Date.now() - now }))
            .catch(wwgLogger.error)
            .finally(() => {
              fs.unlinkSync(destination)
              res.Success(msg);
            });
        }

        return res.Success(msg);
      })
      .catch((e: Error) => next(e));
  })
  .get(function (req, res, next) {
    Initiative.findById(req.params.id).lean().exec()
      .then((initiative: any) => {
        if (!initiative) {
          return res.FromModel(initiative);
        }
        Document.find({ ownerId: initiative._id })
          .sort({ created: 'desc' }).lean().exec()
          .then((data: Array<any>) => {
            initiative.files = data.map(documents.addFileUrl);
            res.FromModel(initiative);
          }).catch(e => next(e));
      }).catch((e: Error) => next(e));
  });

router.route('/combine/:sourceId/:destinationId')
  .post(async (req, res, next) => {
    try {
      const { sourceId, destinationId } = req.params;

      const sourceInitiative = await Initiative.findOne({ _id: sourceId, tags: InitiativeTags.Organization }).exec();
      const destinationInitiative = await Initiative.findOne({ _id: destinationId, tags: InitiativeTags.Organization }).exec();

      if (!sourceInitiative || !destinationInitiative) {
        return next(new UserError('Invalid initiatives'))
      }

      if (!sourceInitiative.tags?.includes(InitiativeTags.Organization) || !destinationInitiative.tags?.includes(InitiativeTags.Organization)) {
        return next(new UserError('Initiatives are not proper organizations. Aborting.'))
      }

      const sourceUsers = await User.find<UserPermissionUpdateModel>({ 'permissions.initiativeId': sourceInitiative._id }, { _id: 1, permissions: 1 }).exec();
      const userManager = getUserManager();

      await Promise.all(sourceUsers
        .map(async sourceUser => {
          const currentPermissions = sourceUser.permissions.find(permission => String(permission.initiativeId) === sourceId);
          if (!currentPermissions) {
            return false;
          }
          // Copy same permissions to destination and delete from source
          return userManager
            .updateInitiativePermissions(sourceUser, destinationId, currentPermissions.permissions)
            .then(() => userManager.removeInitiativePermissions(sourceUser, sourceId));
        })
      );

      await InitiativeManager.softDelete(sourceInitiative);

      res.Success(`Combined ${sourceId} with ${destinationId}. ${sourceUsers.length} migrated. ${sourceInitiative.name} has been soft-deleted.`);

    } catch (e) {
      next(e)
    }
  });

router.route('/:id/export/csv')
  .get(async (req, res, next) => {
    try {
      const filename = `Subsidiaries import sheet template.${FileParserType.Csv}`;
      const manager = getExportImportManager();

      const csvData = await manager.exportSubsidiariesImportTemplate(req.params.id);
      setCsvFileHeaders(res, filename);

      return res.send(csvData);
    } catch (error) {
      next(error);
    }
  });

router.route('/:initiativeId/import/csv')
  .post(isRootLevel, FileUpload.single('file'), async (req, res, next) => {
    if (!req.file) {
      return next(new ContextError('Missing required import file'));
    }
    try {
      const manager = getExportImportManager();
      const result = await manager.importFile(res.locals.initiative, req.file.path);
      res.FromModel(result);
    } catch (error) {
      next(error);
    }
  });

router.route('/bulk-update-financial-end-date').post(async (req, res) => {
  const results = await InitiativeManager.bulkUpdateFinancialEndDate();
  res.FromModel(results);
});

router
  .route('/:initiativeId/audit-logs')
  .get(requireStaffScopes([StaffScope.CompanyRead, StaffScope.CompanyWrite]), async (req, res, next) => {
    try {
      const { startDate, endDate, limit } = mustValidate(req.query, getInitiativeAuditLogsSchema);
      const logs = await auditLogger.findForInitiative(req.params.initiativeId, {
        fromDate: startDate,
        toDate: endDate,
        limit,
      });
      res.FromModel(logs);
    } catch (e) {
      next(e);
    }
  });

router
  .route('/clone/export/:initiativeId')
  .post(async (req, res, next) => {
    try {
      const { initiativeId } = req.params;
      const workflow = getCloneOrganizationWorkflow();
      const job = await workflow.createExportJob({
        initiativeId,
        filename: `clone-${config.appEnv}-${initiativeId}.g17`,
      }, req.user);

      const bgJobService = getBackgroundJobService();
      bgJobService.runFromJob(job).catch((e) => wwgLogger.error(e));

      return res.FromModel({
        job
      });
    } catch (e) {
      next(e);
    }
  });

router
  .route('/clone/import')
  .post(async (req, res, next) => {
    try {
      const { url } = req.body;
      if (!url) {
        return res.Invalid('Missing required url');
      }

      const workflow = getCloneOrganizationWorkflow();
      const job = await workflow.createImportJob({
        url: String(url),
      }, req.user);

      const bgJobService = getBackgroundJobService();
      bgJobService.runFromJob(job).catch((e) => wwgLogger.error(e));

      return res.FromModel({
        job
      });
    } catch (e) {
      next(e);
    }
  });

router
  .route('/clone/jobs')
  .get(async (req, res, next) => {
    try {
      const workflow = getCloneOrganizationWorkflow();
      const jobs = await workflow.getJobs();
      return res.FromModel(jobs);
    } catch (e) {
      next(e);
    }
  });

router
  .route('/clone/jobs/:jobId')
  .get(async (req, res, next) => {
    try {
      const { jobId } = req.params;
      const workflow = getCloneOrganizationWorkflow();
      const jobInfo = await workflow.getJob(jobId);
      return res.FromModel(jobInfo);
    } catch (e) {
      next(e);
    }
  });

router.route('/:initiativeId/settings/features').post(async (req, res, next) => {
  try {
    const service = getInitiativeSettingsService();
    const featuresConfig = mustValidate(
      req.body,
      z.object({
        features: z
          .object({
            tag: z.nativeEnum(FeatureTag),
            active: z.boolean(),
            disabled: z.boolean().optional(),
            disabledReason: z
              .string()
              .optional()
              .transform((dirtyInput) => {
                return dirtyInput
                  ? DOMPurify.sanitize(dirtyInput, {
                      ALLOWED_TAGS: [], // No HTML tags allowed
                      ALLOWED_ATTR: [], // No attributes allowed
                    })
                  : undefined;
              }),
          })
          .array(),
      })
    );
    const result = await service.updateFeatures(req.params.initiativeId, featuresConfig.features);
    res.FromModel(result);
  } catch (error) {
    next(error);
  }
});

module.exports = router;
