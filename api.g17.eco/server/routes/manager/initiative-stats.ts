/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import express from 'express';
import { type AuthenticatedRequest, type AuthRouter } from '../../http/AuthRouter';
import { getInitiativeStatsService, toStatsQuery } from '../../service/initiative/InitiativeStatsService';
import Initiative from '../../models/initiative';
import UserError from '../../error/UserError';
import { FileParserType } from '../../service/survey/transfer/parserTypes';
import { setXlsxFileHeaders } from '../../http/FileDownload';
import * as XLSX from '@sheet/core';
import { ObjectId } from 'bson';
import { SurveyRepository } from '../../repository/SurveyRepository';
import User from '../../models/user';

const router = express.Router() as AuthRouter;
const initiativeStats = getInitiativeStatsService();

router.route('')
  .get(async (req, res, next) => {
    Initiative.findById(res.locals.initiativeId).orFail().exec()
      .then(initiative => initiativeStats.withQuestionStatus(toStatsQuery(req, initiative)))
      .then(stats => res.FromModel(stats))
      .catch(e => next(e))
  });

router.route('/download')
  .get(async (req, res, next) => {
    Initiative.findById(res.locals.initiativeId).orFail().exec()
      .then(initiative => initiativeStats.initiativeStatsExcel(toStatsQuery(req, initiative)))
      .then(stats => {
        const { fileName, workBook } = stats
        if (!workBook) {
          return next(new UserError('Could not generate xlsx'));
        }
        setXlsxFileHeaders(res, fileName);
        res.send(
          XLSX.write(workBook, { type: 'buffer', bookType: FileParserType.Xlsx })
        );
      })
      .catch(e => next(e))
  });

router.route('/users')
  .get(async (req, res, next) => {
    Initiative.findById(res.locals.initiativeId).orFail().exec()
      .then(initiative => {
        const query = toStatsQuery(req, initiative);
        return initiativeStats.userStats(query);
      })
      .then(stats => res.FromModel(stats))
      .catch(e => next(e))
  });

const getUserStats = async (req: AuthenticatedRequest, initiativeId: string) =>
  Initiative.findById(initiativeId)
    .orFail()
    .exec()
    .then((initiative) => {
      const query = toStatsQuery(req, initiative);
      return initiativeStats.userSurveyStats(query);
    });

router.route('/users/:userId').get(async (req, res, next) => {
  try {
    const data = await getUserStats(req, res.locals.initiativeId);
    return res.FromModel(data);
  } catch (e) {
    next(e);
  }
});

router.route('/users/:userId/export/excel').get(async (req, res, next) => {
  try {
    const data = await getUserStats(req, res.locals.initiativeId);
    const { fileName, exportType, workBook } = await initiativeStats.userStatsExcel(data);
    setXlsxFileHeaders(res, fileName);

    return res.send(
      XLSX.write(workBook, {
        type: 'buffer',
        bookType: exportType,
      })
    );
  } catch (e) {
    next(e);
  }
});

router.route('/users/:userId/surveys/:surveyId').get(async (req, res) => {
  const user = await User.findById(req.params.userId).lean().exec();
  if (!user) {
    return res.FromModel();
  }
  const action = await SurveyRepository.getSurveyData(req.params.surveyId, user, req.header('origin'));
  return res.FromModel(action);
});

router.route('/users/:userId/survey/:surveyId/download').get(async (req, res, next) => {
  try {
    // Why do we need id, seems like it's always current user?
    const userId = req.params.userId;
    const user = req.user._id.equals(userId) ? req.user : await User.findById(userId).orFail().lean().exec();

    const stats = await initiativeStats.surveyStatsExcel({
      initiativeId: res.locals.initiativeId as string,
      surveyId: req.params.surveyId,
      user,
      origin : req.header('origin')
    })
    setXlsxFileHeaders(res, stats.fileName);
    res.send(XLSX.write(stats.workBook, { type: 'buffer', bookType: FileParserType.Xlsx }));
  } catch (e) {
    next(e);
  }
});

router.route('/surveys/:surveyId/stakeholders')
  .get(async (req, res, next) => {
    Initiative.findById(res.locals.initiativeId).orFail().exec()
      .then(initiative => initiativeStats.usersForSurvey({
        initiative,
        surveyId: new ObjectId(req.params.surveyId)
      }))
      .then(stats => res.FromModel(stats))
      .catch(e => next(e))
  });

// @TODO remove as it was replaced by survey specific endpoint
router.route('/stakeholders')
  .post(async (req, res, next) => {
    Initiative.findById(res.locals.initiativeId).orFail().exec()
      .then(initiative => {
        return initiativeStats.userStatsBySurvey(initiative, req.body.surveyIds.map((id: string) => new ObjectId(id)));
      })
      .then(stats => res.FromModel(stats))
      .catch(e => next(e))
  });

module.exports = router;
