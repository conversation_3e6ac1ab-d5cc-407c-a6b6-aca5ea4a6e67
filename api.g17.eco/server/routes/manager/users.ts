import { write } from '@sheet/core';
import express from 'express';
import { ObjectId } from 'bson';
import UserError from '../../error/UserError';
import { type AuthenticatedRequest, type AuthRouter } from '../../http/AuthRouter';
import { setXlsxFileHeaders } from '../../http/FileDownload';
import FileUpload from '../../http/FileUpload';
import { ContextMiddleware } from '../../middleware/audit/contextMiddleware';
import User, { userMinFields } from '../../models/user';
import { InitiativeRepository } from '../../repository/InitiativeRepository';
import { getAuditLogger } from '../../service/audit/AuditLogger';
import { InitiativeAudit } from '../../service/audit/events/Initiative';
import { InitiativePermissions } from '../../service/initiative/InitiativePermissions';
import {
  type UserBulkDelegationParams,
  UserBulkDelegationService,
} from '../../service/initiative/UserBulkDelegationService';
import { getOnboardingManager, type MultipleOnboardingEnrollData } from '../../service/onboarding/OnboardingManager';
import { FileParserType } from '../../service/survey/transfer/parserTypes';
import { getBulkOnboardingService } from '../../service/user/BulkOnboardingService';
import { getInitiativeUserService } from '../../service/user/InitiativeUserService';
import { createUserManager } from '../../service/user/UserManager';
import { ALLOWED_UPDATING_USER_ROLES, UserRoles } from '../../service/user/userPermissions';
import { wwgLogger } from '../../service/wwgLogger';
import { getOnboardingListService } from '../../service/onboarding/OnboardingListService';
import { mustValidate } from '../../util/validation';
import { z } from 'zod';
import { LEVEL } from '../../service/event/Events';

const router = express.Router() as AuthRouter;
const bulkOnboardingManager = getBulkOnboardingService();
const onboardingManager = getOnboardingManager();
const userManager = createUserManager();
const auditLogger = getAuditLogger();

router.route('/').get(async (_req, res, next) => {
  try {
    const initiativeId = res.locals.initiativeId as string;
    const result = await getInitiativeUserService().getInitiativeUsers(initiativeId);
    res.FromModel(result);
  } catch (e) {
    next(e);
  }
});

router.route('/subsidiaries').get(async (_req, res, next) => {
  try {
    const initiativeId = res.locals.initiativeId as string;
    const result = await getInitiativeUserService().getInitiativeUsers(initiativeId, true);
    res.FromModel(result);
  } catch (e) {
    next(e);
  }
});

router.route('/onboard').post(ContextMiddleware, async (req, res) => {
  const initiativeId = res.locals.initiativeId as string; // Parsed by middleware
  const body = req.body as MultipleOnboardingEnrollData;

  const { emails = [] } = body;
  const resp = await onboardingManager.onboardEmails(emails, initiativeId, req.user, body);
  res.FromModel(resp);
});

router.route('/import').post(FileUpload.single('file'), ContextMiddleware, async (req, res, next) => {
  const file = req.file;
  if (!file || !file.path) {
    return next(new UserError('Missing required import file'));
  }
  const initiativeId = res.locals.initiativeId as string;

  try {
    const { validatedData } = await bulkOnboardingManager.validateImportData({
      initiativeId,
      filePath: file.path,
    });

    await bulkOnboardingManager.bulkOnboardEmails({
      users: validatedData,
      delegator: req.user,
      fileName: file.originalname,
      initiativeId,
    });
    res.Success();
  } catch (e) {
    next(e);
  }
});

router
  .route('/:userId')
  .get(async (req, res, next) => {
    try {
      const { userId } = req.params;
      const initiativeId = res.locals.initiativeId as string;
      const initiatives = await InitiativeRepository.getMainTreeChildren(initiativeId);
      const initiativeIds = initiatives.map((i) => i._id);

      const $match: Record<string, unknown> = {
        _id: new ObjectId(userId),
        'permissions.initiativeId': { $in: initiativeIds },
      };
      const user = await User.findOne($match, userMinFields).orFail().lean().exec();
      return res.FromModel(user);
    } catch (e) {
      next(e);
    }
  })
  .patch(async (req, res, next) => {
    try {
      const initiativeId = res.locals.initiativeId;
      const user = await User.findById(req.params.userId).exec();
      if (!user) {
        throw new UserError('User not found');
      }

      const { permissions } = mustValidate(
        req.body,
        z.object({
          permissions: z.enum(ALLOWED_UPDATING_USER_ROLES).array(),
        })
      );
      const isCurrentOwner = user.permissions?.find(p => String(p.initiativeId) === initiativeId)?.permissions?.includes(UserRoles.Owner);
      // If current owner, preserve owner role
      const updateRoles = isCurrentOwner ? [UserRoles.Owner, ...permissions] : permissions;
      await userManager.updateInitiativePermissions(user, initiativeId, updateRoles);
      auditLogger
        .fromRequest(req, {
          initiativeId,
          auditEvent: InitiativeAudit.userPermissionUpdate,
          targets: [auditLogger.initiativeTarget({ _id: initiativeId }), auditLogger.userTarget(user)],
          debugData: { permissions },
        })
        .catch(wwgLogger.error);

      return res.FromModel(user.getSafeUser());
    } catch (e) {
      next(e);
    }
  })
  .delete(async (req, res, next) => {
    try {
      const initiativeId = res.locals.initiativeId;
      const user = await User.findById(req.params.userId).exec();
      if (!user) {
        throw new UserError('User not found');
      }

      if (await InitiativePermissions.isOwner(user, initiativeId)) {
        throw new UserError('Cannot delete Owner', {
          initiativeId,
          userId: user._id.toString(),
          actorUserId: req.user._id.toString(),
        });
      }

      user.permissions = user.permissions?.filter((p) => String(p.initiativeId) !== initiativeId);
      await user.save();

      auditLogger
        .fromRequest(req as AuthenticatedRequest, {
          initiativeId,
          auditEvent: InitiativeAudit.userRemove,
          message: user.isStaff ? 'A staff member has been removed access to a company' : undefined,
          targets: [auditLogger.initiativeTarget({ _id: initiativeId }), auditLogger.userTarget(user)],
        })
        .catch(wwgLogger.error);

      return res.FromModel(user.getSafeUser());
    } catch (e) {
      next(e);
    }
  });


router.route('/:userId/owner').patch(async (req, res, next) => {
  try {
    const initiativeId = res.locals.initiativeId;

    // Current user must be direct owner of the initiative, as we will transfer to initiativeId tha was passed in
    const currentOwnerPerm = req.user.permissions.find((p) => String(p.initiativeId) === initiativeId);
    if (!currentOwnerPerm?.permissions.includes(UserRoles.Owner)) {
      next(new UserError('Only the owner can transfer ownership', { userId: req.params.userId, initiativeId }));
      return;
    }

    const user = await User.findById(req.params.userId).exec();
    if (!user) {
      next(new UserError('User not found', { userId: req.params.userId, initiativeId }));
      return;
    }

    // Current logic was adding Owner, Manager, Contributor and Verifier...
    const permissions = [UserRoles.Owner, UserRoles.Manager];

    const replace = false; // !!! Ensure we will merge, instead of replace
    await userManager.updateInitiativePermissions(user, initiativeId, permissions, replace);
    auditLogger
      .fromRequest(req, {
        initiativeId,
        severity: LEVEL.WARNING,
        auditEvent: InitiativeAudit.userPermissionUpdate,
        targets: [auditLogger.initiativeTarget({ _id: initiativeId }), auditLogger.userTarget(user)],
        debugData: { permissions },
      })
      .catch(wwgLogger.error);

    return res.FromModel(user.getSafeUser());
  } catch (e) {
    next(e);
  }
})

router.route('/:userId/delegation').post(async (req, res, next) => {
  try {
    const user = await User.findById(req.params.userId, { _id: 1 }).lean().exec();
    if (!user) {
      return next(new UserError('User not found'));
    }

    const { datePeriods, initiativeIds, stakeholderUtrIds, verifierUtrIds } = req.body as Pick<
      UserBulkDelegationParams,
      'datePeriods' | 'initiativeIds' | 'stakeholderUtrIds' | 'verifierUtrIds'
    >;
    const data = await UserBulkDelegationService.executeDelegate({
      user,
      delegator: req.user,
      initiativeId: res.locals.initiativeId as string,
      datePeriods,
      initiativeIds,
      stakeholderUtrIds,
      verifierUtrIds,
    });
    return res.FromModel(data);
  } catch (e) {
    next(e);
  }
});

router.route('/:userId/delegation/questions').post(async (req, res, next) => {
  try {
    const user = await User.findById(req.params.userId, { _id: 1 }).lean().exec();
    if (!user) {
      return next(new UserError('User not found'));
    }
    const { datePeriods, initiativeIds } = req.body as Pick<UserBulkDelegationParams, 'datePeriods' | 'initiativeIds'>;
    const data = await UserBulkDelegationService.getDelegateQuestions({
      user,
      delegator: req.user,
      initiativeId: res.locals.initiativeId as string,
      datePeriods,
      initiativeIds,
    });
    return res.FromModel(data);
  } catch (e) {
    next(e);
  }
});

router.route('/export/xlsx').get(ContextMiddleware, async (req, res, next) => {
  try {
    const exportType = FileParserType.Xlsx;
    const workbook = await bulkOnboardingManager.exportBulkInviteTemplate(res.locals.initiativeId as string);
    const filename = `Bulk invite users sheet template.${exportType}`;
    setXlsxFileHeaders(res, filename);

    return res.send(write(workbook, { type: 'buffer', bookType: exportType, cellStyles: true }));
  } catch (e) {
    next(e);
  }
});

router.route('/import/validate').post(FileUpload.single('file'), ContextMiddleware, async (req, res, next) => {
  const file = req.file;
  const initiativeId = res.locals.initiativeId as string;
  if (!file || !file.path) {
    return next(new UserError('Missing required import file'));
  }

  try {
    const result = await bulkOnboardingManager.validateImportData({ initiativeId, filePath: file.path });
    res.FromModel(result);
  } catch (e) {
    next(e);
  }
});

router.route('/import/list').get(async (_, res, next) => {
  const initiativeId = res.locals.initiativeId as string;

  try {
    const result = await getOnboardingListService().getByInitiative(new ObjectId(initiativeId));
    res.FromModel(result);
  } catch (e) {
    next(e);
  }
});

module.exports = router;
