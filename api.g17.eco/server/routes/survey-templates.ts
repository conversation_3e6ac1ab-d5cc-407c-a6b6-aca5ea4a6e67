import express from 'express';
import { type AuthRouter } from '../http/AuthRouter';
import { ContextMiddleware } from '../middleware/audit/contextMiddleware';
import { canManageTemplate, hasValidTags } from '../middleware/surveyTemplateMiddlewares';
import { SurveyTemplateRepository } from '../repository/SurveyTemplateRepository';
import { getSurveyTemplateManager } from '../service/survey-template/SurveyTemplateManager';
import { getBluePrintContribution } from '../service/survey/BlueprintContribution';
import { ObjectId } from 'bson';
import { type BulkSurveysScopeData, SurveyType } from '../models/survey';
import { SurveyTemplateHistoryRepository } from '../repository/SurveyTemplateHistoryRepository';
import { getSurveyTemplateHistoryManager } from '../service/survey-template/SurveyTemplateHistoryManager';
import { SurveyTemplateHistory } from '../models/surveyTemplateHistory';
import { type SurveyTemplateModel } from '../models/surveyTemplate';
import { getTemplateManager } from '../service/survey-template/TemplateManager';
import { getAggregatedTemplateManager } from '../service/survey-template/AggregatedTemplateManager';
import { getTemplateFactory } from '../service/survey-template/TemplateFactory';
import { getSurveyTemplateJobManager } from '../service/survey-template/SurveyTemplateJobManager';
import { getAuditLogger } from '../service/audit/AuditLogger';
import { InitiativeAudit } from '../service/audit/events/Initiative';
import { wwgLogger } from '../service/wwgLogger';


const router = express.Router() as AuthRouter;
const templateManager = getTemplateManager();
const templateFactory = getTemplateFactory();
const surveyTemplateManager = getSurveyTemplateManager();
const aggregatedTemplateManager = getAggregatedTemplateManager();
const surveyTemplateHistoryManager = getSurveyTemplateHistoryManager();
const bc = getBluePrintContribution();

router.use(ContextMiddleware);

router.route('/')
  .post(async (req, res, next) => {
    try {
      // Permission check is done inside processTemplate->processCompany
      const template = await templateFactory.processTemplate(req.user, req.body);
      res.FromModel(template);
    } catch (e) {
      next(e);
    }
  });

router.route('/:templateId/duplicate')
  .post(canManageTemplate, async (req, res, next) => {
    try {
      const template = await templateManager.duplicateTemplate(req.user, req.params.templateId);
      res.FromModel(template);
    } catch (e) {
      next(e);
    }
  });

router.route('/:templateId/questions').get(canManageTemplate, async (_req, res) => {
  const { template } = res.locals;
  const utrs = await bc.getQuestionsWithCustomMetrics(template.sourceName, template.initiativeId);
  return res.FromModel(utrs);
});

router.route('/merge-tags')
  .get(async (req, res) => {
    const mergeOptions = templateManager.getMergeOptions();
    return res.FromModel(mergeOptions);
  });

router.route('/:templateId')
  .get(canManageTemplate, async (req, res) => {
    const template = await SurveyTemplateRepository.mustFindByIdWithRootCurrency(req.params.templateId);
    return res.FromModel(template);
  })
  .patch(canManageTemplate, async (req, res) => {
    const template = res.locals.template as SurveyTemplateModel<ObjectId>;
    if (template.type === SurveyType.Aggregation) {
      const update = await aggregatedTemplateManager.updateAggregatedTemplate(template, req.user, req.body);
      return res.FromModel(update);
    }
    const update = await surveyTemplateManager.updateTemplate(template, req.user, req.body);
    return res.FromModel(update);
  })
  .delete(canManageTemplate, async (req, res) => {
    const { template } = res.locals;
    await templateManager.disableTemplate(template, req.user);
    return res.Success(`Deleted template ${template._id} successfully`);
  });

router.route('/:templateId/scope')
  .patch(canManageTemplate, async (req, res) => {
    const { template } = res.locals;
    const update = await surveyTemplateManager.updateScope(template, req.user, req.body);
    return res.FromModel(update);
  });

router.route('/:templateId/bulk-surveys-create-job')
  .post(canManageTemplate, hasValidTags, async (req, res, next) => {
    getSurveyTemplateJobManager()
      .processJobRequest({
        template: res.locals.template as SurveyTemplateModel,
        data: req.body,
        user: req.user
      })
      .then(history => res.FromModel(history))
      .catch(e => next(e))
  });

router.route('/:templateId/history')
  .get(canManageTemplate, async (req, res) => {
    const history = await surveyTemplateHistoryManager.getHistoryWithInitiative(req.params.templateId);
    return res.FromModel(history);
  });

router.route('/:templateId/history/:historyId')
  .get(canManageTemplate, async (req, res) => {
    const history = await SurveyTemplateHistoryRepository.findHistoryWithInitiative(req.params.historyId);
    return res.FromModel(history);
  });

router.route('/:templateId/history/:historyId/refresh')
  .post(canManageTemplate, async (req, res) => {
    const history = await SurveyTemplateHistory.findOne({
      _id: req.params.historyId,
      templateId: req.params.templateId,
    })
      .orFail()
      .exec();
    const surveyIds = history.results.reduce((acc, current) => {
      if (!current.surveyId) {
        return acc;
      }
      return [...acc, current.surveyId];
    }, [] as ObjectId[]);

    const bulkSurveysScopeData: BulkSurveysScopeData = {
      templateId: new ObjectId(req.params.templateId),
      surveyIds,
      user: req.user,
    };

    const successSurveyIds = await templateManager.bulkSurveysScopeUpdate(bulkSurveysScopeData);

    // if there are surveys updated then update the lastUpdated in history
    if (successSurveyIds.length > 0) {
      history.lastUpdated = new Date();
      await history.save();
    }

    return res.FromModel(successSurveyIds);
  });

router.route('/:templateId/history/:historyId')
  .delete(canManageTemplate, async (req, res, next) => {
    try {
      const history = await SurveyTemplateHistory.findOne({
        _id: req.params.historyId,
        templateId: req.params.templateId,
      })
        .orFail()
        .exec();
      await surveyTemplateHistoryManager.bulkSurveysDelete({
        history,
        user: req.user,
      });

      const auditLogger = getAuditLogger();

      auditLogger
        .fromRequest(req, {
          initiativeId: history.initiativeId,
          auditEvent: InitiativeAudit.templateHistoryDeleted,
          targets: [
            {
              id: history._id.toString(),
              type: 'TemplateHistory',
            },
            {
              id: history.initiativeId.toString(),
              type: 'Initiative',
            },
          ],
        })
        .catch(wwgLogger.error);

      return res.FromModel({
        message: 'Successfully deleted surveys.',
      });
    } catch (e) {
      next(e);
    }
  });

module.exports = router;
