/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import { checkToken } from '../../middleware/jwt';
import { getSubscriptionService } from '../../service/subscription/SubscriptionService';
import { wwgLogger } from '../../service/wwgLogger';

const router = express.Router();
const subscriptionService = getSubscriptionService();

router.route('/unsubscribe/token/:token').get(checkToken, async (req, res) => {
  if (typeof req.tokenData !== 'object' || !req.tokenData.type) {
    const message = `Unsubscribe is missing type field, data type ${typeof req.tokenData}`;
    wwgLogger.info(message);
    return res.Invalid('Token is not valid');
  }

  const data = await subscriptionService.unsubscribe(req.tokenData);
  res.FromModel(data);
});

module.exports = router;
