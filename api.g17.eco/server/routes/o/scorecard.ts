/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import Initiative, { getMateriality, type InitiativePlain } from '../../models/initiative';
import ScorecardFactory from '../../service/scorecard/ScorecardFactory';
import express from 'express';
import Survey from '../../models/survey';
import cache from '../../service/cache';
import { type KeysEnum } from '../../models/public/projectionUtils';

const router = express.Router();

type ScorecardInitiative = Pick<InitiativePlain, '_id' | 'permissionGroup' | 'materiality'>;
type ExtraLookupProps = Pick<InitiativePlain, 'displaySettings' | 'industry' | 'materialityMap'>;
type ScorecardInitiativeLookups = ScorecardInitiative & ExtraLookupProps;

router.route('/:initiativeId/:token').get(cache.route(), async function (req, res) {
  const { initiativeId, token } = req.params;

  if (!token) {
    return res.NotPermitted();
  }

  const projection: KeysEnum<ScorecardInitiativeLookups> = {
    _id: 1,
    displaySettings: 1,
    // materiality is virtual and needs to be generated, like on getInitiativeById
    materiality: 1,
    permissionGroup: 1,
    industry: 1,
    materialityMap: 1,
  };
  const initiative = await Initiative.findById(initiativeId, projection).lean().exec();
  const sharingSettings = initiative?.displaySettings?.sdgContributionChart;

  if (!sharingSettings || !sharingSettings.liveSharing || sharingSettings.token !== token) {
    return res.NotPermitted();
  }

  const survey = await Survey.findOne(
    {
      initiativeId: initiative._id,
      deletedDate: { $exists: false },
      publishedDate: { $exists: true },
      // @TODO need to ensure clients are aware of the changes and have migrated surveys
      // until then we keep publishedDate to allow clients to migrate
      // $or: [
      //   { completedDate: { $exists: true } },
      //   { publishedDate: { $exists: true } }
      // ]
    },
    { _id: 1 }
  ) // Only need id do to further lookups
    .sort({ effectiveDate: -1 })
    .lean()
    .exec();

  if (!survey) {
    return res.Invalid('No survey found');
  }

  const scorecardFactory = new ScorecardFactory();
  const scorecard = await scorecardFactory.getBySurveyId(survey._id);

  const scorecardInitiative: ScorecardInitiative = {
    _id: initiative._id,
    // @TODO [INHERITANCE] Should populate industry recursively?
    // Virtual property, but only populated in getInitiativeById that is recursive, however
    // that would also recursively populate displaySettings as well,
    // and that is probably not be what we want. Instead creating here
    materiality: getMateriality(initiative.industry, initiative.materialityMap),
    permissionGroup: initiative.permissionGroup,
  };

  res.FromModel({ scorecard, initiative: scorecardInitiative });
});

module.exports = router;
