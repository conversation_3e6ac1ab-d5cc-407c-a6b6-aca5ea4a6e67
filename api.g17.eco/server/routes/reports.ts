/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import { ObjectId } from 'bson';
import { ReportGenerator } from '../service/scorecard/ReportGenerator';
import Initiative from '../models/initiative';

const router = express.Router();

const paramIdCheck = (params: string[]) => (req: any, res: any, next: any) => {
  try {
    for (const param of params) {
      if (req.params[param]) {
        req.params[param] = new ObjectId(req.params[param]);
      }
    }
  } catch (e) {
    return res.Invalid('Initiative id is not valid');
  }
  next();
};

router
  .route('/initiative/:initiativeId/scorecard{/:surveyId}')
  .get(paramIdCheck(['initiativeId']), async (req: any, res) => {
    const initiative = await Initiative.findById(req.params.initiativeId).orFail().exec();
    const { data, headers } = await ReportGenerator.generate(initiative, req.user, req.params.surveyId);
    ReportGenerator.sendResponse(res, {
      data,
      headers,
      name: initiative.name,
    });
  });

module.exports = router;
