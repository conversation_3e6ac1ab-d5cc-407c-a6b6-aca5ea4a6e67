/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import { ReportingFrameworkRepository } from '../repository/ReportingFrameworkRepository';
const router = express.Router();

router.route('/map')
  .get(async (req, res) => {
    const map = await ReportingFrameworkRepository.getSDGToFrameworkMap();
    res.FromModel(map);
  });

router.route('/map/adaptor')
  .get(async (req, res) => {
    const map = await ReportingFrameworkRepository.getSDGToFrameworkMapFromSurvey();
    res.FromModel(map);
  });

module.exports = router;
