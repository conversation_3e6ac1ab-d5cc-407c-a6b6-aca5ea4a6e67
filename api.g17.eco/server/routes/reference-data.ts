/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import ReferenceData from '../models/referenceData';
import { type AuthRouter } from '../http/AuthRouter';
import { InitiativePermissions } from '../service/initiative/InitiativePermissions';
const router = express.Router() as AuthRouter;

router.route('/initiativeId/:initiativeId').get(async (req, res) => {
  if (!(await InitiativePermissions.canAccess(req.user, req.params.initiativeId))) {
    return res.NotPermitted();
  }
  const model = await ReferenceData.find({ initiativeId: req.params.initiativeId }).exec();
  res.FromModel(model);
});

module.exports = router;
