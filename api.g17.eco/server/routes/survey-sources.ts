/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import express from 'express';
import { getBlueprintRepository } from '../repository/BlueprintRepository';
const router = express.Router();

const surveyRepo = getBlueprintRepository();

router.route('/')
  .get(async (req, res) => {
    const r = await surveyRepo.listAll();
    res.FromModel(r);
  });

router.route('/code/:code/expanded')
  .get(async (req, res) => {
    const r = await surveyRepo.getExpandedBlueprintByCode(req.params.code);
    res.FromModel(r);
  });

router.route('/code/:code')
  .get(async (req, res) => {
    const r = await surveyRepo.getBlueprint(req.params.code);
    res.FromModel(r);
  });

module.exports = router;
