import type { DoubleMaterialityAssessmentData } from '@apps/materiality-tracker/api/materiality-assessment';
import { Chart } from 'react-google-charts';
import '../../styles.scss';
import React, { useMemo, useCallback, useEffect } from 'react';
import { ChartWrapperOptions, type GoogleChartWrapper } from 'react-google-charts/dist/types';
import { type GenerateChartsProps } from './GenerateCharts';
import type { DataSource } from '@features/report-output/common';
import { REPORT_COLORS } from '../utils';

// --- Constants for Chart Synchronization ---
const CHART_WIDTH = 600;
const CHART_HEIGHT = 900;

const getBaseOptions = (dataLength: number): ChartWrapperOptions['options'] => ({
  legend: 'none',
  bar: { groupWidth: '80%' },
  chartArea: {
    height: '100%',
    width: '100%',
  },
  vAxis: {
    textPosition: 'none',
  },
  hAxis: {
    textPosition: 'none',
  },
  annotations: {
    textStyle: {
      fontSize: 12,
      fontName: 'Arial, sans-serif',
    },
    highContrast: true,
    alwaysOutside: dataLength < 25,
    stem: {
      length: 0,
    },
  },
});

const getChartData = ({ chartWrapper }: { chartWrapper: GoogleChartWrapper }) => {
  // getImageURI return string but type definition say it returns void
  const imageURI = chartWrapper.getChart().getImageURI() as unknown;
  return typeof imageURI === 'string' ? imageURI.split(',')[1] : '';
};

const processChartData = (data: DoubleMaterialityAssessmentData[]) => {
  return data.reduce(
    (acc, topic) => {
      const { name, financialRelativeScore, nonFinancialRelativeScore } = topic;
      acc.labels.push(name);
      acc.financialData.push([
        name,
        financialRelativeScore ?? NaN,
        financialRelativeScore ? `${financialRelativeScore.toFixed(2)}%` : '',
        `color: ${REPORT_COLORS.FINANCIAL_COLUMN}; opacity: 1`,
      ]);
      acc.impactData.push([
        name,
        nonFinancialRelativeScore ?? NaN,
        nonFinancialRelativeScore ? `${nonFinancialRelativeScore.toFixed(2)}%` : '',
        `color: ${REPORT_COLORS.NONFINANCIAL_COLUMN}; opacity: 1`,
      ]);
      return acc;
    },
    {
      labels: [],
      financialData: [['Topic', 'Financial', { role: 'annotation' }, { role: 'style' }]],
      impactData: [['Topic', 'Impact', { role: 'annotation' }, { role: 'style' }]],
    } as {
      labels: (string | undefined)[];
      financialData: (string | number | undefined | { [key: string]: any })[][];
      impactData: (string | number | undefined | { [key: string]: any })[][];
    },
  );
};

const reportData: DataSource = {
  loaded: true,
  width: CHART_WIDTH,
  height: CHART_HEIGHT,
};

export const BiDirectionalBarChart = React.memo(({ data, updateData }: Omit<GenerateChartsProps, 'sizeScope'>) => {
  const { labels, financialData, impactData } = useMemo(() => processChartData(data), [data]);

  const rowHeight = CHART_HEIGHT / data.length;

  const baseOptions = getBaseOptions(data.length);

  // Use ref to track chart state for side effects without triggering re-renders
  const chartStateRef = React.useRef({
    financial: { ready: false, chart: '' },
    impact: { ready: false, chart: '' },
    hasCreated: false,
  });

  // Ref needs manual reset when data changes
  useEffect(() => {
    chartStateRef.current = {
      financial: { ready: false, chart: '' },
      impact: { ready: false, chart: '' },
      hasCreated: false,
    };
  }, [data]);

  const createCompositeChart = useCallback(
    (financialChart: string, impactChart: string) => {
      return new Promise<string>((resolve) => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d')!;

        // Set canvas dimensions to match the full chart width and height
        canvas.width = CHART_WIDTH;
        canvas.height = CHART_HEIGHT + 60; // Extra height for headers and margins

        // Fill with white background
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        const financialImg = new Image();
        const impactImg = new Image();
        let loadedCharts = 0;

        const onImageLoad = () => {
          loadedCharts++;
          // Must wait for both images to load before combining them
          if (loadedCharts === 2) {
            // Draw headers
            ctx.fillStyle = '#000000';
            ctx.font = '16px sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText('Financial', CHART_WIDTH * 0.15, 25); // Center of 30% section
            ctx.fillText('Impact', CHART_WIDTH * 0.85, 25); // Center of 30% section

            // Calculate dimensions for each section
            const chartWidth = CHART_WIDTH * 0.30; // 30% for each chart
            const labelWidth = CHART_WIDTH * 0.40; // 40% for labels
            const headerOffset = 40;

            // Draw financial chart (left side, scaled to fit 30% width)
            ctx.drawImage(financialImg, 0, headerOffset, chartWidth, CHART_HEIGHT);

            // Draw impact chart (right side, scaled to fit 30% width)
            ctx.drawImage(impactImg, CHART_WIDTH * 0.70, headerOffset, chartWidth, CHART_HEIGHT);

            // Draw labels in the center
            ctx.fillStyle = '#333333';
            ctx.font = 'bold 12px sans-serif';
            ctx.textAlign = 'center';

            labels.forEach((label, index) => {
              if (!label) {
                return '';
              }

              const rowStartY = headerOffset + index * rowHeight;
              const rowCenterY = rowStartY + rowHeight / 2;
              const centerX = CHART_WIDTH * 0.30 + labelWidth / 2; // Center of 40% section

              // Truncate text if it's too long
              const maxTextWidth = labelWidth * 0.9;
              let displayText = label;

              const textMetrics = ctx.measureText(label);

              if (textMetrics.width > maxTextWidth) {
                // Truncate text and add ellipsis
                while (ctx.measureText(displayText + '...').width > maxTextWidth && displayText.length > 0) {
                  displayText = displayText.slice(0, -1);
                }
                displayText += '...';
              }

              // Draw the single line of text
              ctx.fillText(displayText, centerX, rowCenterY + 4);
            });

            // Convert to base64
            const compositeChartData = canvas.toDataURL('image/png').split(',')[1];
            resolve(compositeChartData);
          }
        };

        financialImg.onload = onImageLoad;
        impactImg.onload = onImageLoad;
        // Browser loads the image asynchronously
        financialImg.src = 'data:image/png;base64,' + financialChart;
        impactImg.src = 'data:image/png;base64,' + impactChart;
      });
    },
    [labels, rowHeight],
  );

  const handleChartReady = useCallback(
    async (type: 'financial' | 'impact', chartData: string) => {
      chartStateRef.current[type] = { ready: true, chart: chartData };

      // Check if both charts are ready and we haven't created composite chart yet
      const { financial, impact, hasCreated } = chartStateRef.current;
      if (financial.ready && impact.ready && !hasCreated) {
        chartStateRef.current.hasCreated = true;

        try {
          // Create composite chart with both charts and labels
          const compositeChart = await createCompositeChart(financial.chart, impact.chart);

          // Create composite report data
          const compositeReportData = {
            ...reportData,
            chart: compositeChart,
          };

          updateData('doubleMaterialityTopics', compositeReportData);
        } catch (error) {
          console.error('Failed to create composite chart:', error);
        }
      }
    },
    [createCompositeChart, updateData],
  );

  return (
    <div className='assessment__insights-chart' style={{
      padding: '20px 0',
      width: CHART_WIDTH,
      height: CHART_HEIGHT + 80,
      margin: '0 auto',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      overflow: 'visible'
    }}>
      <div className='d-flex text-ThemeHeadingMedium'>
        <div className='h4' style={{ width: '30%', textAlign: 'center' }}>
          Financial
        </div>
        <div style={{ width: '40%' }} />
        <div className='h4' style={{ width: '30%', textAlign: 'center' }}>
          Impact
        </div>
      </div>
      <div className='d-flex' style={{ gap: '0' }}>
        <div style={{ width: '30%' }}>
          <Chart
            chartType='BarChart'
            width={CHART_WIDTH * 0.30}
            height={CHART_HEIGHT}
            data={financialData}
            chartEvents={[
              {
                eventName: 'ready',
                callback: ({ chartWrapper }) => handleChartReady('financial', getChartData({ chartWrapper })),
              },
            ]}
            options={{
              ...baseOptions,
              // This is the key to making bars grow from right-to-left
              hAxis: {
                ...baseOptions.hAxis,
                direction: -1,
              },
            }}
          />
        </div>
        <div className='d-flex flex-column' style={{ width: '40%' }}>
          {labels.map((label, index) => (
            <div
              key={`label-${index}-${label}`}
              className='d-flex align-items-center justify-content-center px-2'
              style={{
                height: rowHeight,
                fontSize: '11px',
                fontWeight: '600',
                textAlign: 'center',
                lineHeight: '1.0',
                padding: '0 4px'
              }}
            >
              <span
                className='text-ThemeTextMedium text-truncate'
                style={{
                  maxWidth: '100%',
                  display: 'block',
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis'
                }}
                title={label}
              >
                {label}
              </span>
            </div>
          ))}
        </div>
        <div style={{ width: '30%' }}>
          <Chart
            chartType='BarChart'
            width={CHART_WIDTH * 0.30}
            height={CHART_HEIGHT}
            data={impactData}
            chartEvents={[
              {
                eventName: 'ready',
                callback: ({ chartWrapper }) => handleChartReady('impact', getChartData({ chartWrapper })),
              },
            ]}
            options={baseOptions}
          />
        </div>
      </div>
    </div>
  );
});
