import React from 'react';
import { <PERSON><PERSON>hartWrapper, ReactGoogleChartEvent } from 'react-google-charts/dist/types';
import {
  DoubleMaterialityAssessmentData,
  MaterialityAssessmentScope,
} from '@apps/materiality-tracker/api/materiality-assessment';
import { topicLengthMap } from '../../../../utils';
import { DataSource } from '@features/report-output/common';
import { BiDirectionalBarChart } from './BiDirectionalBarChart';

export interface DataSources {
  doubleMaterialityTopics: DataSource;
}

export interface GenerateChartsProps {
  updateData: (key: string, data: DataSource) => void;
  data: DoubleMaterialityAssessmentData[];
  sizeScope: MaterialityAssessmentScope;
}

export const GenerateCharts = React.memo(({ data, sizeScope, ...props }: GenerateChartsProps) => {
  const topTopicsData = data.slice(0, topicLengthMap[sizeScope]);
  return (
    <div style={{ height: 0, width: 0, overflow: 'hidden' }}>
      <BiDirectionalBarChart {...props} data={topTopicsData} />
    </div>
  );
});
