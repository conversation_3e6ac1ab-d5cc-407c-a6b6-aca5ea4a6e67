import { vi, Mock } from 'vitest';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { MetricGroupConfiguration } from './MetricGroupConfiguration';
import {
  useGetAssessmentSizeQuery,
  MaterialityAssessmentScope,
} from '@apps/materiality-tracker/api/materiality-assessment';
import { useRegenerateMetricGroup } from './hooks/useRegenerateMetricGroup';
import { createMTMetricGroup } from '@fixtures/metric-group-fixtures';

vi.mock(import('@apps/materiality-tracker/api/materiality-assessment'), async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    useGetAssessmentSizeQuery: vi.fn(),
  };
});

vi.mock('./hooks/useRegenerateMetricGroup', () => ({
  useRegenerateMetricGroup: vi.fn(),
}));

describe('MetricGroupConfiguration component', () => {
  const mockHandleRegenerate = vi.fn();

  const baseMetricGroup = createMTMetricGroup();

  beforeEach(() => {
    (useGetAssessmentSizeQuery as Mock).mockReturnValue({
      data: { sizeScope: MaterialityAssessmentScope.SME },
      isFetching: false,
    });
    (useRegenerateMetricGroup as Mock).mockReturnValue({
      handleRegenerate: mockHandleRegenerate,
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Content rendering', () => {
    it('renders Module scope title', () => {
      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      expect(screen.getByText('Module scope')).toBeInTheDocument();
      expect(
        screen.getByText(/Which segment of topics would you like to use to generate metrics for this custom module/),
      ).toBeInTheDocument();
      expect(screen.getByRole('combobox')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Cancel' })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Update assigned metrics' })).toBeInTheDocument();
    });
  });

  describe('Select options for different scopes', () => {
    it('generates correct options for SME scope', async () => {
      const user = userEvent.setup();
      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      const combobox = screen.getByRole('combobox');
      await user.click(combobox);

      // SME scope has max 20 topics, so options should be 5, 10, 15, 20
      expect(screen.getByText('Top 5 topics')).toBeInTheDocument();
      expect(screen.getByText('Top 10 topics')).toBeInTheDocument();
      expect(screen.getAllByText('Top 15 topics')).toHaveLength(2); // One in selected value, one in options
      expect(screen.getByText('Top 20 topics')).toBeInTheDocument();
    });

    it('generates correct options for Micro scope', async () => {
      (useGetAssessmentSizeQuery as Mock).mockReturnValue({
        data: { sizeScope: MaterialityAssessmentScope.Micro },
        isFetching: false,
      });

      const user = userEvent.setup();
      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      const combobox = screen.getByRole('combobox');
      await user.click(combobox);

      // Micro scope has max 15 topics, so options should be 5, 10, 15
      expect(screen.getByText('Top 5 topics')).toBeInTheDocument();
      expect(screen.getByText('Top 10 topics')).toBeInTheDocument();
      expect(screen.getAllByText('Top 15 topics')).toHaveLength(2); // One in selected value, one in options
      expect(screen.queryByText('Top 20 topics')).toBeInTheDocument();
      expect(screen.queryByText('Top 25 topics')).not.toBeInTheDocument();
    });

    it('generates correct options for Large scope', async () => {
      (useGetAssessmentSizeQuery as Mock).mockReturnValue({
        data: { sizeScope: MaterialityAssessmentScope.Large },
        isFetching: false,
      });

      const user = userEvent.setup();
      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      const combobox = screen.getByRole('combobox');
      await user.click(combobox);

      // Large scope has max 25 topics, so should include up to 25
      expect(screen.getByText('Top 25 topics')).toBeInTheDocument();
    });
  });

  describe('Button states', () => {
    it('disables Update button when no changes are made', () => {
      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      expect(screen.getByRole('button', { name: 'Update assigned metrics' })).toBeDisabled();
    });

    it('enables Update button when selection changes', async () => {
      const user = userEvent.setup();
      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      const combobox = screen.getByRole('combobox');
      await user.click(combobox);

      const option = screen.getByText('Top 10 topics');
      await user.click(option);

      expect(screen.getByRole('button', { name: 'Update assigned metrics' })).not.toBeDisabled();
    });
  });

  describe('User interactions', () => {
    it('calls handleRegenerate when Update button is clicked', async () => {
      const user = userEvent.setup();
      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      // Change selection to enable the button
      const combobox = screen.getByRole('combobox');
      await user.click(combobox);

      const option = screen.getByText('Top 10 topics');
      await user.click(option);

      const updateButton = screen.getByRole('button', { name: 'Update assigned metrics' });
      await user.click(updateButton);

      expect(mockHandleRegenerate).toHaveBeenCalledWith(baseMetricGroup, 10);
    });
  });
});
