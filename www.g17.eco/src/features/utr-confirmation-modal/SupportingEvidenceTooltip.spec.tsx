/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { describe, it, expect } from 'vitest';
import { setupSimple, screen, waitFor } from '@fixtures/utils';
import { SupportingEvidenceTooltip } from './SupportingEvidenceTooltip';

describe('SupportingEvidenceTooltip', () => {
  const renderComponent = (props = {}) => {
    return setupSimple(<SupportingEvidenceTooltip {...props} />);
  };

  describe('tooltip behavior', () => {
    it('shows default GDPR tooltip content on hover', async () => {
      const { user, container } = renderComponent();

      const icon = container.querySelector('.fa-circle-info');
      expect(icon).toBeInTheDocument();
      await user.hover(icon!);

      const tooltip = await screen.findByRole('tooltip', { hidden: true });
      expect(tooltip).toHaveTextContent('Providing supporting evidence to verify data enhances transparency');
      expect(tooltip).toHaveTextContent('Suggested evidence:');
      expect(tooltip).toHaveTextContent('financial statements');
      expect(tooltip).toHaveTextContent('annual reports');
      expect(tooltip).toHaveTextContent('meter readings');
      expect(tooltip).toHaveTextContent('contracts');
    });

    it('shows custom string tooltip content on hover', async () => {
      const customTooltip = 'This is a custom tooltip message';
      const { user, container } = renderComponent({ tooltip: customTooltip });

      const icon = container.querySelector('.fa-circle-info');
      expect(icon).toBeInTheDocument();
      await user.hover(icon!);

      const tooltip = await screen.findByRole('tooltip', { hidden: true });
      expect(tooltip).toHaveTextContent(customTooltip);
    });

    it('processes string tooltip with newlines', async () => {
      const customTooltip = 'Line 1\nLine 2\nLine 3';
      const { user, container } = renderComponent({ tooltip: customTooltip });

      const icon = container.querySelector('.fa-circle-info');
      expect(icon).toBeInTheDocument();
      await user.hover(icon!);

      const tooltip = await screen.findByRole('tooltip', { hidden: true });
      expect(tooltip).toHaveTextContent('Line 1');
      expect(tooltip).toHaveTextContent('Line 2');
      expect(tooltip).toHaveTextContent('Line 3');
    });

    it('shows custom JSX tooltip content on hover', async () => {
      const customTooltip = (
        <div>
          <strong>Custom JSX</strong>
          <p>With multiple elements</p>
        </div>
      );
      const { user, container } = renderComponent({ tooltip: customTooltip });

      const icon = container.querySelector('.fa-circle-info');
      expect(icon).toBeInTheDocument();
      await user.hover(icon!);

      const tooltip = await screen.findByRole('tooltip', { hidden: true });
      expect(tooltip).toHaveTextContent('Custom JSX');
      expect(tooltip).toHaveTextContent('With multiple elements');
    });

    it('hides tooltip when mouse leaves', async () => {
      const { user, container } = renderComponent();

      const icon = container.querySelector('.fa-circle-info');
      expect(icon).toBeInTheDocument();

      // Hover to show tooltip
      await user.hover(icon!);
      const tooltip = await screen.findByRole('tooltip', { hidden: true });
      expect(tooltip).toBeInTheDocument();

      // Move mouse away to hide tooltip
      await user.unhover(icon!);
      await waitFor(() => {
        expect(screen.queryByRole('tooltip', { hidden: true })).not.toBeInTheDocument();
      });
    });
  });

  describe('edge cases', () => {
    it('handles empty string tooltip - show default tooltip', async () => {
      const { user, container } = renderComponent({ tooltip: '' });

      const icon = container.querySelector('.fa-circle-info');
      expect(icon).toBeInTheDocument();
      await user.hover(icon!);

      const tooltip = await screen.findByRole('tooltip', { hidden: true });
      expect(tooltip).toHaveTextContent('Providing supporting evidence to verify data enhances transparency');
    });

    it('handles null tooltip - show default tooltip', async () => {
      const { user, container } = renderComponent({ tooltip: null });

      const icon = container.querySelector('.fa-circle-info');
      expect(icon).toBeInTheDocument();
      await user.hover(icon!);

      const tooltip = await screen.findByRole('tooltip', { hidden: true });
      expect(tooltip).toHaveTextContent('Providing supporting evidence to verify data enhances transparency');
    });

    it('handles undefined tooltip - show default tooltip', async () => {
      const { user, container } = renderComponent({ tooltip: undefined });

      const icon = container.querySelector('.fa-circle-info');
      expect(icon).toBeInTheDocument();
      await user.hover(icon!);

      const tooltip = await screen.findByRole('tooltip', { hidden: true });
      expect(tooltip).toHaveTextContent('Providing supporting evidence to verify data enhances transparency');
    });
  });
});
