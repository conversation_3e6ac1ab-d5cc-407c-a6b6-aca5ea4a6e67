import { render, screen } from '@testing-library/react';
import { AggregationConfigDropdown } from './AggregationConfigDropdown';
import { ValueAggregation } from '@g17eco/types/universalTracker';

import { vi } from 'vitest';

describe('AggregationConfigDropdown', () => {
  const mockOnChange = vi.fn();

  const mockChildrenOptions = [
    { label: 'Use default settings (what they are)', value: 'default' as const },
    { label: 'SUM: Add up all values', value: ValueAggregation.ValueSumAggregator },
    { label: 'IGNORE: Do not aggregate', value: ValueAggregation.EmptyAggregator },
  ];

  const mockCombinedOptions = [
    { label: 'Use default settings (what they are)', value: 'default' as const },
    { label: 'AVERAGE: Simple average', value: ValueAggregation.ValueAverageAggregator },
    { label: 'IGNORE: Do not aggregate', value: ValueAggregation.EmptyAggregator },
  ];

  beforeEach(() => {
    mockOnChange.mockClear();
  });

  it('renders children and combined mode dropdowns', () => {
    render(
      <AggregationConfigDropdown
        childrenOptions={mockChildrenOptions}
        combinedOptions={mockCombinedOptions}
        onChange={mockOnChange}
      />,
    );

    expect(screen.getByText('Children mode')).toBeInTheDocument();
    expect(screen.getByText('Combined mode')).toBeInTheDocument();
  });

  it('displays current values when aggregationConfig is provided', () => {
    const mockAggregationConfig = {
      modes: {
        children: { valueAggregation: ValueAggregation.ValueSumAggregator },
        combined: { valueAggregation: ValueAggregation.ValueAverageAggregator },
      },
    };

    render(
      <AggregationConfigDropdown
        childrenOptions={mockChildrenOptions}
        combinedOptions={mockCombinedOptions}
        aggregationConfig={mockAggregationConfig}
        onChange={mockOnChange}
      />,
    );

    // The component should render with the provided values
    expect(screen.getByText('Children mode')).toBeInTheDocument();
    expect(screen.getByText('Combined mode')).toBeInTheDocument();
  });
});
