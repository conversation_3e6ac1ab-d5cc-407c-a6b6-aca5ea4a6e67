import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ModalFooter, Label } from 'reactstrap';
import { Loader } from '@g17eco/atoms/loader';
import { BulkActionUtr } from '@g17eco/types/survey-question-list';
import { AggregationMode, UtrValueType } from '@g17eco/types/universalTracker';
import { AggregationConfigDropdown } from './AggregationConfigDropdown';
import { TableColumnConfiguration } from './TableColumnConfiguration';
import { getAggregationOptions } from './utils';
import {
  AggregationOverrideData,
  AggregationConfigOverride,
  TableOverride,
} from '@g17eco/types/initiativeUniversalTracker';

interface Props {
  isLoading: boolean;
  initialData: AggregationOverrideData;
  selectedQuestion: BulkActionUtr;
  handleUpdateAggregationOverride: (aggregationOverride: AggregationOverrideData) => void;
  handleCloseModal: () => void;
}

export const AggregationOverrideForm = (props: Props) => {
  const { isLoading, initialData, selectedQuestion, handleUpdateAggregationOverride, handleCloseModal } = props;
  const [overrideData, setOverrideData] = useState<AggregationOverrideData>(initialData);

  const hasAnythingChanged = JSON.stringify(initialData) !== JSON.stringify(overrideData);

  const onClickUpdate = () => {
    if (!hasAnythingChanged) {
      return;
    }
    handleUpdateAggregationOverride(overrideData);
  };

  const onChangeAggregationConfig = (aggregationConfig: AggregationConfigOverride) => {
    setOverrideData((prev) => ({ ...prev, aggregationConfig }));
  };

  const onChangeTableOverride = (table: TableOverride) => {
    setOverrideData((prev) => ({ ...prev, table }));
  };

  // Generate options based on the first question's valueType
  const valueType = selectedQuestion.valueType;
  const childrenOptions = valueType ? getAggregationOptions(valueType as UtrValueType, AggregationMode.Children) : [];
  const combinedOptions = valueType ? getAggregationOptions(valueType as UtrValueType, AggregationMode.Combined) : [];

  return (
    <>
      <ModalBody>
        {isLoading ? <Loader /> : null}

        <div className='mb-3'>
          <AggregationConfigDropdown
            childrenOptions={childrenOptions}
            combinedOptions={combinedOptions}
            aggregationConfig={overrideData.aggregationConfig}
            onChange={onChangeAggregationConfig}
          />
        </div>

        {valueType === UtrValueType.Table && (
          <div className='mt-4'>
            <Label style={{ fontWeight: 500 }}>Table Column Configuration</Label>
            <TableColumnConfiguration
              selectedQuestion={selectedQuestion}
              table={overrideData.table}
              onChangeTableOverride={onChangeTableOverride}
            />
          </div>
        )}
      </ModalBody>
      <ModalFooter>
        <Button color='transparent' onClick={handleCloseModal}>
          Cancel
        </Button>
        <Button color='primary' disabled={isLoading || !hasAnythingChanged} onClick={onClickUpdate}>
          Save
        </Button>
      </ModalFooter>
    </>
  );
};
