import { useState } from 'react';
import { Table, ColumnDef } from '@g17eco/molecules/table';
import { BulkActionUtr } from '@g17eco/types/survey-question-list';
import { ColumnSetting } from './ColumnSetting';
import {
  TableOverride,
  TableColumnOverride,
  ColumnValueAggregationOverride,
} from '@g17eco/types/initiativeUniversalTracker';
import IconButton from '@g17eco/molecules/button/IconButton';
import {
  BADGE_COLORS,
  BADGE_STYLES,
  getAggregationShortName,
  getAggregationTooltip,
  hasAnyAggregation,
  MODE_PREFIXES,
} from './utils';
import { Badge } from 'reactstrap';

interface Props {
  selectedQuestion: BulkActionUtr;
  table: TableOverride | undefined;
  onChangeTableOverride: (table: TableOverride) => void;
}

export const TableColumnConfiguration = ({ selectedQuestion, table: tableOverride, onChangeTableOverride }: Props) => {
  const [editingColumn, setEditingColumn] = useState<TableColumnOverride>();

  // Get table columns from the selected question
  const tableColumns = selectedQuestion.valueValidation?.table?.columns || [];

  // Transform table columns into display format
  const tableData: TableColumnOverride[] = tableColumns.map((column) => {
    const columnOverride = tableOverride?.columns?.find((col: any) => col.code === column.code);
    const columnAggregationConfig = columnOverride?.aggregationConfig;

    return {
      code: column.code,
      type: column.type,
      name: column.name,
      aggregationConfig: columnAggregationConfig,
    };
  });
  
  if (tableData.length === 0) {
    return (
      <div className='text-center py-3 text-muted'>
        <i className='fal fa-table fa-2x mb-2'></i>
        <div>No table columns found for this question.</div>
      </div>
    );
  }

  const handleCloseModal = () => {
    setEditingColumn(undefined);
  };

  const handleOverrideColumnAggregation = (params: {
    code: string;
    childrenAggregation: ColumnValueAggregationOverride;
    combinedAggregation: ColumnValueAggregationOverride;
  }) => {
    const { code, childrenAggregation, combinedAggregation } = params;

    // Create updated table with new column aggregation config
    const updatedTable = {
      columns:
        tableOverride?.columns?.map((col: TableColumnOverride) =>
          col.code === code
            ? {
                ...col,
                aggregationConfig: {
                  modes: {
                    children: { valueAggregation: childrenAggregation },
                    combined: { valueAggregation: combinedAggregation },
                  },
                },
              }
            : col,
        ) || [],
    };

    onChangeTableOverride(updatedTable);
    setEditingColumn(undefined);
  };

  const columns: ColumnDef<TableColumnOverride>[] = [
    {
      accessorKey: 'code',
      header: 'Code',
    },
    {
      accessorKey: 'type',
      header: 'Type',
    },
    {
      accessorKey: 'name',
      header: 'Name',
    },
    {
      accessorKey: 'aggregationConfig',
      header: () => 'Agg Config',
      cell: ({ row }) => {
        const column = row.original;

        if (!column.aggregationConfig || !hasAnyAggregation(column.aggregationConfig)) {
          return <span className='text-muted'>-</span>;
        }

        const childrenAggregation = column.aggregationConfig?.modes?.children?.valueAggregation;
        const combinedAggregation = column.aggregationConfig?.modes?.combined?.valueAggregation;

        return (
          <div className='d-flex flex-wrap gap-1'>
            {childrenAggregation && childrenAggregation !== 'default' ? (
              <Badge
                color={BADGE_COLORS.children}
                style={BADGE_STYLES.default}
                title={getAggregationTooltip(childrenAggregation, 'children')}
              >
                {MODE_PREFIXES.children}
                {getAggregationShortName(childrenAggregation)}
              </Badge>
            ) : null}
            {combinedAggregation && combinedAggregation !== 'default' ? (
              <Badge
                color={BADGE_COLORS.combined}
                style={BADGE_STYLES.default}
                title={getAggregationTooltip(combinedAggregation, 'combined')}
              >
                {MODE_PREFIXES.combined}
                {getAggregationShortName(combinedAggregation)}
              </Badge>
            ) : null}
          </div>
        );
      },
      enableSorting: false,
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => (
        <IconButton
          icon='fa-pencil'
          tooltip='Edit column aggregation'
          onClick={() => {
            setEditingColumn(row.original);
          }}
          color='primary'
          size='sm'
        />
      ),
      enableSorting: false,
      meta: {
        cellProps: {
          className: 'text-center',
          style: { width: '80px' },
        },
      },
    },
  ];

  return (
    <>
      {editingColumn && (
        <ColumnSetting
          column={editingColumn}
          onClose={handleCloseModal}
          overrideColumnAggregation={handleOverrideColumnAggregation}
        />
      )}
      <Table data={tableData} columns={columns} pageSize={tableData.length} responsive className='table-sm' />
    </>
  );
};
