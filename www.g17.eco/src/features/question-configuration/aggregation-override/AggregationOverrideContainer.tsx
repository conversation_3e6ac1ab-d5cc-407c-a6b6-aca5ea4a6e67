import {
  useGetInitiativeUniversalTrackersQuery,
  useOverrideAggregationMutation,
} from '@api/initiative-universal-trackers';
import { addSiteError } from '@g17eco/slices/siteAlertsSlice';
import {
  InitiativeUniversalTracker,
  AggregationOverrideData,
} from '@g17eco/types/initiativeUniversalTracker';
import { getInitialAggregationOverrideData } from './utils';
import { useMemo } from 'react';
import { useAppSelector } from '@reducers/index';
import { isStaff } from '@selectors/user';
import { AggregationOverrideForm } from './AggregationOverrideForm';
import { BulkActionUtr } from '@g17eco/types/survey-question-list';

interface Props {
  handleReload: (props?: { reloadSurvey?: boolean; closeModal?: boolean }) => Promise<void | undefined>;
  selectedQuestions: BulkActionUtr[];
  numericSelectedQuestions: BulkActionUtr[];
  initiativeId: string;
  handleCloseModal: () => void;
}

export const AggregationOverrideContainer = (props: Props) => {
  const { handleReload, selectedQuestions, initiativeId, handleCloseModal } = props;
  const isStaffUser = useAppSelector(isStaff);

  const { data: initiativeUtrs, isFetching } = useGetInitiativeUniversalTrackersQuery(initiativeId, {
    skip: !initiativeId || selectedQuestions.length === 0,
  });

  const initiativeUtrMap = useMemo(() => {
    return new Map<string, InitiativeUniversalTracker>(
      (initiativeUtrs ?? []).map((utr: InitiativeUniversalTracker) => [utr.universalTrackerId, utr]),
    );
  }, [initiativeUtrs]);

  const [updateAggregationOverride, { isLoading: isUpdating }] = useOverrideAggregationMutation();

  // Process only single metric override
  const selectedUtr = selectedQuestions[0];
  const initialData = getInitialAggregationOverrideData({ initiativeUtrMap, utr: selectedUtr });

  const isLoading = isFetching || isUpdating;

  const handleUpdateAggregationOverride = ({  aggregationConfig, table }: AggregationOverrideData) => {
    // Prevent non-staff and multiple metrics update
    if (!isStaffUser || selectedQuestions.length !== 1) {
      return;
    }

    updateAggregationOverride({ aggregationConfig, valueValidation: { table }, initiativeId, utrId: selectedUtr._id })
      .unwrap()
      .then(() => {
        handleReload({ reloadSurvey: true, closeModal: true });
      })
      .catch((err) => {
        addSiteError(err);
        handleReload();
      });
  };

  return (
    <AggregationOverrideForm
      key={JSON.stringify({ initialData })}
      isLoading={isLoading}
      handleCloseModal={handleCloseModal}
      selectedQuestion={selectedUtr}
      initialData={initialData}
      handleUpdateAggregationOverride={handleUpdateAggregationOverride}
    />
  );
};
