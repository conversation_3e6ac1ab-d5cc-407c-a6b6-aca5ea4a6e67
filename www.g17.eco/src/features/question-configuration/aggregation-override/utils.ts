import {
  InitiativeUniversalTracker,
  ColumnValueAggregationOverride,
  ValueAggregationOverride,
  AggregationOverrideData,
  ColumnAggregationConfigOverride,
} from '@g17eco/types/initiativeUniversalTracker';
import {
  AggregationMode,
  UtrValueType,
  ColumnAggregationCompatibility,
  TableColumnType,
  ColumnValueAggregation,
} from '@g17eco/types/universalTracker';
import { aggregationLabels, columnAggregationTypes, getCompatibleAggregations } from '@utils/universalTracker';
import { Option } from '@g17eco/molecules/select/SelectFactory';
import { BulkActionUtr } from '@g17eco/types/survey-question-list';
import { CSSProperties } from 'react';

export const getInitialAggregationOverrideData = ({
  initiativeUtrMap,
  utr,
}: {
  initiativeUtrMap: Map<string, InitiativeUniversalTracker>;
  utr: BulkActionUtr;
}) => {
  const defaultConfig: AggregationOverrideData = {
    aggregationConfig: {
      modes: {
        children: {
          valueAggregation: 'default',
        },
        combined: {
          valueAggregation: 'default',
        },
      },
    },
    table: undefined,
  };

  if (!utr._id || initiativeUtrMap.size === 0) {
    return defaultConfig;
  }

  const existingOverrides = initiativeUtrMap.get(utr._id);
  const result: AggregationOverrideData = {
    aggregationConfig: {
      modes: {
        children: {
          valueAggregation: existingOverrides?.aggregationConfig?.modes?.children?.valueAggregation || 'default',
        },
        combined: {
          valueAggregation: existingOverrides?.aggregationConfig?.modes?.combined?.valueAggregation || 'default',
        },
      },
    },
    table: undefined,
  };

  if (utr.valueType === UtrValueType.Table) {
    const existingColumnsOverride = existingOverrides?.valueValidation?.table?.columns || [];
    const utrColumns = utr.valueValidation?.table?.columns || [];

    result.table = {
      columns: utrColumns.map((column) => {
        const existingColumn = existingColumnsOverride.find((ec) => ec.code === column.code);
        const config = existingColumn?.aggregationConfig;
        return (
          existingColumn || {
            code: column.code,
            name: column.name,
            type: column.type,
            aggregationConfig: {
              modes: {
                children: { valueAggregation: config?.modes?.children?.valueAggregation || 'default' },
                combined: { valueAggregation: config?.modes?.combined?.valueAggregation || 'default' },
              },
            },
          }
        );
      }),
    };
  }

  return result;
};

export const getAggregationOptions = (valueType: UtrValueType, mode: AggregationMode) => {
  const compatibleAggregations = getCompatibleAggregations(valueType, mode);

  // Always include the "default" option first
  const options: Option<ValueAggregationOverride>[] = [
    {
      label: aggregationLabels['default'],
      value: 'default',
    },
  ];

  // Add compatible aggregation options
  if (compatibleAggregations.length > 0) {
    const aggregationOptions = compatibleAggregations.map((option) => ({
      label: aggregationLabels[option] ?? option,
      value: option,
    }));
    options.push(...aggregationOptions);
  }

  return options;
};

// Get compatible aggregation options for this column type
export const getColumnAggregationOptions = (columnType: TableColumnType): Option<ColumnValueAggregationOverride>[] => {
  const compatibility = ColumnAggregationCompatibility[columnType];

  if (!compatibility) {
    return [{ label: 'Use default settings', value: 'default' }];
  }

  const options: Option<ColumnValueAggregationOverride>[] = [{ label: 'Use default settings', value: 'default' }];

  // Add compatible aggregations
  compatibility.compatible.forEach((aggregation) => {
    if (aggregation !== compatibility.default) {
      options.push({
        label: columnAggregationTypes[aggregation],
        value: aggregation,
      });
    }
  });

  return options;
};

/**
 * Badge styling constants
 */
export const BADGE_STYLES = {
  default: {
    fontSize: '0.55rem',
    fontWeight: '500',
    padding: '0.15rem 0.3rem',
  } as CSSProperties,
};

/**
 * Mode display prefixes
 */
export const MODE_PREFIXES = {
  children: 'Ch:',
  combined: 'Co:',
} as const;

/**
 * Badge color mappings
 */
export const BADGE_COLORS = {
  children: 'info',
  combined: 'success',
  default: 'secondary',
  warning: 'warning',
} as const;

export const hasAnyAggregation = (aggregationConfig: ColumnAggregationConfigOverride): boolean => {
  const configuredModes = Object.keys(aggregationConfig?.modes || {});
  return configuredModes.length > 0;
};

export const isWeightedAverageAggregation = (aggregation?: ColumnValueAggregation | string): boolean => {
  return aggregation === ColumnValueAggregation.ColumnWeightedAverageAggregator;
};

export const getAggregationTooltip = (
  aggregation: ColumnValueAggregation | string,
  mode?: 'children' | 'combined' | 'default',
): string => {
  const prefix =
    mode === 'children' ? 'Children mode: ' : mode === 'combined' ? 'Combined mode: ' : 'Default aggregation: ';

  if (mode === 'default' && isWeightedAverageAggregation(aggregation)) {
    return 'Warning: Weighted average without mode-specific weight formula';
  }

  return prefix + aggregation;
};

export const getAggregationShortName = (aggregation: ColumnValueAggregation | string): string => {
  switch (aggregation) {
    case ColumnValueAggregation.ColumnSumAggregator:
      return 'SUM';
    case ColumnValueAggregation.ColumnAverageAggregator:
      return 'AVG';
    case ColumnValueAggregation.ColumnWeightedAverageAggregator:
      return 'W.AVG';
    case ColumnValueAggregation.ColumnMaxAggregator:
      return 'MAX';
    case ColumnValueAggregation.ColumnPostAggregationCalculation:
      return 'CALC';
    case ColumnValueAggregation.ColumnLatestAggregator:
      return 'LATEST';
    case ColumnValueAggregation.ColumnEmptyAggregator:
      return 'IGNORE';
    default:
      return 'UNK';
  }
};
