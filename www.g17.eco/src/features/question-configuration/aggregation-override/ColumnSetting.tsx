import React, { useState } from 'react';
import { <PERSON>ton, Col, Label, Row } from 'reactstrap';
import { SelectFactory, SelectTypes } from '@g17eco/molecules/select/SelectFactory';
import { ColumnValueAggregationOverride, TableColumnOverride } from '@g17eco/types/initiativeUniversalTracker';
import { getColumnAggregationOptions } from './utils';

interface Props {
  column: TableColumnOverride;
  onClose: () => void;
  overrideColumnAggregation: (params: {
    code: string;
    childrenAggregation: ColumnValueAggregationOverride;
    combinedAggregation: ColumnValueAggregationOverride;
  }) => void;
}

export const ColumnSetting = ({ column, onClose, overrideColumnAggregation }: Props) => {
  // Get current aggregation settings for this column
  const originalAggregation = {
    children: column.aggregationConfig?.modes?.children?.valueAggregation || 'default',
    combined: column.aggregationConfig?.modes?.combined?.valueAggregation || 'default',
  };

  const [columnAggregationOverride, setColumnAggregationOverride] = useState({
    children: originalAggregation.children,
    combined: originalAggregation.combined,
  });

  const aggregationOptions = getColumnAggregationOptions(column.type);

  const handleSave = () => {
    overrideColumnAggregation({
      code: column.code,
      childrenAggregation: columnAggregationOverride.children,
      combinedAggregation: columnAggregationOverride.combined,
    });
  };

  const handleCancel = () => {
    // Reset to original values
    setColumnAggregationOverride(originalAggregation);
    onClose();
  };

  return (
    <div className='mb-3 p-3 bg-light rounded'>
      <div className='mb-3'>
        <p className='text-muted mb-0 small'>
          <i className='fa fa-info-circle me-1' />
          Configure mode-specific aggregation overrides. This allows different aggregation behavior for{' '}
          <strong>children mode</strong> (tree aggregations) vs <strong>combined mode</strong> (report aggregations).
        </p>
      </div>
      <div className='mb-2'>
        <Label className='text-muted small'>
          Column Code: <code>{column.code}</code> | Type: {column.type}
        </Label>
      </div>
      <Row>
        <Col cols={6} className='mb-3'>
          <Label style={{ fontWeight: 500 }} className='mb-0'>
            Children Mode
          </Label>
          <div className='text-muted small mb-1'>Used for tree aggregations up to parent entities</div>
          <SelectFactory
            selectType={SelectTypes.SingleSelect}
            options={aggregationOptions}
            value={aggregationOptions.find((opt: any) => opt.value === columnAggregationOverride.children) || null}
            onChange={(option: any) =>
              setColumnAggregationOverride((prev) => ({ ...prev, children: option?.value || 'default' }))
            }
            isMenuPortalTargetBody
            menuPlacement='auto'
          />
        </Col>
        <Col cols={6} className='mb-3'>
          <Label style={{ fontWeight: 500 }} className='mb-0'>
            Combined Mode
          </Label>
          <div className='text-muted small mb-1'>Used for combined reports (e.g., monthly to yearly)</div>
          <SelectFactory
            selectType={SelectTypes.SingleSelect}
            options={aggregationOptions}
            value={aggregationOptions.find((opt: any) => opt.value === columnAggregationOverride.combined) || null}
            onChange={(option: any) =>
              setColumnAggregationOverride((prev) => ({ ...prev, combined: option?.value || 'default' }))
            }
            isMenuPortalTargetBody
            menuPlacement='auto'
          />
        </Col>
      </Row>
      <div>
        <Button color='primary' onClick={handleSave}>
          Save Changes
        </Button>
        <Button color='transparent' onClick={handleCancel}>
          Cancel
        </Button>
      </div>
    </div>
  );
};
