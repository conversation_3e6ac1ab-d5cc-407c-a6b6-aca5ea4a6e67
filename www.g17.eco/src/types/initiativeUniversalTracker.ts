import { UtrvConfigType } from '../features/question-configuration/types';
import { BaseAggregationConfig, ColumnValueAggregation, TableColumnType, UniversalTrackerPlain , UtrAggregationConfig, ValueAggregation } from './universalTracker';

export type ValueAggregationOverride = ValueAggregation | 'default';
export type ColumnValueAggregationOverride = ColumnValueAggregation | 'default';

export type AggregationConfigOverride = BaseAggregationConfig<ValueAggregationOverride>;
export type ColumnAggregationConfigOverride = BaseAggregationConfig<ColumnValueAggregationOverride>;
export type TableColumnOverride = {
  code: string;
  name: string;
  type: TableColumnType;
  aggregationConfig?: ColumnAggregationConfigOverride;
}
export type TableOverride = {
  columns: TableColumnOverride[];
}

export interface AggregationOverrideData {
  aggregationConfig: AggregationConfigOverride | undefined;
  table: TableOverride | undefined;
}

export interface InitiativeUniversalTracker
  extends Pick<UniversalTrackerPlain, 'unitInput' | 'numberScaleInput' | 'valueValidation' | 'unitLocked' | 'numberScaleLocked'> {
  _id: string;
  initiativeId: string;
  universalTrackerId: string;
  utrvConfig?: UtrvConfigType;
  aggregationConfig?: UtrAggregationConfig;
}
